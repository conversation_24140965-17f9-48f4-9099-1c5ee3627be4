package mh.cloud.module.business.service.bususerexts.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import mh.cloud.module.business.dal.dataobject.AUserDO;
import mh.cloud.module.business.dal.dataobject.AUserExtsDO;
import mh.cloud.module.business.dal.mysql.AUserExtsMapper;
import mh.cloud.module.business.service.bususer.AUserService;
import mh.cloud.module.business.service.bususerexts.AUserExtsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.core.io.FileSystemResource;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
public class AUserExtsServiceImpl extends ServiceImpl<AUserExtsMapper, AUserExtsDO> implements AUserExtsService {
    private static final String OUTPUT_DIR = "/tempImg";
    private static final String UPLOAD_URL = "http://10.10.1.170:8080/FileManager/WebUploader/UploadFile";

    // 专用于处理过程日志的Logger，输出到独立文件
    private static final Logger processLogger = LoggerFactory.getLogger("mh.cloud.module.business.service.bususerexts.impl.AUserExtsServiceImpl.PROCESS");

    private String uploadToken;
    // 统计信息
    @Resource
    private AUserService aUserService;

    // 用户处理状态跟踪
    private static class UserProcessStatus {
        String userId;
        long startTime;
        long endTime;
        boolean signImgProcessed = false;
        boolean signImgSuccess = false;
        boolean headPortraitProcessed = false;
        boolean headPortraitSuccess = false;
        String signImgError;
        String headPortraitError;

        UserProcessStatus(String userId) {
            this.userId = userId;
            this.startTime = System.currentTimeMillis();
        }

        void finish() {
            this.endTime = System.currentTimeMillis();
        }

        long getProcessingTime() {
            return endTime - startTime;
        }
    }

    /**
     * 将SignImg和HeadPortrait字段的数据转换为图片文件保存到/tempImg文件夹下，并上传至指定URL。
     * 以A_User表的ID字段为基准进行查询，处理完成后删除A_UserExts中与A_User表对应不上的数据。
     *
     * @return 返回操作统计信息
     */
    @Override
    public Map<String, Object> processAndUploadImages() {
        processLogger.info("=== 开始执行图片处理和上传任务 ===");
        long startTime = System.currentTimeMillis();

        int successSaveFiles = 0;
        int failedSaveFiles = 0;
        int successUploadFiles = 0;
        int failedUploadFiles = 0;
        int successUpdateRecords = 0;
        int failedUpdateRecords = 0;
        int deletedOrphanedRecords = 0; // 新增：删除的无效记录数

        // 用户处理状态跟踪
        Map<String, UserProcessStatus> userStatusMap = new ConcurrentHashMap<>();

        File outputDir = new File(OUTPUT_DIR);
        if (!outputDir.exists()) {
            outputDir.mkdirs();
            processLogger.info("创建输出目录: {}", OUTPUT_DIR);
        }

        // 第一阶段：处理有效用户的图片数据
        processLogger.info("=== 第一阶段：开始处理有效用户的图片数据 ===");

        int currentPage = 1;
        int pageSize = 200;
        boolean hasMore = true;
        int totalProcessedRecords = 0;
        int totalProcessedPages = 0;

        // 先获取A_User表中有效用户的总数
        long totalValidUsers = baseMapper.countValidUsers();
        processLogger.info("A_User表中有效用户总数: {}", totalValidUsers);

        while (hasMore) {
            processLogger.info("开始处理第{}页数据，每页{}条记录", currentPage, pageSize);

            // 分页获取A_User表中的有效用户ID
            int offset = (currentPage - 1) * pageSize;
            List<String> validUserIds = baseMapper.selectValidUserIds(offset, pageSize);

            if (validUserIds.isEmpty()) {
                processLogger.info("第{}页没有查询到有效用户ID，处理完成", currentPage);
                hasMore = false;
                continue;
            }

            processLogger.info("第{}页查询到{}个有效用户ID", currentPage, validUserIds.size());

            // 基于这些有效用户ID查询A_UserExts表中需要处理的记录
            LambdaQueryWrapper<AUserExtsDO> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(AUserExtsDO::getUserID, AUserExtsDO::getSignImg, AUserExtsDO::getHeadPortrait)
                   .in(AUserExtsDO::getUserID, validUserIds)
                   .and(w -> w.isNotNull(AUserExtsDO::getSignImg).or().isNotNull(AUserExtsDO::getHeadPortrait));

            List<AUserExtsDO> records = this.list(wrapper);

            if (records.isEmpty()) {
                processLogger.info("第{}页的有效用户中没有需要处理的图片数据", currentPage);
            } else {
                totalProcessedPages++;
                processLogger.info("第{}页找到{}条需要处理的图片记录，开始处理", currentPage, records.size());
                totalProcessedRecords += records.size();

                for (AUserExtsDO userExt : records) {
                    // 创建用户处理状态跟踪
                    UserProcessStatus userStatus = new UserProcessStatus(userExt.getUserID());
                    userStatusMap.put(userExt.getUserID(), userStatus);

                    processLogger.info("用户ID: {} - 开始处理图片数据，处理开始时间: {}",
                        userExt.getUserID(), new Date(userStatus.startTime));

                    // 处理签名图片
                    if (userExt.getSignImg() != null && userExt.getSignImg().length > 0) {
                        userStatus.signImgProcessed = true;
                        String filePath = Paths.get(OUTPUT_DIR, userExt.getUserID() + "_Sign.png").toString();
                        String fileName = userExt.getUserID() + "_Sign.png";
                        processLogger.info("用户ID: {} - 开始处理签名图片，文件名: {}, 数据大小: {} 字节",
                            userExt.getUserID(), fileName, userExt.getSignImg().length);

                        try {
                            writeBytesToFile(userExt.getSignImg(), filePath);
                            successSaveFiles++;
                            processLogger.info("用户ID: {} - 签名图片文件保存成功，文件路径: {}", userExt.getUserID(), filePath);

                            String uploadedName = uploadFile(filePath, userExt.getUserID(), "sign");
                            if (uploadedName != null) {
                                successUploadFiles++;
                                processLogger.info("用户ID: {} - 签名图片上传成功，上传后文件名: {}", userExt.getUserID(), uploadedName);

                                updateSignImgFileName(userExt.getUserID(), uploadedName);
                                successUpdateRecords++;
                                userStatus.signImgSuccess = true;
                                processLogger.info("用户ID: {} - 签名图片处理完全成功（保存->上传->更新数据库）", userExt.getUserID());
                            } else {
                                failedUploadFiles++;
                                userStatus.signImgError = "上传失败";
                                processLogger.error("用户ID: {} - 签名图片上传失败，文件名: {}", userExt.getUserID(), fileName);
                            }
                        } catch (Exception e) {
                            failedSaveFiles++;
                            userStatus.signImgError = "文件保存失败: " + e.getMessage();
                            processLogger.error("用户ID: {} - 签名图片处理失败，文件名: {}, 错误信息: {}",
                                userExt.getUserID(), fileName, e.getMessage());
                            // 继续处理头像，不跳过
                        }
                    } else {
                        processLogger.info("用户ID: {} - 无签名图片数据，跳过签名图片处理", userExt.getUserID());
                    }

                    // 处理头像（独立处理，不与签名图片耦合）
                    if (userExt.getHeadPortrait() != null && userExt.getHeadPortrait().length > 0) {
                        userStatus.headPortraitProcessed = true;
                        String filePath = Paths.get(OUTPUT_DIR, userExt.getUserID() + "_Head.png").toString();
                        String fileName = userExt.getUserID() + "_Head.png";
                        processLogger.info("用户ID: {} - 开始处理头像图片，文件名: {}, 数据大小: {} 字节",
                            userExt.getUserID(), fileName, userExt.getHeadPortrait().length);

                        try {
                            writeBytesToFile(userExt.getHeadPortrait(), filePath);
                            successSaveFiles++;
                            processLogger.info("用户ID: {} - 头像图片文件保存成功，文件路径: {}", userExt.getUserID(), filePath);

                            String uploadedName = uploadFile(filePath, userExt.getUserID(), "head");
                            if (uploadedName != null) {
                                successUploadFiles++;
                                processLogger.info("用户ID: {} - 头像图片上传成功，上传后文件名: {}", userExt.getUserID(), uploadedName);

                                updateHeadPortraitFileName(userExt.getUserID(), uploadedName);
                                successUpdateRecords++;
                                userStatus.headPortraitSuccess = true;
                                processLogger.info("用户ID: {} - 头像图片处理完全成功（保存->上传->更新数据库）", userExt.getUserID());
                            } else {
                                failedUploadFiles++;
                                userStatus.headPortraitError = "上传失败";
                                processLogger.error("用户ID: {} - 头像图片上传失败，文件名: {}", userExt.getUserID(), fileName);
                            }
                        } catch (Exception e) {
                            failedSaveFiles++;
                            userStatus.headPortraitError = "文件保存失败: " + e.getMessage();
                            processLogger.error("用户ID: {} - 头像图片处理失败，文件名: {}, 错误信息: {}",
                                userExt.getUserID(), fileName, e.getMessage());
                        }
                    } else {
                        processLogger.info("用户ID: {} - 无头像图片数据，跳过头像图片处理", userExt.getUserID());
                    }

                    // 完成用户处理状态记录
                    userStatus.finish();
                    processLogger.info("用户ID: {} - 图片处理完成，总耗时: {} 毫秒，签名图片: {}，头像图片: {}",
                        userExt.getUserID(),
                        userStatus.getProcessingTime(),
                        userStatus.signImgProcessed ? (userStatus.signImgSuccess ? "成功" : "失败(" + userStatus.signImgError + ")") : "未处理",
                        userStatus.headPortraitProcessed ? (userStatus.headPortraitSuccess ? "成功" : "失败(" + userStatus.headPortraitError + ")") : "未处理");
                }
            }

            processLogger.info("第{}页处理完成，继续处理下一页", currentPage);
            currentPage++; // 增加页码
        }

        processLogger.info("=== 第一阶段完成：有效用户图片数据处理完毕 ===");

        // 第二阶段：清理无效记录
        processLogger.info("=== 第二阶段：开始清理A_UserExts中的无效记录 ===");

        try {
            // 先查询要删除的无效记录，记录详细日志
            List<AUserExtsDO> orphanedRecords = baseMapper.selectOrphanedRecords();
            processLogger.info("发现{}条A_UserExts中UserID在A_User表中不存在的无效记录", orphanedRecords.size());

            if (!orphanedRecords.isEmpty()) {
                // 记录每条要删除的记录详情
                for (AUserExtsDO record : orphanedRecords) {
                    processLogger.info("准备删除无效记录 - ID: {}, UserID: {}, 签名图片: {}, 头像图片: {}",
                        record.getId(),
                        record.getUserID(),
                        record.getSignImg() != null ? "有数据(" + record.getSignImg().length + "字节)" : "无数据",
                        record.getHeadPortrait() != null ? "有数据(" + record.getHeadPortrait().length + "字节)" : "无数据");
                }

                // 执行删除操作
                deletedOrphanedRecords = baseMapper.deleteOrphanedRecords();
                processLogger.info("成功删除{}条无效记录", deletedOrphanedRecords);
            } else {
                processLogger.info("没有发现需要删除的无效记录");
            }
        } catch (Exception e) {
            processLogger.error("清理无效记录时发生异常: {}", e.getMessage());
        }

        processLogger.info("=== 第二阶段完成：无效记录清理完毕 ===");

        // 第三阶段：清理临时文件夹
        processLogger.info("=== 第三阶段：开始清理临时文件夹 ===");
        cleanupTempDirectory();
        processLogger.info("=== 第三阶段完成：临时文件夹清理完毕 ===");

        // 计算执行时间
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // 输出每个用户的详细处理结果
        processLogger.info("=== 用户处理详细结果统计 ===");
        int totalProcessedUsers = userStatusMap.size();
        int successfulUsers = 0;
        int partialSuccessUsers = 0;
        int failedUsers = 0;

        for (UserProcessStatus status : userStatusMap.values()) {
            boolean signSuccess = !status.signImgProcessed || status.signImgSuccess;
            boolean headSuccess = !status.headPortraitProcessed || status.headPortraitSuccess;

            if (signSuccess && headSuccess) {
                successfulUsers++;
            } else if (status.signImgSuccess || status.headPortraitSuccess) {
                partialSuccessUsers++;
            } else {
                failedUsers++;
            }

            processLogger.info("用户ID: {} - 处理时间: {}ms, 签名图片: {}, 头像图片: {}",
                status.userId,
                status.getProcessingTime(),
                status.signImgProcessed ? (status.signImgSuccess ? "成功" : "失败(" + status.signImgError + ")") : "未处理",
                status.headPortraitProcessed ? (status.headPortraitSuccess ? "成功" : "失败(" + status.headPortraitError + ")") : "未处理");
        }

        // 详细统计日志记录到专用文件
        processLogger.info("=== 图片处理和上传任务执行完成 ===");
        processLogger.info("任务执行时间: {} 毫秒 ({} 秒)", executionTime, executionTime / 1000.0);
        processLogger.info("A_User表有效用户总数: {}", totalValidUsers);
        processLogger.info("总共处理页数: {}", totalProcessedPages);
        processLogger.info("总共处理记录数: {}", totalProcessedRecords);
        processLogger.info("实际处理用户数: {}", totalProcessedUsers);
        processLogger.info("用户处理结果 - 完全成功: {}, 部分成功: {}, 完全失败: {}", successfulUsers, partialSuccessUsers, failedUsers);
        processLogger.info("平均每页记录数: {}", totalProcessedPages > 0 ? (double) totalProcessedRecords / totalProcessedPages : 0);
        processLogger.info("文件保存统计 - 成功: {}, 失败: {}, 成功率: {}%",
            successSaveFiles, failedSaveFiles,
            (successSaveFiles + failedSaveFiles) > 0 ? String.format("%.2f", (double) successSaveFiles / (successSaveFiles + failedSaveFiles) * 100) : "0.00");
        processLogger.info("文件上传统计 - 成功: {}, 失败: {}, 成功率: {}%",
            successUploadFiles, failedUploadFiles,
            (successUploadFiles + failedUploadFiles) > 0 ? String.format("%.2f", (double) successUploadFiles / (successUploadFiles + failedUploadFiles) * 100) : "0.00");
        processLogger.info("记录更新统计 - 成功: {}, 失败: {}, 成功率: {}%",
            successUpdateRecords, failedUpdateRecords,
            (successUpdateRecords + failedUpdateRecords) > 0 ? String.format("%.2f", (double) successUpdateRecords / (successUpdateRecords + failedUpdateRecords) * 100) : "0.00");
        processLogger.info("无效记录清理统计 - 删除记录数: {}", deletedOrphanedRecords);
        processLogger.info("=== 任务统计完成 ===");

        // 返回统计结果
        Map<String, Object> stats = new HashMap<>();
        stats.put("successSaveFiles", successSaveFiles);
        stats.put("failedSaveFiles", failedSaveFiles);
        stats.put("successUploadFiles", successUploadFiles);
        stats.put("failedUploadFiles", failedUploadFiles);
        stats.put("successUpdateRecords", successUpdateRecords);
        stats.put("failedUpdateRecords", failedUpdateRecords);
        stats.put("totalProcessedRecords", totalProcessedRecords);
        stats.put("totalProcessedPages", totalProcessedPages);
        stats.put("totalValidUsers", totalValidUsers); // 新增：有效用户总数
        stats.put("deletedOrphanedRecords", deletedOrphanedRecords); // 新增：删除的无效记录数
        stats.put("executionTimeMs", executionTime);
        stats.put("totalProcessedUsers", totalProcessedUsers); // 新增：实际处理用户数
        stats.put("successfulUsers", successfulUsers); // 新增：完全成功用户数
        stats.put("partialSuccessUsers", partialSuccessUsers); // 新增：部分成功用户数
        stats.put("failedUsers", failedUsers); // 新增：完全失败用户数

        return stats;
    }

    /**
     * 上传文件到指定URL。
     *
     * @param filePath 文件路径
     * @param userID   用户ID
     * @param type     文件类型（sign或head）
     * @return 上传结果中的Name字段
     */
    @Override
    public String uploadFile(String filePath, String userID, String type) {
        processLogger.info("用户ID: {} - 开始上传{}文件，文件路径: {}", userID, type, filePath);

        try {
            RestTemplate restTemplate = new RestTemplate();

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            headers.set("token", this.uploadToken);
            headers.setOrigin("http://localhost");
            headers.setAccessControlAllowOrigin("*");
            headers.set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
            // 设置表单数据
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            FileSystemResource resource = new FileSystemResource(new File(filePath));
            body.add("file", resource);
            // 添加额外参数
            body.add("RelateId", "_Nv200_3");
            HttpServletRequest request = getCurrentHttpRequest();
            body.add("Src", "http://localhost" + ":" + request.getServerPort() + "/");
            body.add("id", "WU_FILE_0");
            String fileNameWithoutExtension = Paths.get(filePath).getFileName().toString().replaceFirst("\\.[^\\.]+$", "");
            body.add("name", fileNameWithoutExtension);
            body.add("type", "image/png");
            body.add("lastModifiedDate", String.valueOf(new Date().getTime()));
            File file = new File(filePath);
            body.add("size", String.valueOf(file.length()));

            processLogger.info("用户ID: {} - 准备上传{}文件，文件大小: {} 字节，目标URL: {}",
                userID, type, file.length(), UPLOAD_URL);

            // 构建请求实体
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // 发送POST请求
            ResponseEntity<String> response = restTemplate.postForEntity(UPLOAD_URL, requestEntity, String.class);

            // 解析响应JSON并提取Name字段
            if (response.getStatusCode() == HttpStatus.OK) {
                String responseBody = response.getBody();
                processLogger.info("用户ID: {} - {}文件上传响应: {}", userID, type, responseBody);
                // 这里假设响应是类似{"Name":"filename.png"}的JSON字符串
                int nameStartIndex = responseBody.indexOf("\"Name\":\"") + 8; // "Name":"的长度是8，不是9
                int nameEndIndex = responseBody.indexOf("\"", nameStartIndex);
                if (nameStartIndex >= 8 && nameEndIndex > nameStartIndex) {
                    String fileName = responseBody.substring(nameStartIndex, nameEndIndex);
                    processLogger.info("用户ID: {} - {}文件上传成功，返回文件名: {}", userID, type, fileName);
                    return fileName;
                } else {
                    processLogger.error("用户ID: {} - 无法从{}文件上传响应中解析文件名，响应内容: {}", userID, type, responseBody);
                }
            } else {
                processLogger.error("用户ID: {} - {}文件上传失败，HTTP状态码: {}", userID, type, response.getStatusCode());
            }
            return null;
        } catch (Exception e) {
            processLogger.error("用户ID: {} - {}文件上传异常，文件路径: {}, 错误信息: {}", userID, type, filePath, e.getMessage());
            throw new RuntimeException("Failed to upload file: " + filePath, e);
        }
    }

    /**
     * 更新SignImgFile字段。
     *
     * @param userID   用户ID
     * @param fileName 文件名
     */
    @Override
    public void updateSignImgFileName(String userID, String fileName) {
        processLogger.info("用户ID: {} - 开始更新签名图片文件名: {}", userID, fileName);

        try {
            LambdaUpdateWrapper<AUserExtsDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(AUserExtsDO::getUserID, userID)
                    .set(AUserExtsDO::getSignImgFile, fileName)
                    .set(AUserExtsDO::getSignImg, null);
            boolean result = this.update(updateWrapper);
            processLogger.info("用户ID: {} - AUserExtsDO表签名图片文件名更新结果: {}", userID, result);

            LambdaUpdateWrapper<AUserDO> userWrapper = new LambdaUpdateWrapper<>();
            userWrapper.eq(AUserDO::getId, userID)
                    .set(AUserDO::getSignImgFile, fileName);
            boolean userResult = aUserService.update(userWrapper);
            processLogger.info("用户ID: {} - AUserDO表签名图片文件名更新结果: {}", userID, userResult);

            if (result && userResult) {
                processLogger.info("用户ID: {} - 签名图片文件名更新成功，已清空原始二进制数据", userID);
            } else {
                processLogger.error("用户ID: {} - 签名图片文件名更新失败，AUserExtsDO结果: {}, AUserDO结果: {}",
                    userID, result, userResult);
            }
        } catch (Exception e) {
            processLogger.error("用户ID: {} - 更新签名图片文件名时发生异常: {}", userID, e.getMessage());
            throw e;
        }
    }

    /**
     * 更新HeadPortraitFile字段。
     *
     * @param userID   用户ID
     * @param fileName 文件名
     */
    @Override
    public void updateHeadPortraitFileName(String userID, String fileName) {
        processLogger.info("用户ID: {} - 开始更新头像图片文件名: {}", userID, fileName);

        try {
            LambdaUpdateWrapper<AUserExtsDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(AUserExtsDO::getUserID, userID).set(AUserExtsDO::getHeadPortraitFile, fileName).set(AUserExtsDO::getHeadPortrait, null); // 修正：应该清空HeadPortrait而不是SignImg
            boolean result = this.update(updateWrapper);
            processLogger.info("用户ID: {} - AUserExtsDO表头像图片文件名更新结果: {}", userID, result);

            LambdaUpdateWrapper<AUserDO> userWrapper = new LambdaUpdateWrapper<>();
            userWrapper.eq(AUserDO::getId, userID)
                    .set(AUserDO::getHeadPortraitFile, fileName);
            boolean userResult = aUserService.update(userWrapper);
            processLogger.info("用户ID: {} - AUserDO表头像图片文件名更新结果: {}", userID, userResult);

            if (result && userResult) {
                processLogger.info("用户ID: {} - 头像图片文件名更新成功，已清空原始二进制数据", userID);
            } else {
                processLogger.error("用户ID: {} - 头像图片文件名更新失败，AUserExtsDO结果: {}, AUserDO结果: {}",
                    userID, result, userResult);
            }
        } catch (Exception e) {
            processLogger.error("用户ID: {} - 更新头像图片文件名时发生异常: {}", userID, e.getMessage());
            throw e;
        }
    }

    /**
     * 将字节数组写入指定路径的文件。
     *
     * @param data     字节数据
     * @param filePath 输出文件路径
     */
    @Override
    public void writeBytesToFile(byte[] data, String filePath) {
        processLogger.info("开始写入文件: {}, 数据大小: {} 字节", filePath, data.length);

        try (FileOutputStream fos = new FileOutputStream(filePath)) {
            fos.write(data);
            processLogger.info("文件写入成功: {}", filePath);
        } catch (IOException e) {
            processLogger.error("文件写入失败: {}, 错误信息: {}", filePath, e.getMessage());
            throw new RuntimeException("Failed to write file: " + filePath, e);
        }
    }

    @Override
    public String getUploadToken() {
        return this.uploadToken;
    }

    @Override
    public void setUploadToken(String token) {
        this.uploadToken = token;
    }

    // 获取当前HTTP请求
    private HttpServletRequest getCurrentHttpRequest() {
        return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    }

    /**
     * 清理临时文件夹
     * 删除整个/tempImg文件夹及其所有内容，防止文件或数据泄露
     */
    private void cleanupTempDirectory() {
        try {
            Path tempDirPath = Paths.get(OUTPUT_DIR);
            if (Files.exists(tempDirPath)) {
                processLogger.info("开始清理临时文件夹: {}", OUTPUT_DIR);

                // 统计要删除的文件数量
                long fileCount = Files.walk(tempDirPath)
                    .filter(Files::isRegularFile)
                    .count();

                processLogger.info("临时文件夹中共有 {} 个文件需要删除", fileCount);

                // 递归删除整个目录及其内容
                Files.walk(tempDirPath)
                    .sorted((path1, path2) -> path2.compareTo(path1)) // 先删除文件，再删除目录
                    .forEach(path -> {
                        try {
                            if (Files.isRegularFile(path)) {
                                processLogger.info("删除临时文件: {}", path.toString());
                            } else if (Files.isDirectory(path)) {
                                processLogger.info("删除临时目录: {}", path.toString());
                            }
                            Files.delete(path);
                        } catch (IOException e) {
                            processLogger.error("删除文件/目录失败: {}, 错误信息: {}", path.toString(), e.getMessage());
                        }
                    });

                processLogger.info("临时文件夹清理完成，已删除 {} 个文件", fileCount);
            } else {
                processLogger.info("临时文件夹不存在，无需清理: {}", OUTPUT_DIR);
            }
        } catch (Exception e) {
            processLogger.error("清理临时文件夹时发生异常: {}, 错误信息: {}", OUTPUT_DIR, e.getMessage());
            // 不抛出异常，避免影响主流程
        }
    }
}