<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>mh.cloud</groupId>
        <artifactId>microservices-module-system</artifactId>
        <version>${revision}</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>microservices-module-system-biz</artifactId>
    <!--打包方式-->
    <packaging>war</packaging>

    <name>${project.artifactId}</name>
    <description>
        system 模块下，我们放通用业务，支撑上层的核心业务。
        例如说：用户、部门、权限、数据字典等等
    </description>

    <dependencies>
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
            <version>8.4.1.jre8</version>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>com.jfinal</groupId>
            <artifactId>activerecord</artifactId>
            <version>5.1.2</version>
        </dependency>
        <!-- Spring Cloud 基础 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <dependency>
            <groupId>mh.cloud</groupId>
            <artifactId>microservices-spring-boot-starter-env</artifactId>
        </dependency>
        <!-- 依赖服务 -->
        <dependency>
            <groupId>mh.cloud</groupId>
            <artifactId>microservices-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <!-- 业务组件 -->
        <dependency>
            <groupId>mh.cloud</groupId>
            <artifactId>microservices-spring-boot-starter-biz-data-permission</artifactId>
        </dependency>
        <dependency>
            <groupId>mh.cloud</groupId>
            <artifactId>microservices-spring-boot-starter-biz-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>mh.cloud</groupId>
            <artifactId>microservices-spring-boot-starter-biz-ip</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>mh.cloud</groupId>
            <artifactId>microservices-spring-boot-starter-security</artifactId>
            <!--<exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
            </exclusions>-->
        </dependency>
        <!-- DB 相关 -->
        <dependency>
            <groupId>mh.cloud</groupId>
            <artifactId>microservices-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>mh.cloud</groupId>
            <artifactId>microservices-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>mh.cloud</groupId>
            <artifactId>microservices-spring-boot-starter-rpc</artifactId>
        </dependency>


        <!-- 消息队列相关 -->
        <dependency>
            <groupId>mh.cloud</groupId>
            <artifactId>microservices-spring-boot-starter-mq</artifactId>
        </dependency>

        <!-- 服务保障相关 TODO luohang：暂时去掉 -->
<!--        <dependency>-->
<!--            <groupId>mh.cloud</groupId>-->
<!--            <artifactId>microservices-spring-boot-starter-protection</artifactId>-->
<!--        </dependency>-->

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>mh.cloud</groupId>
            <artifactId>microservices-spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>mh.cloud</groupId>
            <artifactId>microservices-spring-boot-starter-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

        <!-- 监控相关 -->
        <dependency>
            <groupId>mh.cloud</groupId>
            <artifactId>microservices-spring-boot-starter-monitor</artifactId>
        </dependency>

        <!-- 三方云服务相关 -->
        <dependency>
            <groupId>com.xingyuv</groupId>
            <artifactId>spring-boot-starter-justauth</artifactId> <!-- 社交登陆（例如说，个人微信、企业微信等等） -->
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>wx-java-mp-spring-boot-starter</artifactId> <!-- 微信登录（公众号） -->
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>wx-java-miniapp-spring-boot-starter</artifactId>  <!-- 微信登录（小程序） -->
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId> <!-- 短信（阿里云） -->
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-dysmsapi</artifactId> <!-- 短信（阿里云） -->
        </dependency>
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java-sms</artifactId> <!-- 短信（腾讯云） -->
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.xingyuv</groupId>-->
<!--            <artifactId>spring-boot-starter-captcha-plus</artifactId> &lt;!&ndash; 验证码，一般用于登录使用 &ndash;&gt;-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.dromara.hutool</groupId>
            <artifactId>hutool-extra</artifactId> <!-- 邮件 -->
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>

        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
        </dependency>
        <dependency>
            <groupId>mh.cloud</groupId>
            <artifactId>microservices-module-business-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>mh.cloud</groupId>
            <artifactId>microservices-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.github.fppt</groupId>
            <artifactId>jedis-mock</artifactId>
        </dependency>
        <!--排除内置tomcat 使用外置版 -->
        <!--本地/生产环境部署时需要处理的代码-->
<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-web</artifactId>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>org.springframework.boot</groupId>-->
<!--                    <artifactId>spring-boot-starter-tomcat</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>jakarta.servlet</groupId>-->
<!--            <artifactId>jakarta.servlet-api</artifactId>-->
<!--            <version>6.1.0</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.springframework.kafka</groupId>-->
<!--            <artifactId>spring-kafka</artifactId>-->
<!--            <optional>true</optional>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.glassfish</groupId>-->
<!--            <artifactId>javax.servlet</artifactId>-->
<!--            <version>3.2-b06</version>-->
<!--        </dependency>-->

        <!--本地/生产环境部署时需要处理的代码-->
        <!--内置版-->
        <!--<dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tongweb</groupId>
            <artifactId>tongdino-client-3.x</artifactId>
            <version>1.0.0.0</version>
        </dependency>
         &lt;!&ndash;添加tongweb-spring-boot-starter依赖 &ndash;&gt;
        <dependency>
            <groupId>com.tongweb.springboot</groupId>
            <artifactId>tongweb-spring-boot-starter-3.x</artifactId>
            <version>7.0.E.6_P11</version>
        </dependency>-->
    </dependencies>
    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
