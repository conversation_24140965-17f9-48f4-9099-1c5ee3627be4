package mh.cloud.module.system.controller.admin.publicinformation;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import mh.cloud.framework.common.pojo.PageParam;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.framework.common.util.object.BeanUtils;
import static mh.cloud.framework.common.pojo.CommonResult.success;

import mh.cloud.framework.excel.core.util.ExcelUtils;

import mh.cloud.framework.apilog.core.annotation.ApiAccessLog;
import static mh.cloud.framework.apilog.core.enums.OperateTypeEnum.*;

import mh.cloud.module.system.controller.admin.publicinformation.vo.*;
import mh.cloud.module.system.dal.dataobject.publicinformation.PublicinformationDO;
import mh.cloud.module.system.service.publicinformation.PublicinformationService;

@Tag(name = "管理后台 - 院内信息发布")
@RestController
@RequestMapping("/system/publicinformation")
@Validated
public class PublicinformationController {

    @Resource
    private PublicinformationService publicinformationService;

    @PostMapping("/create")
    @Operation(summary = "创建院内信息发布")
    @PreAuthorize("@ss.hasPermission('system:publicinformation:create')")
    public CommonResult<String> createPublicinformation(@Valid @RequestBody PublicinformationSaveReqVO createReqVO) {
        return success(publicinformationService.createPublicinformation(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新院内信息发布")
    @PreAuthorize("@ss.hasPermission('system:publicinformation:update')")
    public CommonResult<Boolean> updatePublicinformation(@Valid @RequestBody PublicinformationSaveReqVO updateReqVO) {
        publicinformationService.updatePublicinformation(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除院内信息发布")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:publicinformation:delete')")
    public CommonResult<Boolean> deletePublicinformation(@RequestParam("id") String id) {
        publicinformationService.deletePublicinformation(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得院内信息发布")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:publicinformation:query')")
    public CommonResult<PublicinformationRespVO> getPublicinformation(@RequestParam("id") String id) {
        PublicinformationDO publicinformation = publicinformationService.getPublicinformation(id);
        return success(BeanUtils.toBean(publicinformation, PublicinformationRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得院内信息发布分页")
    @PreAuthorize("@ss.hasPermission('system:publicinformation:query')")
    public CommonResult<PageResult<PublicinformationRespVO>> getPublicinformationPage(@Valid PublicinformationPageReqVO pageReqVO) {
        PageResult<PublicinformationDO> pageResult = publicinformationService.getPublicinformationPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PublicinformationRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出院内信息发布 Excel")
    @PreAuthorize("@ss.hasPermission('system:publicinformation:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportPublicinformationExcel(@Valid PublicinformationPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<PublicinformationDO> list = publicinformationService.getPublicinformationPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "院内信息发布.xls", "数据", PublicinformationRespVO.class,
                        BeanUtils.toBean(list, PublicinformationRespVO.class));
    }

}
