package mh.cloud.module.system.controller.admin.business.vo;

public class htmlResVo {
    private String roles;
    private String permissions;
    private String success;
    private String message;
    private String code;

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getRoles() {
        return roles;
    }

    public void setRoles(String roles) {
        this.roles = roles;
    }

    public String getPermissions() {
        return permissions;
    }

    public void setPermissions(String permissions) {
        this.permissions = permissions;
    }

    public String getSuccess() {
        return success;
    }

    public void setSuccess(String success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public mh.cloud.module.system.controller.admin.business.vo.data getData() {
        return data;
    }

    public void setData(mh.cloud.module.system.controller.admin.business.vo.data data) {
        this.data = data;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    private data data;
    private String traceId;
    private String messageType;
}
