package mh.cloud.module.system.controller.admin.portal;

import cn.hutool.core.util.ObjUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import mh.cloud.module.system.service.oauth2.OAuth2TokenService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import java.util.HashMap;
import java.util.Map;

@Tag(name = "token管理外部服务接口")
@RestController
@RequestMapping("/auth2")
@Validated
@Slf4j
public class OutSysTokenServiceController {
    @Resource
    private OAuth2TokenService oauth2TokenService;

    @GetMapping("/sysauthbytoken")
    @PermitAll
    @Operation(summary = "通过x-www-form-urlencoded传token")
    public Map<String, Object> checkOutSystemToken() {
        Map<String, Object> logInfo = new HashMap<>();
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();
        String secretkey = request.getHeader("secretkey");
        if (ObjUtil.isEmpty(secretkey)){
            secretkey = request.getParameter("secretkey");
        }
        if (ObjUtil.isEmpty(secretkey)){
            logInfo.put("msg", "secretkey不存在，请使用正确的secretkey值！");
            logInfo.put("code", "0");
            logInfo.put("loginfo", null);
        }else {
            String sys = request.getHeader("sys");
            if (ObjUtil.isEmpty(sys)){
                sys = request.getParameter("sys");
            }
            if(ObjUtil.isEmpty(sys)){
                logInfo.put("msg", "sys不存在，请使用正确的sys值！");
                logInfo.put("code", "0");
                logInfo.put("loginfo", null);
            }else {
                if (oauth2TokenService.IsNotOutSystem(secretkey,sys)){
                    logInfo.put("msg", "secretkey未授权，请使用正确的secretkey值！");
                    logInfo.put("code", "0");
                    logInfo.put("loginfo", null);
                }else {
                    String token = request.getHeader("systoken");
                    if (ObjUtil.isEmpty(token)) {
                        token = request.getParameter("systoken");
                        if (ObjUtil.isEmpty(token)){
                            log.error("systoken：请求头参数缺失");
                            logInfo.put("msg", "systoken：请求头参数缺失!");
                            logInfo.put("code", "0");
                            logInfo.put("loginfo", null);
                        }else {
                            logInfo = oauth2TokenService.checkOutSystemToken(token);
                        }
                    }else {
                        logInfo = oauth2TokenService.checkOutSystemToken(token);
                    }
                }
            }
        }
        return logInfo;
    }
}
