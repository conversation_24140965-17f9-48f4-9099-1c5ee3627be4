package mh.cloud.module.system.util.storedProcedures;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import mh.cloud.module.system.service.db.SQLHelper;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class TaskListProvider {

    public static Page<Map<String, Object>> getCompleteTaskList(int pageIndex, int pageSize, String userId, String beginTime, String endTime, String con) throws SQLException {
        // 替换con中的单引号
        con = con.replace("&squot;", "'");

        try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("master");
             SQLHelper sqlHelper2 = SQLHelper.createSqlHelper("master");
        ) {
            // 构建SQL查询
            StringBuilder sqlQuery = new StringBuilder();
            String sqlBase = " select * from OA_TaskDataProvider.dbo.";

            // 查询符合条件的表名
            String tableQuery = "SELECT TableItemName FROM OA_TaskDataProvider.dbo.T_CompleteTaskTables WHERE DataTimeEnd > ? AND DataTimeBegin < ?";
            List<Map<String, Object>> maps = sqlHelper.selectRows(tableQuery, beginTime, endTime);

            List<String> tables = new ArrayList<>();

            for (Map<String, Object> map : maps) {
                tables.add(map.get("TableItemName") == null ? "" : map.get("TableItemName").toString());
            }

            // Return empty page if no tables found
            if (tables.isEmpty()) {
                Page<Map<String, Object>> page = new Page<>();
                page.setRecords(new ArrayList<>());
                page.setTotal(0);
                page.setCurrent(pageIndex);
                page.setSize(pageSize);
                page.setPages(0);
                return page;
            }

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String formattedBeginTime = sdf.format(Timestamp.valueOf(beginTime));
            String formattedEndTime = sdf.format(Timestamp.valueOf(endTime));

            for (int i = 0; i < tables.size(); i++) {
                String tableName = tables.get(i);
                sqlQuery.append(sqlBase).append(tableName)
                        .append(" where viewurl is not null and ExecUserID='").append(userId).append("'")
                        .append(" and ExecTime>'").append(formattedBeginTime).append("'")
                        .append(" and ExecTime<'").append(formattedEndTime).append("'")
                        .append(con);
                if (i < tables.size() - 1) {
                    sqlQuery.append(" union all ");
                }
            }

            // 数据分页
            String sqlQueryForPage = "SELECT * FROM ( SELECT ROW_NUMBER() OVER ( ORDER BY ExecTime DESC) AS THENO, * FROM (" + sqlQuery.toString() + ") A ) B WHERE THENO > " + (pageIndex - 1) * pageSize
                    + " AND THENO <= " + pageIndex * pageSize;
            System.out.println(sqlQueryForPage);

            // 查询总记录数
            String sqlQueryCount = "SELECT COUNT(0) AS RecordCount FROM (" + sqlQuery.toString() + ")";
            int recordCount = 0;
            Map<String, Object> map = sqlHelper.selectFirstRow(sqlQueryCount);
            if (map != null) {
                recordCount = map.get("RecordCount") == null ? 0 : Integer.parseInt(map.get("RecordCount").toString());
            }

            List<Map<String, Object>> maps1 = sqlHelper.selectRows(sqlQueryForPage);
            for (Map<String, Object> map1 : maps1) {
                map1.put("TotalCount", recordCount);
            }
            Page<Map<String, Object>> page = new Page<>();
            page.setRecords(maps1);
            page.setTotal(recordCount);
            page.setCurrent(pageIndex);
            page.setSize(pageSize);
            page.setPages(
                    recordCount % pageSize == 0 ? recordCount / pageSize : recordCount / pageSize + 1);
            return page;
        }
    }

}