package mh.cloud.module.system.controller.admin.business;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.module.system.dal.dataobject.job.SysJobLog;
import mh.cloud.module.system.service.job.IJobLogService;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/quartz/job-log")
@Tag(name = "后台管理-任务调度日志接口")
public class JobLogController {

    @Resource
    private IJobLogService jobLogService;

    /**
     * 获取调度日志列表
     */
    @GetMapping("/getLogList")
    @Operation(summary = "获取调度日志列表")
    public Object getJobLogList(@RequestParam String jobId,
                                String beginTime,
                                String endTime,
                                String status,
                                @RequestParam(defaultValue = "1") Integer pageNo,
                                @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<SysJobLog> page = new Page<>(pageNo, pageSize);
        return jobLogService.getJobLogList(jobId, beginTime, endTime, status, page);
    }

    @GetMapping("/getResult")
    @Operation(summary = "获取执行日志")
    public Object getResult(@RequestParam String taskId) {
        return jobLogService.getResult(taskId);
    }


    @PostMapping("/delJobLogs")
    @Operation(summary = "删除日志数据")
    public CommonResult<Boolean> delJobLogs(@RequestParam String jobId, @RequestParam String startTime,
                                            @RequestParam String endTime) {
        return CommonResult.success(jobLogService.delJobLogs(jobId, startTime, endTime));
    }


}
