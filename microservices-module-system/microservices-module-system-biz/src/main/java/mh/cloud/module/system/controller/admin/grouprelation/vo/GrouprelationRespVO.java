package mh.cloud.module.system.controller.admin.grouprelation.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 分组内部关联 Response VO")
@Data
@ExcelIgnoreUnannotated
public class GrouprelationRespVO {

    @Schema(description = "A_Groupd的id", requiredMode = Schema.RequiredMode.REQUIRED, example = "18823")
    @ExcelProperty("A_Groupd的id")
    private String parentGroupID;

    @Schema(description = "UserID", requiredMode = Schema.RequiredMode.REQUIRED, example = "14312")
    @ExcelProperty("UserID")
    private String childGroupID;

    @Schema(description = "全路径ID", example = "17758")
    @ExcelProperty("全路径ID")
    private String deptFullID;

    @Schema(description = "类型", example = "2")
    @ExcelProperty("类型")
    private String relationType;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}