package mh.cloud.module.system.service.db;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.StrUtil;
import com.jfinal.plugin.activerecord.ActiveRecordPlugin;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.plugin.druid.DruidPlugin;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import mh.cloud.module.system.controller.admin.news.service.NewServiceImpl;
import mh.cloud.module.system.dal.mysql.auser.AUserMapper;
import mh.cloud.module.system.service.db.VO.DbConfig;
import mh.cloud.module.system.service.db.conf.MasterDataSourceProperties;
import mh.cloud.module.system.util.des.DesUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
//@Slf4j
public class SqlServiceImpl implements SqlService {
    @Resource
    private MasterDataSourceProperties masterDataSourceProperties;

    @Autowired
    private Environment environment;

    private static JfinalSqlHelper jfinalSqlHelper = new JfinalSqlHelper();
    @Resource
    private AUserMapper aUserMapper;

    /**
     * 创建数据库配置对象，根据数据库名称查询连接字符串、用户名和密码。
     *
     * @param dbName 数据库名称
     * @return DbConfig 数据库配置对象
     */
    @Override
    public DbConfig createConfigByDbName(String dbName) {
        try {
            //如果是主库，直接返回配置文件信息
            if ("master".equals(dbName) || "Core".equals(dbName)) {
                return getMaserDbConfig();
            } else if (getDbConfigByName(dbName) != null) {
                return getDbConfigByName(dbName);
            }
            // 获取主数据库配置
            DbConfig dbConfig = getMaserDbConfig();
            // 设置数据库连接字符串
            Map<String, Object> connectStringByDbName = aUserMapper.getConnectStringByDbName(dbName);
            String connectString = connectStringByDbName.get("ConnectString").toString();
            String dbType = connectStringByDbName.get("DbType").toString();
            if (StrUtil.isEmptyIfStr(connectString)) {
                return null;
            }
            //检查是否需要解密
            if (!connectString.contains(";")) {
                connectString = DesUtils.DesDecrypt(dbName, connectString);
//                log.info("解密成功:" + connectString);
            }

            setjdbcUrl(dbConfig, connectString, dbType);

            // 设置数据库用户名和密码
               /* String loginName = record.get("LoginName");
                String password = record.get("Password");*/

            // 更新DbConfig对象中的连接信息

//                dbConfig.setUsername(loginName);
//                dbConfig.setPassword(password);

            // 返回填充好的DbConfig对象
            return dbConfig;
        } catch (Exception e) {
            e.printStackTrace();
//            log.error("获取数据库连接信息时报错：" + e.getMessage());
            // 异常处理：返回null表示创建失败
            return null;
        }

        // 如果没有找到匹配的数据库配置，返回null
    }

    /**
     * 读取配置文件中主数据库的配置
     *
     * @return
     */
    @Override
    public @NotNull DbConfig getMaserDbConfig() {
        DbConfig dbConfig = BeanUtil.toBean(masterDataSourceProperties, DbConfig.class);
        dbConfig.setJdbcUrl(masterDataSourceProperties.getUrl());
        return dbConfig;
    }

    public DbConfig getDbConfigByName(String dataSourceName) {
        try {
            // 构建配置前缀
            String prefix = "spring.datasource.dynamic.datasource." + dataSourceName;

            // 从环境变量中读取配置
            String url = environment.getProperty(prefix + ".url");
            String username = environment.getProperty(prefix + ".username");
            String password = environment.getProperty(prefix + ".password");

            if (url == null || url.trim().isEmpty()) {
                return null;
            }

            // 创建 DbConfig 对象
            DbConfig dbConfig = new DbConfig();
            dbConfig.setJdbcUrl(url);
            dbConfig.setUsername(username);
            dbConfig.setPassword(password);

            return dbConfig;

        } catch (Exception e) {
            // 如果获取失败，抛出异常
            throw new IllegalArgumentException("数据源配置不存在或无效: " + dataSourceName, e);
        }
    }

    private void setjdbcUrl(DbConfig dbConfig, String connectString, String dbType) {
        String[] parts = connectString.split(";");
        String serverPart = "";
        String portPart = "";
        String userId = "";
        String pwd = "";
        String database = "";

        for (String part : parts) {
            if (part.startsWith("Server=")) {
                serverPart = part.substring("Server=".length());
            } else if (part.startsWith("Port=")) {
                portPart = part.substring("Port=".length());
            } else if (part.startsWith("User ID=")) {
                userId = part.substring("User ID=".length());
            } else if (part.startsWith("Password=")) {
                pwd = part.substring("Password=".length());
            } else if (part.startsWith("Database=")) {
                database = part.substring("Database=".length());
            }
        }
        String jdbcUrlTeample = dbConfig.getJdbcUrlTeample();
        if (dbType.equals("MSSQL")) {
            // SQL Server 标准连接字符串模板
            jdbcUrlTeample = getJdbcUrlTemplate("MSSQL");
        }
        //将jdbcUrlTeample中的ip_port替换为server_part
        if (StrUtil.isEmptyIfStr(portPart)) {
//            log.error("数据库端口无法解析：" + connectString);
            throw new RuntimeException("数据库端口无法解析：" + portPart);
        }
        jdbcUrlTeample = jdbcUrlTeample.replace("ip_port", portPart);
        //将jdbcUrlTeample中的ip_addr替换为server_part
        jdbcUrlTeample = jdbcUrlTeample.replace("ip_addr", serverPart);
        if (StrUtil.isEmptyIfStr(serverPart)) {
//            log.error("数据库地址无法解析：" + connectString);
            throw new RuntimeException("数据库ip无法解析：" + serverPart);
        }
        if (StrUtil.isEmptyIfStr(database)) {
//            log.error("数据库名称无法解析：" + connectString);
            throw new RuntimeException("数据库名无法解析：" + database);
        }

        if (dbType.equals("MSSQL")) {
            jdbcUrlTeample = jdbcUrlTeample.replace("MSSQLDatabaseName", database);
        } else {
            jdbcUrlTeample = jdbcUrlTeample.replace("databaseName", database);
        }
        dbConfig.setJdbcUrl(jdbcUrlTeample);
        dbConfig.setPassword(pwd);
        dbConfig.setUsername(userId);
    }

    /**
     * 根据数据库类型生成正确的连接字符串模板
     */
    private String getJdbcUrlTemplate(String dbType) {
        switch (dbType.toUpperCase()) {
            case "MSSQL":
            case "SQLSERVER":
                return "********************************;" +
                        "databaseName=MSSQLDatabaseName;" +
                        "encrypt=false;" +
                        "trustServerCertificate=true;" +
                        "loginTimeout=30";

            case "KINGBASE":
            case "KINGBASE8":
                return "*********************************************?" +
                        "currentSchema=dbo,productName=PostgreSQL,SYS_CATALOG,PUBLIC&" +
                        "zeroDateTimeBehavior=convertToNull&" +
                        "useUnicode=true&" +
                        "characterEncoding=utf-8&" +
                        "SSL=false&" +
                        "nullCatalogMeansCurrent=true";

            case "MYSQL":
                return "*****************************************?" +
                        "useUnicode=true&" +
                        "characterEncoding=utf8&" +
                        "serverTimezone=GMT%2B8&" +
                        "useSSL=false";

            case "POSTGRESQL":
                return "**********************************************?" +
                        "currentSchema=public&" +
                        "ssl=false";

            case "ORACLE":
                return "**********************************************";

            default:
                throw new IllegalArgumentException("不支持的数据库类型: " + dbType);
        }
    }

    /**
     * 查询数据库并返回记录列表，不带参数化查询。
     *
     * @param dbName 数据库名称，用于确定数据库配置
     * @param sql    SQL查询语句
     * @return List<Record> 查询结果的记录列表
     */
    @Override
    public List<Record> find(String dbName, String sql) {
        // 根据数据库名称创建数据库配置
        DbConfig dbConfig = createConfigByDbName(dbName);

        // 获取数据库操作所需的额外结果信息
        Result result = getResult(dbConfig);

        // 执行SQL查询并获取记录列表
        List<Record> records = Db.find(sql);

        // 停止数据库插件，如Druid监控和ARP（Active Record Plugin）
        stopPlugins(result.druidPlugin(), result.arp());

        // 返回查询结果
        return records;
    }

    /**
     * 查询数据库并返回记录列表，支持参数化查询。
     *
     * @param dbName 数据库名称，用于确定数据库配置
     * @param sql    SQL查询语句
     * @param paras  查询参数数组
     * @return List<Record> 查询结果的记录列表
     */
    @Override
    public List<Record> find(String dbName, String sql, Object... paras) {
        // 根据数据库名称创建数据库配置
        DbConfig dbConfig = createConfigByDbName(dbName);

        // 获取数据库操作所需的额外结果信息
        Result result = getResult(dbConfig);

        // 执行参数化的SQL查询并获取记录列表
        List<Record> records = Db.find(sql, paras);

        // 停止数据库插件，如Druid监控和ARP（Active Record Plugin）
        stopPlugins(result.druidPlugin(), result.arp());

        // 返回查询结果
        return records;
    }

    @Override
    public Record findFirst(String dbName, String sql, Object... paras) {
        // 根据数据库名称创建数据库配置
        DbConfig dbConfig = createConfigByDbName(dbName);

        // 获取数据库操作所需的额外结果信息
        Result result = getResult(dbConfig);

        // 执行参数化的SQL查询并获取记录列表
        Record records = Db.findFirst(sql, paras);

        // 停止数据库插件，如Druid监控和ARP（Active Record Plugin）
        stopPlugins(result.druidPlugin(), result.arp());

        // 返回查询结果
        return records;
    }

    private @NotNull Result getResult(DbConfig dbConfig) {
        DruidPlugin druidPlugin = startDruidPlugin(dbConfig);
        ActiveRecordPlugin arp = startActiveRecordPlugin(druidPlugin);
        return new Result(druidPlugin, arp);
    }

    private record Result(DruidPlugin druidPlugin, ActiveRecordPlugin arp) {
    }

    public DruidPlugin createDruidPlugin(DbConfig config) {
        return new DruidPlugin(config.getJdbcUrl(), config.getUsername(), config.getPassword(), config.getDriverClassName());
    }

    private void stopPlugins(DruidPlugin druidPlugin, ActiveRecordPlugin arp) {
        if (arp != null) {
            arp.stop();
        }
        if (druidPlugin != null) {
            druidPlugin.stop();
        }
    }

    private DruidPlugin startDruidPlugin(DbConfig dbConfig) {
        DruidPlugin druidPlugin = createDruidPlugin(dbConfig);
        druidPlugin.start();
        return druidPlugin;
    }

    private ActiveRecordPlugin startActiveRecordPlugin(DruidPlugin druidPlugin) {
        ActiveRecordPlugin arp = new ActiveRecordPlugin(druidPlugin);
        arp.setShowSql(true);
        arp.setDevMode(true);
        arp.start();
        return arp;
    }

    @Override
    public Page<Record> paginate(String dbName, int pageNumber, int pageSize, String select, String sqlExceptSelect, Object... paras) {
        DbConfig dbConfig = createConfigByDbName(dbName);
        Result result = getResult(dbConfig);

        Page<Record> recordPage = Db.paginate(pageNumber, pageSize, select, sqlExceptSelect, paras);
        stopPlugins(result.druidPlugin(), result.arp());
        return recordPage;
    }

    @Override
    public Page<Record> paginate(String dbName, int pageNumber, int pageSize, String select, Object... paras) {
        DbConfig dbConfig = createConfigByDbName(dbName);
        Result result = getResult(dbConfig);
        Page<Record> recordPage = Db.paginateByFullSql(pageNumber, pageSize, "select count(*) from ( " + select + ")", select, paras);

        stopPlugins(result.druidPlugin(), result.arp());
        return recordPage;
    }


    @Override
    public Page<Record> getFw(int pageNumber, int pageSize) {


        String code = "List_ad520113ed544ca9a81ccc7b99e2fc09";
        Record record = findFirst("master", "select * from ui_List where tenant_id=? and code=?", "1", code);
        if (record != null) {
            String tableNames = record.get("TableNames");
            if (StrUtil.isNotEmpty(tableNames)) {
                String sql = "select * from " + tableNames + " where tenant_id=? and Code=?";
                Record first = findFirst("OA", sql, "1", "YNFW");
                if (first != null) {
                    String listSql = first.get("ListSql");
                    if (StrUtil.isNotEmpty(listSql)) {
                        Page<Record> list = paginate("OA", pageNumber, pageSize, listSql);
                        return list;
                    }
                }
            }

        }
        return null;
    }

    @Override
    public Page<Record> paginate(String dbName, int pageNumber, int pageSize, String select) {
        DbConfig dbConfig = createConfigByDbName(dbName);
        Result result = getResult(dbConfig);
        Page<Record> recordPage = Db.paginateByFullSql(pageNumber, pageSize, "select count(*) from ( " + select + ")", select);
//        Page<Record> recordPage = Db.paginate(pageNumber, pageSize, select, sqlExceptSelect);
        stopPlugins(result.druidPlugin(), result.arp());
        return recordPage;
    }
}
