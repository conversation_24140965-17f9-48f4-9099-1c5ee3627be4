package mh.cloud.module.system.service.db;


// 导入必要的包

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import mh.cloud.module.system.service.db.VO.DatabaseConnection;
import mh.cloud.module.system.service.db.VO.DbConfig;
import org.springframework.stereotype.Service;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
// SQLHelper类，提供数据库操作的通用方法
@Data
@Slf4j
public class SQLHelperImpl implements SQLHelper {
    private static final String timeFormatStrs = "FlowCreateTime||CreateTime||WJDate||SendTime||FirstViewTime||ReplyTime";
    private static final Map<String, SQLHelperImpl> instances = new HashMap<>();
    @Resource
    private SqlService sqlService = SpringUtil.getBean(SqlServiceImpl.class);

    private Connection connection;

    private PreparedStatement pstmt;

    private ResultSet rs;


    public SQLHelperImpl() {
    }

    public SQLHelperImpl(Connection connection) {
        this.connection = connection;
        validateConnection(connection);
    }

    public SQLHelperImpl(String connName) throws SQLException, ClassNotFoundException {
        if (connName.equals("SqlServer")) {
            this.connection = DatabaseConnection.getTargetConnection();
        } else {
            this.connection = getConnection(connName);
        }
        validateConnection(connection);
    }

    public static SQLHelperImpl createSqlHelper(String type) {
        SQLHelperImpl instance = instances.get(type);
        if (instance == null) {
            synchronized (SQLHelperImpl.class) {
                instance = instances.get(type);
                if (instance == null) {
                    try {
                        instance = new SQLHelperImpl(type);
                    } catch (SQLException e) {
                        log.error("createSqlHelper error", type);
                    } catch (ClassNotFoundException e) {
                        log.info("数据库驱动类未找到，请检查配置文件！");
                    }
                    instances.put(type, instance);
                }
            }
        }
        return instance;
    }
    private void validateConnection(Connection connection) {
        if (connection == null) {
            throw new IllegalArgumentException("Connection cannot be null");
        }
    }

    // 根据数据库类型返回数据库连接
    public Connection getConnection(String connName) throws ClassNotFoundException, SQLException {
        //获取数据的配置文件
        DbConfig configByDbName = sqlService.createConfigByDbName(connName);
        //根据数据库配置文件创建数据库连接
        if(configByDbName.getJdbcUrl() != null && !configByDbName.getJdbcUrl().isEmpty()){
            // 根据JDBC URL判断数据库类型并设置相应的驱动类名
            String jdbcUrl = configByDbName.getJdbcUrl();
            String driverClassName = determineDriverClassName(jdbcUrl);
            configByDbName.setDriverClassName(driverClassName);
        }
        Class.forName(configByDbName.getDriverClassName());
        Connection connection = DriverManager.getConnection(configByDbName.getJdbcUrl(), configByDbName.getUsername(), configByDbName.getPassword());
        return connection;
    }

    /**
     * 根据JDBC URL判断数据库类型并返回相应的驱动类名
     *
     * @param jdbcUrl JDBC连接URL
     * @return 对应的数据库驱动类名
     * @throws IllegalArgumentException 如果数据库类型不支持
     */
    private String determineDriverClassName(String jdbcUrl) {
        if (jdbcUrl == null || jdbcUrl.isEmpty()) {
            throw new IllegalArgumentException("JDBC URL不能为空");
        }

        // 转换为小写便于比较
        String lowerUrl = jdbcUrl.toLowerCase();

        if (lowerUrl.startsWith("jdbc:sqlserver://")) {
            // SQL Server数据库
            return "com.microsoft.sqlserver.jdbc.SQLServerDriver";
        } else if (lowerUrl.startsWith("jdbc:kingbase8://")) {
            // 人大金仓数据库
            return "com.kingbase8.Driver";
        } else if (lowerUrl.startsWith("jdbc:mysql://")) {
            // MySQL数据库
            return "com.mysql.cj.jdbc.Driver";
        } else if (lowerUrl.startsWith("jdbc:postgresql://")) {
            // PostgreSQL数据库
            return "org.postgresql.Driver";
        } else if (lowerUrl.startsWith("jdbc:oracle:")) {
            // Oracle数据库
            return "oracle.jdbc.driver.OracleDriver";
        } else if (lowerUrl.startsWith("jdbc:h2:")) {
            // H2数据库
            return "org.h2.Driver";
        } else {
            // 不支持的数据库类型
            throw new IllegalArgumentException("不支持的数据库类型，JDBC URL: " + jdbcUrl);
        }
    }


    // 执行非查询SQL语句

    /**
     * 执行非查询SQL语句，例如插入、更新或删除。
     *
     * @param sql 需要执行的SQL语句。
     * @return 更新的行数。
     * @throws RuntimeException 如果执行时发生SQL异常，则抛出运行时异常。
     */
    public int executeNonQuery(String sql) {
        if (StrUtil.isEmptyIfStr(sql)) {
            return 0;
        }
        try {
            pstmt = connection.prepareStatement(sql);
            return pstmt.executeUpdate();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }
    //执行存储过程

    /**
     * 执行非查询SQL语句，带有参数。
     *
     * @param sql    需要执行的带参数的SQL语句。
     * @param params SQL语句中的参数，按照顺序对应参数的位置。
     * @return 更新的行数。
     * @throws RuntimeException 如果执行时发生SQL异常，则抛出运行时异常。
     */
    public int executeNonQuery(String sql, Object... params) {
        if (StrUtil.isEmptyIfStr(sql)) {
            return 0;
        }
        try {
            pstmt = connection.prepareStatement(sql);
            if (params != null) {
                //循环将params设置到PreparedStatement中
                for (int i = 0; i < params.length; i++) {
                    pstmt.setObject(i + 1, params[i]);
                }
            }
            return pstmt.executeUpdate();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 执行SQL查询语句，返回第一行的第一列的数据。
     * 该方法用于执行那些预期只返回单个结果的查询，如SELECT COUNT(*)等。
     *
     * @param sql 查询语句
     * @return 查询结果，作为一个Object返回
     */
    public Object executeScalar(String sql) {
        if (StrUtil.isEmptyIfStr(sql)) {
            return null;
        }
        try {
            PreparedStatement pstmt = connection.prepareStatement(sql);
            rs = pstmt.executeQuery();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return rs;
    }

    /**
     * 执行SQL查询语句，返回所有查询结果的列表。
     * 每个查询结果被表示为一个Map，其中键是列名，值是对应的列值。
     * 这种方法适用于返回多行多列数据的查询。
     *
     * @param sql 查询语句
     * @return 包含所有查询结果的List，每个结果是一个包含列名和列值的Map
     */
    public List<Map<String, Object>> executeReader(String sql) {
        if (StrUtil.isEmptyIfStr(sql)) {
            return null;
        }
        List<Map<String, Object>> resultList = new ArrayList<>();
        try {
            pstmt = connection.prepareStatement(sql);
            rs = pstmt.executeQuery();
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();

            while (rs.next()) {
                Map<String, Object> row = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
//                    row.put(metaData.getColumnName(i), rs.getObject(i));
                    setTimeFormat(metaData, i, row);
                }
                resultList.add(row);
            }

            return resultList;
        } catch (SQLException | RuntimeException e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 根据SQL语句查询第一行数据。
     * 本方法用于执行一个查询SQL语句，并返回查询结果的第一行数据作为Map对象。
     * 如果查询结果为空，则返回null。
     *
     * @param sql 查询的SQL语句
     * @return 返回查询结果的第一行数据作为Map对象，如果结果为空则返回null。
     */
    @Override
    public Map<String, Object> selectFirstRow(String sql) {
        if (StrUtil.isEmptyIfStr(sql)) {
            return null;
        }
        try {
            // 执行查询SQL语句，返回所有查询结果的List集合
            List<Map<String, Object>> maps = executeReader(sql);
            // 判断查询结果是否为空，如果不为空则返回第一行数据，否则返回null
            if (IterUtil.isNotEmpty(maps)) {
                return maps.get(0);
            } else {
                return null;
            }

        } catch (RuntimeException e) {
            // 捕获运行时异常，如果发生异常则返回null
            return null;
        }
    }

    /**
     * 根据SQL语句和参数查询第一行数据。
     * 本方法用于执行一个带参数的查询SQL语句，并返回查询结果的第一行数据作为Map对象。
     * 如果查询结果为空，则返回null。
     *
     * @param sql    查询的SQL语句
     * @param params 查询的参数，可变参数
     * @return 返回查询结果的第一行数据作为Map对象，如果结果为空则返回null。
     */
    @Override
    public Map<String, Object> selectFirstRow(String sql, Object... params) {
        if (StrUtil.isEmptyIfStr(sql)) {
            return null;
        }
        try {
            // 执行带参数的查询SQL语句，返回所有查询结果的List集合
            List<Map<String, Object>> maps = executeReader(sql, params);
            // 判断查询结果是否为空，如果不为空则返回第一行数据，否则返回null
            if (IterUtil.isNotEmpty(maps)) {
                return maps.get(0);
            } else {
                return null;
            }

        } catch (RuntimeException e) {
            // 捕获运行时异常，如果发生异常则返回null
            return null;
        }
    }

    /**
     * 执行SQL查询，返回结果集的列表。
     *
     * @param sql    要执行的SQL查询语句。
     * @param params SQL查询语句中的参数，可变参数。
     * @return 包含查询结果的Map列表，每个Map代表结果集的一行，键为列名，值为列值。
     * @throws RuntimeException 如果执行查询时发生SQL异常或运行时异常。
     */
    public List<Map<String, Object>> executeReader(String sql, Object... params) {
        if (StrUtil.isEmptyIfStr(sql)) {
            return null;
        }
        List<Map<String, Object>> resultList = new ArrayList<>();
        try {
            // 准备SQL语句，参数化查询
            pstmt = connection.prepareStatement(sql);
            // 如果有参数，则设置参数
            if (params != null) {
                //循环将params设置到PreparedStatement中
                for (int i = 0; i < params.length; i++) {
                    pstmt.setObject(i + 1, params[i]);
                }
            }
            // 执行查询
            rs = pstmt.executeQuery();
            // 获取结果集元数据
            ResultSetMetaData metaData = rs.getMetaData();
            // 获取结果集的列数
            int columnCount = metaData.getColumnCount();

            // 遍历结果集，将每行数据存储在一个Map中，然后添加到结果列表中
            while (rs.next()) {
                Map<String, Object> row = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    // 以列名作为键，列值作为值，存储在当前行的Map中
//                    row.put(metaData.getColumnName(i), rs.getObject(i));
                    setTimeFormat(metaData, i, row);
                }
                resultList.add(row);
            }

            return resultList;
        } catch (SQLException | RuntimeException e) {
            throw new RuntimeException(e);
        }
    }

    private void setTimeFormat(ResultSetMetaData metaData, int i, Map<String, Object> row) throws SQLException {
        if (metaData.getColumnName(i) == null) return;
        if (timeFormatStrs.contains(metaData.getColumnName(i))) {
            try {
                //将rs.getObject(i)强制为LocalDateTime
                row.put(metaData.getColumnName(i), rs.getObject(i).toString().split("\\.")[0]);
            } catch (Exception e) {
                row.put(metaData.getColumnName(i), rs.getObject(i));
            }
        } else {
            row.put(metaData.getColumnName(i), rs.getObject(i));
        }
    }

    @Override
    public List<Map<String, Object>> selectRows(String sql, Object... params) {
        if (StrUtil.isEmptyIfStr(sql)) {
            return null;
        }
        return this.executeReader(sql, params);
    }

    @Override
    public List<Map<String, Object>> selectRows(String sql) {
        if (StrUtil.isEmptyIfStr(sql)) {
            return null;
        }
        return this.executeReader(sql);
    }


    /**
     * 返回分页查询结果
     *
     * @param sql
     * @param startRowNum
     * @param maxRowNum
     * @param params
     * @return
     */
    @Override//string sql, int startRowNum, int maxRowNum,Object... params 实现分页查询
    public Page<Map<String, Object>> ExecuteDataTable(String sql, int startRowNum, int maxRowNum, Object... params) {
        if (StrUtil.isEmptyIfStr(sql)) {
            return Page.of(0, 0, 0, false);
        }
        String countSql = "SELECT COUNT(*) FROM (" + sql + ") t";
        int totalCount = 0;
        try {
            pstmt = connection.prepareStatement(countSql);
            if (params != null) {
                //循环将params设置到PreparedStatement中
                for (int i = 0; i < params.length; i++) {
                    pstmt.setObject(i + 1, params[i]);
                }
            }
            rs = pstmt.executeQuery();
            if (rs.next()) {
                totalCount = rs.getInt(1);
            }

            List<Map<String, Object>> resultList = new ArrayList<>();
            sql = "SELECT * FROM (" + sql + ") t limit " + maxRowNum + "OFFSET " + maxRowNum * (startRowNum - 1);
            pstmt = connection.prepareStatement(sql);
            if (params != null) {
                //循环将params设置到PreparedStatement中
                for (int i = 0; i < params.length; i++) {
                    pstmt.setObject(i + 1, params[i]);
                }
            }
            rs = pstmt.executeQuery();
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();

            while (rs.next()) {
                Map<String, Object> row = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
//                    row.put(metaData.getColumnName(i), rs.getObject(i));
                    setTimeFormat(metaData, i, row);
                }
                resultList.add(row);
            }
            Page<Map<String, Object>> pageResult = new Page<>();
            pageResult.setRecords(resultList);
            pageResult.setTotal(totalCount);
            pageResult.setCurrent(startRowNum);
            pageResult.setSize(maxRowNum);
            pageResult.setPages(totalCount % maxRowNum == 0 ? totalCount / maxRowNum : totalCount / maxRowNum + 1);
            return pageResult;
        } catch (SQLException | RuntimeException e) {
            throw new RuntimeException(e);
        }

    }

    //string sql, int startRowNum, int maxRowNum

    /**
     * 执行SQL查询语句，返回分页查询结果。
     *
     * @param sql
     * @param startRowNum
     * @param maxRowNum
     * @return
     */
    @Override
    public Page<Map<String, Object>> ExecuteDataTable(String sql, int startRowNum, int maxRowNum) {
        if (StrUtil.isEmptyIfStr(sql)) {
            return null;
        }
        String countSql = "SELECT COUNT(*) FROM (" + sql + ") t";
        int totalCount = 0;
        try {
            pstmt = connection.prepareStatement(countSql);

            rs = pstmt.executeQuery();
            if (rs.next()) {
                totalCount = rs.getInt(1);
            }

            List<Map<String, Object>> resultList = new ArrayList<>();
            sql = "SELECT * FROM (" + sql + ") t limit " + maxRowNum + "OFFSET " + maxRowNum * (startRowNum - 1);
            pstmt = connection.prepareStatement(sql);

            rs = pstmt.executeQuery();
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();

            while (rs.next()) {
                Map<String, Object> row = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    setTimeFormat(metaData, i, row);
                }
                resultList.add(row);
            }
            Page<Map<String, Object>> pageResult = new Page<>();
            pageResult.setRecords(resultList);
            pageResult.setTotal(totalCount);
            pageResult.setCurrent(startRowNum);
            pageResult.setSize(maxRowNum);
            pageResult.setPages(totalCount % maxRowNum == 0 ? totalCount / maxRowNum : totalCount / maxRowNum + 1);
            return pageResult;
        } catch (SQLException | RuntimeException e) {
            throw new RuntimeException(e);
        }

    }

    @Override
    public Page<Map<String, Object>> page(String sql, int startRowNum, int maxRowNum, Object... params) {
        if (StrUtil.isEmptyIfStr(sql)) {
            return null;
        }
        return this.ExecuteDataTable(sql, startRowNum, maxRowNum, params);
    }

    @Override
    public Page<Map<String, Object>> page(String sql, int startRowNum, int maxRowNum) {
        if (StrUtil.isEmptyIfStr(sql)) {
            return null;
        }
        return this.ExecuteDataTable(sql, startRowNum, maxRowNum);
    }

    @Override
    public int count(String sql, Object... params) {
        if (StrUtil.isEmptyIfStr(sql)) {
            return 0;
        }

        String countSql = "SELECT COUNT(*) FROM (" + sql + ") t";
        int totalCount = 0;
        try {
            pstmt = connection.prepareStatement(countSql);
            if (params != null) {
                //循环将params设置到PreparedStatement中
                for (int i = 0; i < params.length; i++) {
                    pstmt.setObject(i + 1, params[i]);
                }
            }
            rs = pstmt.executeQuery();
            if (rs.next()) {
                totalCount = rs.getInt(1);
            }
            return totalCount;
        } catch (Exception e) {
            log.error("sql异常：" + sql);
        }
        return 0;
    }

    @Override
    public int count(String sql) {
        if (StrUtil.isEmptyIfStr(sql)) {
            return 0;
        }
        String countSql = "SELECT COUNT(*) FROM (" + sql + ") t";
        int totalCount = 0;
        try {
            pstmt = connection.prepareStatement(countSql);
            rs = pstmt.executeQuery();
            if (rs.next()) {
                totalCount = rs.getInt(1);
                return totalCount;
            }
        } catch (Exception e) {
            log.error("sql异常：" + sql);
        }
        return 0;
    }


    // 构建插入SQL语句的方法
    public static String createInsertSql(String tableName, ResultSet rs) {
        // 构建SQL语句
        StringBuilder sb = new StringBuilder("INSERT INTO ").append(tableName).append(" (");

        // 遍历ResultSet并添加列名
        try {
            ResultSetMetaData metaData = rs.getMetaData();
            for (int i = 1; i <= metaData.getColumnCount(); i++) {
                if (i > 1) sb.append(", ");
                sb.append(metaData.getColumnName(i));
            }

            sb.append(") VALUES (");

            // 添加占位符
            for (int i = 1; i <= metaData.getColumnCount(); i++) {
                if (i > 1) sb.append(", ");
                sb.append("?");
            }

            sb.append(")");
        } catch (SQLException e) {
            // 处理元数据异常
            e.printStackTrace();
        }

        return sb.toString();
    }

    @Override
    public void close() {
        closeResultSet();
        closePreparedStatement();
        closeConection();
    }

    private void closeConection() {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                // Log the exception or handle it as needed
                System.err.println("Error while closing connection: " + e.getMessage());
            }
        }
    }

    private void closeResultSet() {
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                // Log the exception or handle it as needed
                System.err.println("Error while closing ResultSet: " + e.getMessage());
            }
        }
    }

    private void closePreparedStatement() {
        if (pstmt != null) {
            try {
                pstmt.close();
            } catch (SQLException e) {
                // Log the exception or handle it as needed
                System.err.println("Error while closing PreparedStatement: " + e.getMessage());
            }
        }
    }


}
