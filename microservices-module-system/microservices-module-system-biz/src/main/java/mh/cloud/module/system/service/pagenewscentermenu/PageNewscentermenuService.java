package mh.cloud.module.system.service.pagenewscentermenu;

import java.util.*;
import jakarta.validation.*;
import mh.cloud.module.system.controller.admin.pagenewscentermenu.vo.*;
import mh.cloud.module.system.dal.dataobject.pagenewscentermenu.PageNewscentermenuDO;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.pojo.PageParam;

/**
 * 菜单管理 Service 接口
 *
 * <AUTHOR>
 */
public interface PageNewscentermenuService {

    /**
     * 创建菜单管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPageNewscentermenu(@Valid PageNewscentermenuSaveReqVO createReqVO);

    /**
     * 更新菜单管理
     *
     * @param updateReqVO 更新信息
     */
    void updatePageNewscentermenu(@Valid PageNewscentermenuSaveReqVO updateReqVO);

    /**
     * 删除菜单管理
     *
     * @param id 编号
     */
    void deletePageNewscentermenu(String id);

    /**
     * 获得菜单管理
     *
     * @param id 编号
     * @return 菜单管理
     */
    PageNewscentermenuDO getPageNewscentermenu(String id);

    /**
     * 获得菜单管理分页
     *
     * @param pageReqVO 分页查询
     * @return 菜单管理分页
     */
    PageResult<PageNewscentermenuDO> getPageNewscentermenuPage(PageNewscentermenuPageReqVO pageReqVO);

}