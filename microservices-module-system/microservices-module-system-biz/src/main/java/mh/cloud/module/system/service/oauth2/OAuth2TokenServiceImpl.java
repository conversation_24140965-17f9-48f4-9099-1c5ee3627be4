package mh.cloud.module.system.service.oauth2;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import mh.cloud.framework.common.enums.UserTypeEnum;
import mh.cloud.framework.common.exception.BaseException;
import mh.cloud.framework.common.exception.enums.GlobalErrorCodeConstants;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.util.date.DateUtils;
import mh.cloud.framework.mybatis.core.dataobject.BaseDO;
import mh.cloud.framework.security.core.LoginUser;
import mh.cloud.framework.tenant.core.context.TenantContextHolder;
import mh.cloud.module.system.controller.admin.auth.vo.AuthLoginRespVO;
import mh.cloud.module.system.controller.admin.oauth2.vo.token.OAuth2AccessTokenPageReqVO;
import mh.cloud.module.system.controller.admin.portal.V0.UserInfo;
import mh.cloud.module.system.controller.admin.portal.constant.QRCodeConstant;
import mh.cloud.module.system.controller.admin.portal.service.CommonService;
import mh.cloud.module.system.dal.dataobject.auser.AUserDO;
import mh.cloud.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import mh.cloud.module.system.dal.dataobject.oauth2.OAuth2ClientDO;
import mh.cloud.module.system.dal.dataobject.oauth2.OAuth2RefreshTokenDO;
import mh.cloud.module.system.dal.mysql.oauth2.OAuth2AccessTokenMapper;
import mh.cloud.module.system.dal.mysql.oauth2.OAuth2RefreshTokenMapper;
import mh.cloud.module.system.dal.redis.oauth2.OAuth2AccessTokenRedisDAO;
import mh.cloud.module.system.enums.ErrorCodeConstants;
import mh.cloud.module.system.enums.logger.LoginLogTypeEnum;
import mh.cloud.module.system.service.auser.AUserService;
import mh.cloud.module.system.service.auth.AdminAuthService;
import mh.cloud.module.system.service.db.SQLHelper;
import mh.cloud.module.system.service.user.AdminUserService;
import mh.cloud.module.system.util.ConfigurationHelper;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static mh.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static mh.cloud.framework.common.exception.util.ServiceExceptionUtil.exception0;
import static mh.cloud.framework.common.util.collection.CollectionUtils.convertSet;
import static mh.cloud.framework.common.util.servlet.ServletUtils.getClientIP;

/**
 * OAuth2.0 Token Service 实现类
 */
@Service
public class OAuth2TokenServiceImpl implements OAuth2TokenService {

    @Resource
    private OAuth2AccessTokenMapper oauth2AccessTokenMapper;
    @Resource
    private OAuth2RefreshTokenMapper oauth2RefreshTokenMapper;

    @Resource
    private OAuth2AccessTokenRedisDAO oauth2AccessTokenRedisDAO;

    @Resource
    private OAuth2ClientService oauth2ClientService;
    @Resource
    @Lazy // 懒加载，避免循环依赖
    private AdminUserService adminUserService;

    @Resource
    @Lazy // 懒加载，避免循环依赖
    private AUserService aUserService;

    @Resource
    private HttpServletRequest request;

    @Resource
    private ConfigurationHelper configurationHelper;

    @Resource
    private CommonService commonService;

    @Resource
    private AdminAuthService adminAuthService;


    private static String generateAccessToken() {
        return IdUtil.fastSimpleUUID();
    }

    private static String generateRefreshToken() {
        return IdUtil.fastSimpleUUID();
    }

    @Override
    public OAuth2AccessTokenDO createAccessToken(String userId, Integer userType, String clientId, List<String> scopes) {
        //删除已过期的访问令牌
        oauth2AccessTokenMapper.clearExpiredTokenByUserId(userId);
        OAuth2ClientDO clientDO = oauth2ClientService.validOAuthClientFromCache(clientId);
        // 创建刷新令牌
        OAuth2RefreshTokenDO refreshTokenDO = createOAuth2RefreshToken(userId, userType, clientDO, scopes);

        // 创建访问令牌
        return createOAuth2AccessToken(refreshTokenDO, clientDO);
    }

    @Override
    public OAuth2AccessTokenDO refreshAccessToken(String refreshToken, String clientId) {
        // 查询访问令牌
        OAuth2RefreshTokenDO refreshTokenDO = oauth2RefreshTokenMapper.selectByRefreshToken(refreshToken);
        if (refreshTokenDO == null) {
            throw exception0(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "无效的刷新令牌");
        }

        // 校验 Client 匹配
        OAuth2ClientDO clientDO = oauth2ClientService.validOAuthClientFromCache(clientId);
        if (ObjectUtil.notEqual(clientId, refreshTokenDO.getClientId())) {
            throw exception0(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "刷新令牌的客户端编号不正确");
        }

        // 移除相关的访问令牌
        List<OAuth2AccessTokenDO> accessTokenDOs = oauth2AccessTokenMapper.selectListByRefreshToken(refreshToken);
        if (CollUtil.isNotEmpty(accessTokenDOs)) {
//            oauth2AccessTokenMapper.deleteBatchIds(convertSet(accessTokenDOs, OAuth2AccessTokenDO::getId));
            oauth2AccessTokenMapper.delete(Wrappers.<OAuth2AccessTokenDO>lambdaQuery()
                    .in(OAuth2AccessTokenDO::getId, convertSet(accessTokenDOs, OAuth2AccessTokenDO::getId))
                    .in(BaseDO::getDeleted, Arrays.asList(0, 1)));
            //清楚redis中访问令牌数据
            oauth2AccessTokenRedisDAO.deleteList(convertSet(accessTokenDOs, OAuth2AccessTokenDO::getAccessToken));
        }
        // 已过期的情况下，删除刷新令牌
        if (DateUtils.isExpired(refreshTokenDO.getExpiresTime())) {
            oauth2RefreshTokenMapper.deleteByIdNoDel(refreshTokenDO.getId());
        }

        // 创建访问令牌
        return createOAuth2AccessToken(refreshTokenDO, clientDO);
    }

    @Override
    public AUserDO refreshUrlToken(String refreshToken, String accessToken) {
        if (accessToken == null || !accessToken.equals(refreshToken)) {
            throw exception0(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "无效的刷新令牌");
        }
        Map<String, Object> aToken = checkAToken(accessToken);
        if (ObjUtil.isEmpty(aToken)) {
            throw exception0(GlobalErrorCodeConstants.UNAUTHORIZED.getCode(), "刷新令牌已过期");
        }
        //查询用户数据
        String string = aToken.get("UserID").toString();
        return aUserService.getAUserInfo(string);
    }

    /**
     * 获取当前token
     */
    @Override
    public OAuth2AccessTokenDO getNowToken(String userId) {
        List<OAuth2AccessTokenDO> oAuth2AccessTokenDOS = oauth2AccessTokenMapper.selectList(Wrappers.<OAuth2AccessTokenDO>lambdaQuery()
                .eq(OAuth2AccessTokenDO::getUserId, userId)
                .orderBy(true, false, BaseDO::getCreateTime));
        return oAuth2AccessTokenDOS.get(0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> checkAccountGetToken(String account, HttpServletRequest request) {
        String remoteAddr = getClientIP();
        // 校验白名单，获取对照表
        Map<String, Object> dzbInfo = checkAndGetDzbInfo(account, remoteAddr);
        String workNo = dzbInfo.get("KMYWorkNo").toString();
        AUserDO aUserDO = aUserService.getUserSimpInfoByWorkNo(workNo);
        //获取token
        Map<String, Object> nowToken = getUserNowToken(aUserDO.getId(), 2, "djt");
        Map<String, Object> resMap = new HashMap<>();
        resMap.put("Token", nowToken.get("Token"));
        aUserDO.setPassword(null);
        resMap.put("UserInfo", aUserDO);
        resMap.put("ExpiresTime", nowToken.get("ExpiresTime"));
        //记录日志
        commonService.insertLog(aUserDO, remoteAddr, "用户于{}登录了移动端");
        oauth2AccessTokenMapper.clearExpiredTokenByUserId(aUserDO.getId());
        return resMap;
    }

    /**
     * 扫码回调处理
     */
    @Override
    public Boolean checkQRcCode(String account, String remoteAddr, String uuId) {
        Map<String, Object> dzbInfo = checkAndGetDzbInfo(account, remoteAddr);

        String workNo = dzbInfo.get("KMYWorkNo").toString();
        AUserDO aUserDO = aUserService.getUserSimpInfoByWorkNo(workNo);

        //获取token
        AuthLoginRespVO loginRespVO = adminAuthService.createTokenAfterLoginSuccess(aUserDO.getId(), aUserDO.getLoginName(), LoginLogTypeEnum.LOGIN_QRCODE);

        //设置数据内存处理
        QRCodeConstant.setUserToken(uuId, loginRespVO);
        QRCodeConstant.setQRCodeStatus(uuId, QRCodeConstant.QR_SUCCESS);

        //记录日志
        commonService.insertLog(aUserDO, remoteAddr, "用户于{}通过电建通扫码登录门户系统");

        return !ObjUtil.isEmpty(loginRespVO.getAccessToken());
    }

    @Override
    public Map<String, Object> getCurSessionOa(String token, String userIp) {
        Map<String, Object> result = new HashMap<>();
        OAuth2AccessTokenDO accessToken = getAccessTokenUserIP(token, userIp);

        if (accessToken == null) {
            result.put("msg", "Token令牌不存在!");
            result.put("loginfo", null);
            return result;
        }

        if (DateUtils.isExpired(accessToken.getExpiresTime())) {
            result.put("msg", "Token令牌验证失败!Token已过期!");
            result.put("loginfo", null);
            return result;
        }

        if (accessToken.getUserInfo() == null) {
            result.put("msg", "Token令牌验证失败!用户信息为空!");
            result.put("loginfo", null);
            return result;
        }

        String workNo = accessToken.getUserInfo().get("WorkNo");
        AUserDO aUser = aUserService.getByUserName(workNo);

        if (aUser == null) {
            result.put("msg", "Token令牌验证失败!未找到用户信息!");
            result.put("loginfo", null);
            return result;
        }
        UserInfo userInfo = buildUserInfo(aUser);
        result.put("msg", "Token令牌验证成功!");
        result.put("loginfo", Collections.singletonMap("userInfo", userInfo));
        return result;
    }

    public OAuth2AccessTokenDO getAccessTokenUserIP(String accessToken, String userIp) {
        // 优先从 Redis 中获取
        OAuth2AccessTokenDO accessTokenDO = oauth2AccessTokenRedisDAO.get(accessToken);
        if (accessTokenDO != null) {
            return accessTokenDO.getUserIp().equals(userIp) ? accessTokenDO : null;
        }
        // 获取不到，从 MySQL 中获取
        accessTokenDO = oauth2AccessTokenMapper.selectByAccessToken(accessToken);

        // 如果在 MySQL 存在，则往 Redis 中写入
        if (accessTokenDO != null && !DateUtils.isExpired(accessTokenDO.getExpiresTime())) {
            oauth2AccessTokenRedisDAO.set(accessTokenDO);
            return accessTokenDO.getUserIp().equals(userIp) ? accessTokenDO : null;
        }
        //过期清理
        return accessTokenDO.getUserIp().equals(userIp) ? accessTokenDO : null;
    }

    @Override
    public Map<String, String> getTokenById(String id,HttpServletRequest request) {
        String value = configurationHelper.GetSettingValue("GET_TOKEN_BMD");
        List<String> list = Arrays.asList(value.split(","));
        String clientIpAddress = getClientIP();
        if (ObjUtil.isEmpty(list) || !list.contains(clientIpAddress)) {
            throw exception(ErrorCodeConstants.AUTH_BMD_ERROR);
        }
        Map<String, String> tokenMap = new HashMap<>();
        OAuth2AccessTokenDO accessTokenDO = oauth2AccessTokenMapper.getTokenById(id);
        if (accessTokenDO == null) {
            return null;
        } else {
            tokenMap.put("Token", accessTokenDO.getAccessToken());
        }
        return tokenMap;
    }

    @Override
    public Map<String, Object> checkOutSystemToken(String token) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", "1");
        SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core");
        String sql = "SELECT * FROM A_Token_OutSystem WHERE Token = '" + token + "' AND DATEADD(S,ExpireTime,CreateTime) > GETDATE()";
        List<Map<String, Object>> dt_token = sqlHelper.executeReader(sql);
        if (dt_token.isEmpty()){
            result.put("msg", "systoken令牌验证失败!systoken已过期!");
            result.put("code", "0");
            result.put("loginfo", null);
        }else {
            String userID = dt_token.get(0).get("UserID").toString();
            sql = "SELECT * FROM [A_User] WHERE [ID] = '" + userID + "';";
            List<Map<String, Object>> dt_user = sqlHelper.executeReader(sql);
            if (dt_user.isEmpty()){
                result.put("msg", "systoken令牌验证失败!未找到用户信息!");
                result.put("code", "0");
                result.put("loginfo", null);
            }else {
                var user = dt_user.get(0);
                Map<String, Object> userInfo = new HashMap<>();
                userInfo.put("Name",user.get("Name"));
                userInfo.put("WorkNo",user.get("WorkNo"));
                userInfo.put("DeptID",user.get("DeptID"));
                userInfo.put("DeptName",user.get("DeptName"));
                Map<String, Object> loginfo = new HashMap<>();
                loginfo.put("userInfo",userInfo);
                result.put("msg", "请求成功");
                result.put("loginfo", loginfo);
            }
        }
        return result;
    }

    @Override
    public Boolean IsNotOutSystem(String secretkey,String sys) {
        String bootWebUrl = configurationHelper.GetSettingValue("bootWebUrl");
        if (ObjUtil.isEmpty(bootWebUrl)){
            bootWebUrl = "http://10.10.1.170:8081";
        }
        Map<String, Object> datas = httpPost(bootWebUrl + "/forcwtask/checktoken?sys=" + sys + "&token=" + secretkey,null,null);
        if (datas.isEmpty()){
            return true;
        }else {
            if (datas.get("code").toString().equals("0") && datas.get("data").toString().equals("true")){
                return false;
            }else {
                return true;
            }
        }
    }

    private Map<String, Object> httpPost(String url, Map<String, String> params, Map<String, String> heads) {
        if (url == null || url.isBlank()) {
            throw new BaseException("获取发送code验证码url路径异常");
        }

        // 使用try-with-resources自动关闭资源
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            final HttpPost post = new HttpPost(url);
            if (heads != null && !heads.isEmpty()){
                for (String head : heads.keySet()){
                    post.setHeader(head, heads.get(head));
                }
            }
            if (params != null && !params.isEmpty()) {
                post.setEntity(new StringEntity(JSONUtil.toJsonStr(params), StandardCharsets.UTF_8));
            }

            return client.execute(post, response -> {
                final int statusCode = response.getStatusLine().getStatusCode();
                if (statusCode == HttpStatus.SC_OK) {
                    final HttpEntity entity = response.getEntity();
                    try {
                        return JSONUtil.parseObj(EntityUtils.toString(entity));
                    } finally {
                        EntityUtils.consume(entity);
                    }
                }
                throw new BaseException("响应状态码：" + statusCode);
            });
        } catch (Exception e) {
            throw new BaseException("发送请求发送code,url:" + url + "异常信息：" + e.getMessage());
        }
    }

    private UserInfo buildUserInfo(AUserDO aUser) {
        UserInfo userInfo = new UserInfo();
        userInfo.setId(aUser.getId());
        userInfo.setName(aUser.getName());
        userInfo.setUserName(aUser.getWorkNo());
        userInfo.setLoginName(aUser.getLoginName());
        userInfo.setWorkNo(aUser.getWorkNo());
        userInfo.setDeptID(aUser.getDeptID());
        userInfo.setDeptName(aUser.getDeptName());
        userInfo.setDeptFullID(aUser.getDeptFullID());
        userInfo.setPhone(aUser.getPhone());
        userInfo.setMobilePhone(aUser.getMobilePhone());
        userInfo.setSex(aUser.getSex());
        userInfo.setEmail(aUser.getEmail());
        userInfo.setAddress(aUser.getAddress());
        userInfo.setDuties(aUser.getDuties());
        userInfo.setSortIndex(aUser.getSortIndex());

        return userInfo;
    }

    /**
     * 校验白名单，获取对照表
     *
     * @return
     */
    @Override
    public Map<String, Object> checkAndGetDzbInfo(String account, String remoteAddr) {
        String value = configurationHelper.GetSettingValue("DJT_FWQ_BMD");
        List<String> list = Arrays.asList(value.split(","));
        if (ObjUtil.isEmpty(list) || !list.contains(remoteAddr)) {
            throw exception(ErrorCodeConstants.AUTH_BMD_ERROR);
        }
        String sql = "SELECT KMYWorkNo FROM T_Page_MobileUserSync WHERE DJTUserName= '{0}' AND KMYIsDelete = '0' AND DJTIsDisabled = '1'";
        //根据电建通账号查询用户
        Map<String, Object> resMap = new HashMap<>();
        try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core")) {
            Map<String, Object> firstRow = sqlHelper.selectFirstRow(SQLHelper.format(sql, account));

            if (ObjUtil.isEmpty(firstRow)) {
                throw exception(ErrorCodeConstants.AUTH_DZB_DATA_ERROR);
            }
            return firstRow;
        }
    }

    /**
     * 查询用户当前最新token,过期重新创建一个token
     * isCreate 是否直接创建
     *
     * @return token
     */
    private Map<String, Object> getUserNowToken(String userId, Integer userType, String ClientId) {
        if (ObjUtil.isEmpty(userId)) {
            throw new BaseException("用户ID不能为null");
        }

        OAuth2AccessTokenDO nowToken = getNowToken(userId);

        Map<String, Object> nowTokenMap = new HashMap<>();
        //没有即将过期直接返回
//        if (nowToken.getExpiresTime().isAfter(LocalDateTime.now().plusMinutes(30))) {
//            nowTokenMap.put("Token", nowToken.getAccessToken());
//            nowTokenMap.put("ExpiresTime", nowToken.getExpiresTime());
//            return nowTokenMap;
//        }
        //过期重新创建
        OAuth2AccessTokenDO accessTokenDO = createAccessToken(userId, userType, ClientId, null);
        nowTokenMap.put("Token", accessTokenDO.getAccessToken());
        nowTokenMap.put("ExpiresTime", accessTokenDO.getExpiresTime());
        return nowTokenMap;
    }

    private Map<String, Object> checkAToken(String token) {
        try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core")) {
            String sql = "select * from A_Token where Token = '" + token + "'";
            Map<String, Object> map = sqlHelper.selectFirstRow(sql);
            if (ObjUtil.isEmpty(map)) {
                return null;
            }
            //计算出过期日期
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String createTime = map.get("CreateTime").toString();
            String expireTime = map.get("ExpireTime").toString();
            LocalDateTime parse = LocalDateTime.parse(createTime, formatter);
            LocalDateTime endTime = parse.plusSeconds(Long.parseLong(expireTime));
            if (DateUtils.isExpired(endTime)) {
                return null;
            }
            return map;
        }
    }

    @Override
    public OAuth2AccessTokenDO getAccessToken(String accessToken) {
        // 优先从 Redis 中获取
        OAuth2AccessTokenDO accessTokenDO = oauth2AccessTokenRedisDAO.get(accessToken);
        if (accessTokenDO != null) {
            return accessTokenDO;
        }
        // 获取不到，从 MySQL 中获取
        accessTokenDO = oauth2AccessTokenMapper.selectByAccessToken(accessToken);

        // 如果在 MySQL 存在，则往 Redis 中写入
        if (accessTokenDO != null && !DateUtils.isExpired(accessTokenDO.getExpiresTime())) {
            oauth2AccessTokenRedisDAO.set(accessTokenDO);
            return accessTokenDO;
        }
        //过期清理
        return accessTokenDO;
    }

    @Override
    public OAuth2AccessTokenDO checkAccessToken(String accessToken) {
        OAuth2AccessTokenDO accessTokenDO = getAccessToken(accessToken);
        if (accessTokenDO == null) {
            throw exception0(GlobalErrorCodeConstants.UNAUTHORIZED.getCode(), "访问令牌不存在");
        }
        if (DateUtils.isExpired(accessTokenDO.getExpiresTime())) {
            throw exception0(GlobalErrorCodeConstants.UNAUTHORIZED.getCode(), "访问令牌已过期");
        }
        return accessTokenDO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OAuth2AccessTokenDO removeAccessToken(String accessToken) {
        // 删除访问令牌
        OAuth2AccessTokenDO accessTokenDO = oauth2AccessTokenMapper.selectByAccessToken(accessToken);
        if (accessTokenDO == null) {
            return null;
        }
//        delATokenByUserId(accessTokenDO.getUserId(), accessToken);
        //清理token
        oauth2AccessTokenMapper.deleteByIdNoDel(accessTokenDO.getId());
        oauth2AccessTokenMapper.clearToken(accessTokenDO.getUserId());

        oauth2AccessTokenRedisDAO.delete(accessToken);
        // 删除刷新令牌
        oauth2RefreshTokenMapper.deleteByRefreshToken(accessTokenDO.getRefreshToken());
        return accessTokenDO;
    }

    //删除远程访问令牌
    private void delATokenByUserId(String userId, String accessToken) {
        String tj = !ObjUtil.isEmpty(accessToken) ? " and Token = '" + accessToken + "'" : "";
        String delSql = "DELETE FROM A_Token WHERE UserID = '{0}'" + tj;
        try (SQLHelper sqlHelperCore = SQLHelper.createSqlHelper("Core")) {
            sqlHelperCore.executeNonQuery(SQLHelper.format(delSql, userId));
        }
    }

    @Override
    public PageResult<OAuth2AccessTokenDO> getAccessTokenPage(OAuth2AccessTokenPageReqVO reqVO) {
        return oauth2AccessTokenMapper.selectPage(reqVO);
    }

    private OAuth2AccessTokenDO createOAuth2AccessToken(OAuth2RefreshTokenDO refreshTokenDO, OAuth2ClientDO clientDO) {

        OAuth2AccessTokenDO accessTokenDO = new OAuth2AccessTokenDO()
                .setAccessToken(generateAccessToken())
                .setUserId(refreshTokenDO.getUserId()).setUserType(refreshTokenDO.getUserType())
                .setUserInfo(buildUserInfo(refreshTokenDO.getUserId(), refreshTokenDO.getUserType()))
                .setClientId(clientDO.getClientId())
                .setScopes(refreshTokenDO.getScopes())
                .setRefreshToken(refreshTokenDO.getRefreshToken())
                .setExpiresTime(LocalDateTime.now().plusSeconds(clientDO.getAccessTokenValiditySeconds()));
        accessTokenDO.setTenantId(TenantContextHolder.getTenantId()); // 手动设置租户编号，避免缓存到 Redis 的时候，无对应的租户编号
        accessTokenDO.setUserIp(getClientIP());
        oauth2AccessTokenMapper.insert(accessTokenDO);

//        //插入数据到A_token
//        if (!insertToken(accessTokenDO, refreshTokenDO.getUserId(), clientDO)) {
//            throw new RuntimeException("token插入异常");
//        }

//        //插入数据到sqlserver A_token表
//        if (!insertSqlServerToken(accessTokenDO, refreshTokenDO.getUserId(), clientDO)) {
//            throw new RuntimeException("SqlServer token插入异常");
//        }
        // 记录到 Redis 中
        // 注释redis
        oauth2AccessTokenRedisDAO.set(accessTokenDO);
        return accessTokenDO;
    }

    /**
     * 添加token到数据扩A-Token
     */
    private Boolean insertToken(OAuth2AccessTokenDO accessTokenDO, String userId, OAuth2ClientDO clientDO) {

        //获取日期和时间差
        LocalDateTime now = LocalDateTime.now();
        Integer timeStamp = clientDO.getAccessTokenValiditySeconds();

        String remoteAddr = request.getRemoteAddr();
        //获取UUid
        String id = UUID.randomUUID().toString().replace("_", "");
        String createDate = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core")) {
            //插入前，清除之前用户token信息
            delATokenByUserId(userId, null);

            String sql = "INSERT INTO A_Token (ID,UserID,Token,CreateTime,ExpireTime,UserIP) VALUES ('{0}','{1}','{2}','{3}',{4},'{5}')";
            String format = SQLHelper.format(sql, id, userId, accessTokenDO.getAccessToken(), createDate, timeStamp, remoteAddr);
            int i = sqlHelper.executeNonQuery(format);
            if (i < 1) {
                return false;
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
        return true;
    }

    private Boolean insertSqlServerToken(OAuth2AccessTokenDO accessTokenDO, String userId, OAuth2ClientDO clientDO) {

        //获取日期和时间差
        LocalDateTime now = LocalDateTime.now();
        Integer timeStamp = clientDO.getAccessTokenValiditySeconds();

        String remoteAddr = request.getRemoteAddr();
        //获取UUid
        String id = UUID.randomUUID().toString().replace("_", "");
        String createDate = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("SqlServer")) {
            //插入前，清除之前用户token信息
            delATokenByUserId(userId, null);

            String sql = "INSERT INTO A_Token (ID,UserID,Token,CreateTime,ExpireTime,UserIP,Source) VALUES ('{0}','{1}','{2}','{3}',{4},'{5}',1)";
            String format = SQLHelper.format(sql, id, userId, accessTokenDO.getAccessToken(), createDate, timeStamp, remoteAddr);
            int i = sqlHelper.executeNonQuery(format);
            if (i < 1) {
                return false;
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
        return true;
    }

    private OAuth2RefreshTokenDO createOAuth2RefreshToken(String userId, Integer userType, OAuth2ClientDO clientDO, List<String> scopes) {
        OAuth2RefreshTokenDO refreshToken = new OAuth2RefreshTokenDO().setRefreshToken(generateRefreshToken())
                .setUserId(userId).setUserType(userType)
                .setClientId(clientDO.getClientId()).setScopes(scopes)
                .setExpiresTime(LocalDateTime.now().plusSeconds(clientDO.getRefreshTokenValiditySeconds()));
        oauth2RefreshTokenMapper.insert(refreshToken);
        return refreshToken;
    }

    /**
     * 加载用户信息，方便 {@link mh.cloud.framework.security.core.LoginUser} 获取到昵称、部门等信息
     *
     * @param userId   用户编号
     * @param userType 用户类型
     * @return 用户信息
     */
    private Map<String, String> buildUserInfo(String userId, Integer userType) {
        if (userType.equals(UserTypeEnum.ADMIN.getValue())) {
            AUserDO aUser = aUserService.getUserSimpInfo(userId);
            return MapUtil.builder(LoginUser.WORK_NO, aUser.getWorkNo())
                    .put(LoginUser.INFO_KEY_NICKNAME, aUser.getName())
                    .put(LoginUser.INFO_KEY_DEPT_ID, StrUtil.toStringOrNull(aUser.getDeptID()))
                    .put(LoginUser.DUTIES, aUser.getDuties()).build();
        } else if (userType.equals(UserTypeEnum.MEMBER.getValue())) {
            // 注意：目前 Member 暂时不读取，可以按需实现
            return Collections.emptyMap();
        }
        return null;
    }
}
