package mh.cloud.module.system.controller.admin.pagenewscentermenu.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 菜单管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PageNewscentermenuRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20270")
    @ExcelProperty("ID")
    private String id;

    @Schema(description = "创建人")
    @ExcelProperty("创建人")
    private String createUser;

    @Schema(description = "创建人ID", example = "1589")
    @ExcelProperty("创建人ID")
    private String createUserID;

    @Schema(description = "创建日期")
    @ExcelProperty("创建日期")
    private LocalDateTime createTime;

    @Schema(description = "修改人")
    @ExcelProperty("修改人")
    private String modifyUser;

    @Schema(description = "修改人ID", example = "21043")
    @ExcelProperty("修改人ID")
    private String modifyUserID;

    @Schema(description = "修改日期")
    @ExcelProperty("修改日期")
    private LocalDateTime modifyTime;

    @Schema(description = "流程状态")
    @ExcelProperty("流程状态")
    private String flowPhase;

    @Schema(description = "流程步骤")
    @ExcelProperty("流程步骤")
    private String flowStep;

    @Schema(description = "流程结束日期")
    @ExcelProperty("流程结束日期")
    private LocalDateTime flowCompleteTime;

    @Schema(description = "是否删除")
    @ExcelProperty("是否删除")
    private String isDeleted;

    @Schema(description = "状态", example = "2")
    @ExcelProperty("状态")
    private String status;

    @Schema(description = "所有签字、意见")
    @ExcelProperty("所有签字、意见")
    private String signTemp;

    @Schema(description = "部门ID", example = "4078")
    @ExcelProperty("部门ID")
    private String deptID;

    @Schema(description = "部门", example = "王五")
    @ExcelProperty("部门")
    private String deptName;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String description;

    @Schema(description = "经手人")
    @ExcelProperty("经手人")
    private String flowHandler;

    @Schema(description = "经手人ID", example = "14074")
    @ExcelProperty("经手人ID")
    private String flowHandlerID;

    @Schema(description = "是否当前版本")
    @ExcelProperty("是否当前版本")
    private String isCurrent;

    @Schema(description = "版本号")
    @ExcelProperty("版本号")
    private Integer versionNumber;

    @Schema(description = "修改记录大字段")
    @ExcelProperty("修改记录大字段")
    private String modifyRecordText;

    @Schema(description = "资讯中心菜单名称", example = "王五")
    @ExcelProperty("资讯中心菜单名称")
    private String catalogName;

    @Schema(description = "排序号")
    @ExcelProperty("排序号")
    private String sortIndex;

    @Schema(description = "是否启用")
    @ExcelProperty("是否启用")
    private String isEnabled;

    @Schema(description = "设置组织")
    @ExcelProperty("设置组织")
    private String departmentInfo;

    @Schema(description = "设置组织名称", example = "张三")
    @ExcelProperty("设置组织名称")
    private String departmentInfoName;

    @Schema(description = "设置平台角色")
    @ExcelProperty("设置平台角色")
    private String roleInfo;

    @Schema(description = "设置平台角色名称", example = "luohang")
    @ExcelProperty("设置平台角色名称")
    private String roleInfoName;

    @Schema(description = "设置新建角色")
    @ExcelProperty("设置新建角色")
    private String selfBuildRoleInfo;

    @Schema(description = "设置新建角色名称", example = "李四")
    @ExcelProperty("设置新建角色名称")
    private String selfBuildRoleInfoName;

    @Schema(description = "获取列表数据")
    @ExcelProperty("获取列表数据")
    private String listSql;

    @Schema(description = "获取数据详情")
    @ExcelProperty("获取数据详情")
    private String detailSql;

    @Schema(description = "编码")
    @ExcelProperty("编码")
    private String code;

    @Schema(description = "序号")
    @ExcelProperty("序号")
    private String orderNumber;

    @Schema(description = "浏览次数")
    @ExcelProperty("浏览次数")
    private String viewCountSql;

    @Schema(description = "资讯中心菜单名称", example = "luohang")
    @ExcelProperty("资讯中心菜单名称")
    private String menuName;

}