package mh.cloud.module.system.service.portal.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import mh.cloud.framework.common.exception.ServiceException;
import mh.cloud.framework.security.core.util.SecurityFrameworkUtils;
import mh.cloud.framework.tenant.core.context.TenantContextHolder;
import mh.cloud.module.system.controller.admin.portal.V0.SysLayoutDataReq;
import mh.cloud.module.system.controller.admin.portal.V0.SysLayoutDataVo;
import mh.cloud.module.system.controller.admin.portal.V0.SysLayoutVo;
import mh.cloud.module.system.dal.mysql.portal.LayoutDataMapper;
import mh.cloud.module.system.dal.mysql.portal.LayoutMapper;
import mh.cloud.module.system.service.portal.LayoutDataService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class LayoutDataServiceImpl extends ServiceImpl<LayoutDataMapper, SysLayoutDataVo> implements LayoutDataService {

    @Resource
    private LayoutMapper layoutMapper;

    /**
     * 获取布局配置数据
     *
     * @param category 布局数据分类
     */
    @Override
    public Object getLayoutData(String category) {

        List<SysLayoutDataVo> voList = lambdaQuery().eq(SysLayoutDataVo::getCategory, category).list();

        return voList.stream().map(item -> {
            SysLayoutDataReq bean = BeanUtil.toBean(item, SysLayoutDataReq.class);
            JSONObject contentJson = JSONUtil.parseObj(item.getContent());
            bean.setItemJson(contentJson);
            return bean;
        }).toList();
    }

    /**
     * 保存布局配置数据
     */
    @Override
    public Boolean saveLayoutData(SysLayoutDataReq sysLayoutReq) {
        SysLayoutDataVo layoutVo = BeanUtil.toBean(sysLayoutReq, SysLayoutDataVo.class);
        String contentStr = JSONUtil.toJsonStr(sysLayoutReq.getItemJson());
        String layoutStr = JSONUtil.toJsonStr(sysLayoutReq.getLayoutJson());
        layoutVo.setContent(contentStr);
        layoutVo.setDefaultLayout(layoutStr);

        LocalDateTime now = LocalDateTime.now();
        layoutVo.setCreateTime(now);
        layoutVo.setUpdateTime(now);

        String userId = SecurityFrameworkUtils.getLoginUserId();
        layoutVo.setUserId(userId);
        Long tenantId = TenantContextHolder.getTenantId();
        layoutVo.setTenantId(tenantId.toString());

        return this.save(layoutVo);
    }

    /**
     * 修改布局配置数据
     */
    @Override
    public Boolean updateLayoutData(SysLayoutDataReq sysLayoutReq) {
        if (ObjUtil.isEmpty(sysLayoutReq.getId())) {
            throw new ServiceException(400, "请求参数携带异常");
        }
        SysLayoutDataVo layoutVo = BeanUtil.toBean(sysLayoutReq, SysLayoutDataVo.class);
        String contentStr = JSONUtil.toJsonStr(sysLayoutReq.getItemJson());
        String layoutStr = JSONUtil.toJsonStr(sysLayoutReq.getLayoutJson());
        layoutVo.setContent(contentStr);
        layoutVo.setDefaultLayout(layoutStr);


        layoutVo.setUpdateTime(LocalDateTime.now());

        String userId = SecurityFrameworkUtils.getLoginUserId();
        layoutVo.setUserId(userId);

        return this.updateById(layoutVo);
    }

    /**
     * 删除布局配置数据
     */
    @Override
    public Boolean deleteLayoutData(String id) {
        if (this.removeById(id)) {
            //去除布局中包含该组件数据
            return layoutMapper.delete(Wrappers.<SysLayoutVo>lambdaQuery().eq(SysLayoutVo::getDataId, id)) > 0;
        }
        throw new ServiceException(500, "删除异常");
    }


    /**
     * 获取对应默认配置数据
     *
     * @param category
     * @param type
     * @param userId
     * @param group
     */
    @Override
    public List<Map<String, Object>> getGroupLayoutList(String category, String type, String userId, String group) {
        List<Map<String, Object>> mapList = new ArrayList<>();
        if (type.equals("AUTH")) {
            //根据权限分组查询布局
            mapList = layoutMapper.getGroupLayoutList(category, group);

            //若没有权限则显示默认布局配置，配置id设置为null
            if (ObjUtil.isEmpty(mapList)) {
                mapList = layoutMapper.getGroupLayoutList(category, "DEFAULT");

                mapList.forEach(map -> {
                    map.put("id", null);
                    map.put("group_code",group);
                });
            }
        } else if (type.equals("USER")) {
            //根据用户查询布局
            mapList = layoutMapper.getLayout(category, userId, group);
        }
        return mapList;
    }
}
