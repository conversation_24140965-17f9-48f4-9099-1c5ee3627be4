package mh.cloud.module.system.service.uiList;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import mh.cloud.module.system.controller.admin.uiList.vo.*;
import mh.cloud.module.system.dal.dataobject.uiList.ListDO;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.pojo.PageParam;
import mh.cloud.framework.common.util.object.BeanUtils;

import mh.cloud.module.system.dal.mysql.uiList.ListMapper;

import static mh.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static mh.cloud.module.system.enums.ErrorCodeConstants.*;

/**
 * 列表配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ListServiceImpl implements ListService {

    @Resource
    private ListMapper listMapper;

    @Override
    public String createList(ListSaveReqVO createReqVO) {
        // 插入
        ListDO list = BeanUtils.toBean(createReqVO, ListDO.class);
        listMapper.insert(list);
        // 返回
        return list.getId();
    }

    @Override
    public void updateList(ListSaveReqVO updateReqVO) {
        // 校验存在
        validateListExists(updateReqVO.getId());
        // 更新
        ListDO updateObj = BeanUtils.toBean(updateReqVO, ListDO.class);
        listMapper.updateById(updateObj);
    }

    @Override
    public void deleteList(String id) {
        // 校验存在
        validateListExists(id);
        // 删除
        listMapper.deleteById(id);
    }

    private void validateListExists(String id) {
        if (listMapper.selectById(id) == null) {
            throw exception(LIST_NOT_EXISTS);
        }
    }

    @Override
    public ListDO getList(String id) {
        return listMapper.selectById(id);
    }

    @Override
    public PageResult<ListDO> getListPage(ListPageReqVO pageReqVO) {
        return listMapper.selectPage(pageReqVO);
    }

}