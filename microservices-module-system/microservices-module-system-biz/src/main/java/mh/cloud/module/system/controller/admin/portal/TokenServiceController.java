package mh.cloud.module.system.controller.admin.portal;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import cn.hutool.extra.servlet.ServletUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.Servlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import mh.cloud.framework.common.pojo.AuthLoginResult;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.framework.common.util.servlet.ServletUtils;
import mh.cloud.framework.security.config.SecurityProperties;
import mh.cloud.framework.security.core.util.SecurityFrameworkUtils;
import mh.cloud.framework.tenant.core.context.TenantContextHolder;
import mh.cloud.module.system.controller.admin.auth.vo.AuthLoginReqVO;
import mh.cloud.module.system.controller.admin.oauth2.vo.open.OAuth2OpenCheckTokenRespVO;
import mh.cloud.module.system.controller.admin.portal.V0.AuthLoginVo;
import mh.cloud.module.system.controller.admin.user.vo.user.CursessionOA3UserVO;
import mh.cloud.module.system.convert.oauth2.OAuth2OpenConvert;
import mh.cloud.module.system.dal.dataobject.auser.AUserDO;
import mh.cloud.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import mh.cloud.module.system.enums.logger.LoginLogTypeEnum;
import mh.cloud.module.system.service.auser.AUserService;
import mh.cloud.module.system.service.auth.AdminAuthService;
import mh.cloud.module.system.service.oauth2.OAuth2TokenService;
import mh.cloud.module.system.service.oauth2.UserSessionService;
import mh.cloud.module.system.util.SymmetricEncryptionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.HashMap;
import java.util.Map;

import static mh.cloud.framework.common.pojo.AuthLoginResult.authSuccess;
import static mh.cloud.framework.common.pojo.AuthLoginResult.authSuccessAndCodeIs1;
import static mh.cloud.framework.common.pojo.CommonResult.*;
import static mh.cloud.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

@Tag(name = "token管理外部服务接口")
@RestController
@RequestMapping("/auth")
@Validated
@Slf4j
public class TokenServiceController {
    @Resource
    private SecurityProperties securityProperties;
    @Resource
    private OAuth2TokenService oauth2TokenService;
    @Resource
    private AdminAuthService authService;

    @Resource
    private AUserService aUserService;


    @Resource
    private UserSessionService userSessionService;

    //    @PostMapping("/cursessionOA")
//    @PermitAll
//    @Operation(summary = "通过x-www-form-urlencoded传token")
//    @Parameter(name = "token", required = true, description = "访问令牌", example = "biu")
//    public CommonResult<OAuth2OpenCheckTokenRespVO> checkToken(@RequestParam("token") String token) {
//        // 校验令牌
//        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.checkAccessToken(token);
//        Assert.notNull(accessTokenDO, "访问令牌不能为空"); // 防御性检查
//        return success(OAuth2OpenConvert.INSTANCE.convert2(accessTokenDO));
//    }
    @PostMapping("/cursessionOA")
    @PermitAll
    @Operation(summary = "通过x-www-form-urlencoded传token")
    @Parameter(name = "token", required = true, description = "访问令牌", example = "biu")
    public AuthLoginResult<Object> checkToken() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();
        String token = SecurityFrameworkUtils.obtainAuthorization(request, "Authorization", "token");
        if (ObjUtil.isEmpty(token)) {
            log.error("token：请求头参数缺失");
            return authSuccess(null, "token不能为空。");
        }
        String userIp = JakartaServletUtil.getHeaderIgnoreCase(request,"UserIP");
        if (StrUtil.isEmpty(userIp)) {
            userIp = request.getParameter("UserIP");
        }
        if (ObjUtil.isEmpty(token)) {
            log.error("token：请求头参数缺失");
            return authSuccess(null, "token不能为空。");
        }
        if (StrUtil.isEmpty(userIp)) {
            log.error("UserIP：请求头参数缺失");
            return authSuccess(null, "UserIP不能为空。");
        }
        Map<String, Object> curSessionOa = oauth2TokenService.getCurSessionOa(token,userIp);
        String msg = (String) curSessionOa.get("msg");
        Object userInfo = curSessionOa.get("loginfo");
        // 校验令牌
        return authSuccessAndCodeIs1(userInfo, msg);
    }

    @GetMapping("/cursessionOA2")
    @PermitAll
    @Operation(summary = "通过？后拼接参数传token")
    @Parameter(name = "token", required = true, description = "访问令牌", example = "biu")
    public CommonResult<OAuth2OpenCheckTokenRespVO> checkToken2(String token) {
        // 校验令牌
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.checkAccessToken(token);
        Assert.notNull(accessTokenDO, "访问令牌不能为空"); // 防御性检查
        return success(OAuth2OpenConvert.INSTANCE.convert2(accessTokenDO));
    }

    @GetMapping("/cursessionOA3")
    @Operation(summary = "通过header传token,安全系数高")
    public CommonResult<CursessionOA3UserVO> checkToken3(@RequestHeader(value = "Authorization", required = false) String authorizationHeader) {
        // 检查Authorization头是否存在
        if (authorizationHeader == null || authorizationHeader.isEmpty()) {
            return error(401, "Authorization头不能为空");
        }
        // 从Authorization头中提取token
        if (!authorizationHeader.startsWith("Bearer ")) {
            return error(401, "Authorization头必须以Bearer开头");
        }
        String loginUserId = getLoginUserId();
        String token = authorizationHeader.substring(7).trim(); // 去掉"Bearer "前缀
        oauth2TokenService.checkAccessToken(token);
        AUserDO aUser = aUserService.getAUser(loginUserId);
        CursessionOA3UserVO user = new CursessionOA3UserVO(aUser);
        user.setToken(token);
        return success(user);
    }

    @PostMapping("/checkAccountGetToken")
    @Operation(summary = "通过白名单校验，电建通账号，获取用户token")
    @PermitAll
    public CommonResult<Object> checkAccountGetToken(@RequestBody Map<String, String> accountMap, HttpServletRequest request) {
        //获取白名单校验
        String account = accountMap.get("account");
        return success(oauth2TokenService.checkAccountGetToken(account, request));
    }

    @PostMapping("/checkQRcCode")
    @Operation(summary = "扫一扫登录授权验证，白名单校验")
    @PermitAll
    public CommonResult<Boolean> checkQRcCode(@RequestBody Map<String, String> parameter, HttpServletRequest request) {
        //获取白名单校验
        String account = parameter.get("account");
        String uuId = parameter.get("uuId");
        String remoteAddr = request.getRemoteAddr();
        return success(oauth2TokenService.checkQRcCode(account, remoteAddr, uuId));
    }

    @GetMapping("/writeToken")
    @PermitAll
    @Operation(summary = "写入token,请求头携带加密参数")
    public CommonResult<Object> writeToken(HttpServletRequest request) throws Exception {
        String encrypted = request.getHeader("encrypted");
        if (ObjUtil.isEmpty(encrypted)) {
            throw new MissingServletRequestParameterException("encrypted：请求头参数缺失", "String");
        }
        String decrypt = SymmetricEncryptionUtils.decrypt(encrypted, SymmetricEncryptionUtils.KEY);
        if (ObjUtil.isEmpty(decrypt)) {
            throw new MissingServletRequestParameterException("decrypt：请求头参数缺失", "String");
        }
        TenantContextHolder.setTenantId(1L);
        return success(authService.writeToken(decrypt));
    }

    @GetMapping("/getTokenById")
    @Parameter(name = "token", required = true, description = "访问令牌", example = "biu")
    @PermitAll
    @Operation(summary = "通过id获取token")
    public CommonResult<Object> getTokenById(String UserId,HttpServletRequest request) {
        if(UserId == null || UserId.isEmpty()) {
            return error(401, "请传入有效数据");
        }
        Map<String, String> tokenById = oauth2TokenService.getTokenById(UserId, request);
        if (tokenById == null) {
            return error(401, "该用户不存在有效token");
        }else{
            return successByCodeIs1(tokenById);
        }
    }


//    @PostMapping("/login")
//    @PermitAll
//    @Operation(summary = "web使用账号密码登录")
//    public CommonResult<AuthLoginRespVO> weblogin(@RequestBody @Valid AuthLoginReqVO reqVO) {
//        //默认xi
//        TenantContextHolder.setTenantId(1L);
//        TenantContextHolder.setIgnore(false);
//        return success(authService.login(reqVO));
//    }

    @PostMapping("/login")
    @PermitAll
    @Operation(summary = "web使用账号密码登录")
    public AuthLoginResult<Object> weblogin(@RequestBody @Valid AuthLoginReqVO reqVO) {
        //默认xi
        TenantContextHolder.setTenantId(1L);
        TenantContextHolder.setIgnore(false);
        AuthLoginVo authLoginVo = authService.authLogin(reqVO);
        String checkInfo = authLoginVo.getCheckInfo();
        Map<String, AuthLoginVo> userLoginInfo = new HashMap<>();
        userLoginInfo.put("LoginInfo", authLoginVo);
        return authSuccess(userLoginInfo, checkInfo);
    }

    @PostMapping("/logout")
    @PermitAll
    @Operation(summary = "登出系统")
    public CommonResult<Boolean> logout(HttpServletRequest request) {
        String token = SecurityFrameworkUtils.obtainAuthorization(request, securityProperties.getTokenHeader(), securityProperties.getTokenParameter());
        if (StrUtil.isNotBlank(token)) {
            authService.logout(token, LoginLogTypeEnum.LOGOUT_SELF.getType());
        }
        return success(true);
    }

    @PostMapping("/updateDjtUserLoginInfo")
    @Operation(summary = "扫码登录，电建通第三方应用跳转请求，通过【白名单】+【电建通账号】校验，获取用户工号，修改登录状态为已登录、记录登录时间")
    @PermitAll
    public CommonResult<Boolean> updateDjtUserLoginInfo(@RequestBody Map<String, String> parameter, HttpServletRequest request) {
        String account = parameter.get("account");//电建通账号
        String uuId = parameter.get("uuId");//uuid
        String remoteAddr = request.getRemoteAddr();//用户ip

        //检测ip地址白名单,并且获取用户信息
        Map<String, Object> userInfo = oauth2TokenService.checkAndGetDzbInfo(account, remoteAddr);
        if (userInfo.isEmpty()) {
            return success(false);
        }

        //更新扫码登录状态、记录登录时间、记录登录工号
        boolean is_success = userSessionService.updateLoginStatus(uuId, account, userInfo);

        //返回状态
        return success(is_success);
    }
}
