package mh.cloud.module.system.service.crawler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import mh.cloud.framework.common.exception.BaseException;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.module.system.dal.dataobject.IPublicInformation.IPublicInformationDo;
import mh.cloud.module.system.dal.mysql.IPublicInformation.IPublicInformationMapper;
import mh.cloud.module.infra.api.file.FileApi;
import mh.cloud.module.infra.api.file.dto.FileCreateReqDTO;
import mh.cloud.module.system.api.portal.SystemApi;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Iterator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 后台用户 Service 实现类
 */
@Service
@Slf4j
@Data
public class CrawlerToolServiceImpl implements CrawlerToolService {


    public final String baseUrl = "https://www.powerchina.cn";
    @Resource
    private IPublicInformationMapper publicInformationMapper;

    @Resource
    private FileApi fileApi;

    @Resource
    private SystemApi systemApi;


    private String getHtml(String htmlUrl) {
        String value = systemApi.getSettingValue("ExteriorService");
        String url = value + "/external/crawler/getScriptToHtml?url=" + htmlUrl;
        return (String) toGet(url);
    }

    private Document getDocument(String htmlUrl) {
        String value = systemApi.getSettingValue("ExteriorService");
        String url = value + "/external/crawler/getDocument?url=" + htmlUrl;
        Object object = toGet(url);
        return BeanUtil.toBean(object, Document.class);
    }

    private Object toGet(String url) {
        String string = HttpUtil.get(url);
        JSONObject jsonObject = JSONUtil.parseObj(string);
        Integer code = (Integer) jsonObject.get("code");
        Object data = jsonObject.get("data");
        if (!ObjUtil.isEmpty(code) && code == 200) {
            return data;
        }
        throw new BaseException("爬取数据访问服务，{} 数据请求异常" + url);
    }


    @Override
    public Integer getNews(Integer obj, String htmlUrl) {
        // 使用Jsoup获取HTML文档
        String htmlStr = getHtml(htmlUrl);
        Document document = Jsoup.parse(htmlStr);
        Integer insCount = 0;
        Elements li_tags = document.select("li");
        for (Element li_tag : li_tags) {
            Integer num = addIPublicInformation(obj, li_tag);
            insCount += num;
        }
        return insCount;
    }

    /**
     * 根据新闻时间判断是否继续解析
     */
    public String IsToday(Element li_tag) {
        Elements span_tags = li_tag.select("span");
        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);
        // 格式化日期为字符串，用于SQL查询
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String todayStr = today.format(formatter);
        String yesterdayStr = yesterday.format(formatter);//yesterday.format(formatter); "2024-12-31";
        String xqsj = "";//新闻时间
        for (Element span_tag : span_tags) {
            if (span_tag.html().contains("时间")) {
                xqsj = span_tag.html().substring(3);//新闻时间
                if (!yesterday.isAfter(LocalDate.parse(xqsj, formatter)) && !today.isBefore(LocalDate.parse(xqsj, formatter))) {
                    //仅处理当前页 中昨天和今天的新闻数据
                    Elements a_tags = li_tag.select("a.title");
                    for (Element a_tag : a_tags) {
                        String title = a_tag.html();
                        List<IPublicInformationDo> informationDos = publicInformationMapper
                                .selectList(Wrappers.<IPublicInformationDo>
                                                lambdaQuery().eq(IPublicInformationDo::getTitle, title)
                                        .eq(IPublicInformationDo::getSource, "电建官网")
                                        .between(IPublicInformationDo::getReleaseDate, yesterdayStr + " 00:00:00", todayStr + " 23:59:59"));
                        if (!ObjUtil.isEmpty(informationDos)) {
                            return null;
                        }
                    }
                    return xqsj;
                }
            }
        }
        return null;
    }

    public Integer addIPublicInformation(Integer obj, Element li_tag) {
        Elements a_tags = li_tag.select("a.title");
        String xqsj = IsToday(li_tag);
        if (xqsj != null) {
            for (Element a_tag : a_tags) {
                String title = a_tag.html();
                String a_href = a_tag.attr("href");
                if (!a_href.contains("http")) {
                    String href = baseUrl + a_tag.attr("href");
                    Document XQHTML = null;
                    XQHTML = getDocument(href);

                    Elements xq_tags = XQHTML.select("td.dj_at");
                    // 处理备注标签的正则表达式
                    String htmlCommentsRegex = "<!--(.|[\\r\\n])*?-->";
                    Pattern htmlCommentsPattern = Pattern.compile(htmlCommentsRegex, Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
                    Elements btContentTags = XQHTML.select("td.bt_content");
                    String xqnr = btContentTags.get(0).html();
                    // 假设我们需要从另一个具有特定class的<td>标签中提取内容并去除备注
                    if (!btContentTags.isEmpty()) {
                        String btContentHtml = btContentTags.get(0).html();
                        Matcher matcher = htmlCommentsPattern.matcher(btContentHtml);
                        xqnr = matcher.replaceAll("");
                    }

                    String xqzz = "";
                    for (Element xq_tag : xq_tags) {
                        String xqtitle = xq_tag.html();
                        if (xqtitle.contains("来源")) {
                            // 提取我们需要的部分
                            xqzz = xqtitle.substring(3);
                        }
                        if (xqtitle.contains("作者")) {
                            xqzz = xqtitle.substring(3);
                        }

                        Elements xqa_tags = btContentTags.get(0).select("a");
                        for (Element xqa_tag : xqa_tags) {
                            String SaveSrc = "";
                            String fullUrl = "";
                            Elements xqphotos_tags = xqa_tag.select("img");
                            for (Element xqphoto_tag : xqphotos_tags) {
                                String srcAttr = xqphoto_tag.attr("src");
                                fullUrl = srcAttr.startsWith("https://") || srcAttr.startsWith("http://") ? srcAttr : baseUrl + srcAttr;
                                //下载图片方法
                                SaveSrc = downloadPhots(fullUrl, srcAttr);
//                                if (!fullUrl.contains("http")) {
//                                    // SaveSrc = "http://"+Request.Url.Host + ":" + Request.Url.Port + SaveSrc;
//                                }
                                if (fullUrl.contains(xqphoto_tag.attr("src"))) {
                                    xqnr = xqnr.replaceAll(xqphoto_tag.attr("src"), SaveSrc);
                                }
                            }
                            if (xqnr.contains(xqa_tag.attr("href"))) {
                                xqnr = xqnr.replaceAll(xqa_tag.attr("href"), SaveSrc);
                            }
                        }
                        xqnr = xqnr.replaceAll("•", "●");

                        return getPublication(obj, title, xqnr, xqzz, xqsj, null);
                    }
                } else {
                    return getPublication(obj, title, null, null, xqsj, a_href);
                }
            }
        }
        return 0;
    }

    public Integer getPublication(Integer type, String title, String xqnr, String xqzz, String xqsj, String href) {

        List<IPublicInformationDo> list = publicInformationMapper.selectList(Wrappers.<IPublicInformationDo>
                        lambdaQuery().eq(IPublicInformationDo::getTitle, title)
                .eq(IPublicInformationDo::getSource, "电建官网")
                .lt(IPublicInformationDo::getReleaseDate, LocalDate.now().minusDays(2) + " 00:00:00"));
//        查询之前数据是否有系相同数据
        if (!ObjUtil.isEmpty(list)) {
            //移除数据
            List<String> ids = list.stream().map(IPublicInformationDo::getID).toList();
            publicInformationMapper.deleteBatchIds(ids);
        }
        IPublicInformationDo obj = new IPublicInformationDo();
        if (type == 0) {
            obj.setCatalogId("ad590096-adf5-4bc2-97aa-6d88422d26bc");
            obj.setCataLogName("集团要闻");
        } else {
            obj.setCatalogId("ad590097-319b-4c5f-88d1-9a709337ca9e");
            obj.setCataLogName("时政新闻");
        }
        obj.setIsTop("0");
        obj.setFlowPhase("Complete");
        obj.setPublishState("发布");
        obj.setIsExtranet("1");
        obj.setIsImage("0");
        obj.setSource("电建官网");
        obj.setIsDelete("0");
        obj.setNewsType("门户新闻");

        obj.setContent(xqnr);
        obj.setCreateTime(DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss"));
        obj.setTitle(title);
        obj.setDeputyTitle(title);
        obj.setAuthorName(xqzz);
        obj.setReleaseDate(xqsj);

        obj.setReleaseDate(xqsj);
        obj.setOtherWebAddres(href);
        return publicInformationMapper.insert(obj);

    }


    /**
     * 图片下载
     */
    public String downloadPhots(String fullUrl, String srcAttr) {

        byte[] content = HttpUtil.downloadBytes(fullUrl);
        String filename = fullUrl.substring(fullUrl.lastIndexOf("/") + 1);
        FileCreateReqDTO reqDTO = new FileCreateReqDTO();
        reqDTO.setContent(content);
        reqDTO.setName(filename);
        CommonResult<Object> result = fileApi.uploadFile(reqDTO);
        Object data = result.getData();
        JSONObject jsonObject = JSONUtil.parseObj(data);
        String name = (String) jsonObject.get("name");
        return systemApi.getSettingValue("downloadFileUrl") + "/BasicApplication/DownloadFile?FileID=" + name;
    }


    /**
     * 解析script标签获取到指定参数
     *
     * @return
     */
    private String getScriptStr(Document document) {
        Elements scripts = document.selectFirst("div.boxx").select("script");
        String reqUrl = "";
        for (Element script : scripts) {
            String url = script.attr("url");
            String queryData = script.attr("querydata");
            if (!ObjUtil.isEmpty(url) && !ObjUtil.isEmpty(queryData)) {
                String params = jsonToUrlParams(queryData);
                reqUrl = baseUrl + url + "?" + params;
                break;
            }
        }
        return reqUrl;
    }

    /**
     * 获取到指定新闻html
     *
     * @param newsName 查询的名称
     * @param document 资讯中心随便一个地址获取到的html数据
     * @return
     */
    private String getNewsHtml(String newsName, Document document) {
        //获取侧边导航栏标签数据
        Elements elements = document.selectFirst("div.lanmu_box")
                .selectFirst("div.listr")
                .select("div.fu");
        //获取侧边栏数据列表
        for (Element element : elements) {
            Element lei = element.selectFirst("div.lei")
                    .selectFirst("div.li_tit").selectFirst("a");

            String id = lei.attr("id");
            if (ObjUtil.equals(id, newsName)) {
                String href = lei.attr("href");
                if (ObjUtil.isEmpty(href)) {
                    log.error("获取指定新闻url路径异常，请及时查看");
                    return null;
                }
                return baseUrl + href;
            }
        }
        return null;
    }

    /**
     * 将参数字符串拼接为url参数
     */
    private String jsonToUrlParams(String querydata) {
        JSONObject jsonObject = JSONUtil.parseObj(querydata);
        StringBuilder urlParams = new StringBuilder();
        Iterator<String> keys = jsonObject.keySet().iterator();
        boolean first = true;
        while (keys.hasNext()) {
            String key = keys.next();
            if (!first) {
                urlParams.append("&");
            } else {
                first = false;
            }
            urlParams.append(key).append("=").append(jsonObject.getStr(key));
        }
        return urlParams.toString();
    }

}
