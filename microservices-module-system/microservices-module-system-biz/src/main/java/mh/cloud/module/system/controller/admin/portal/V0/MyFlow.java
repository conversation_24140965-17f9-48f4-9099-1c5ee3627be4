package mh.cloud.module.system.controller.admin.portal.V0;

import lombok.Data;

@Data
public class MyFlow {

    private String ID;

    private String CreateUser;

    private String CreateUserID;

    private String CreateTime;

    private String FlowID;

    private String FlowName;

    private String Remark;

    private String TaskID;

    private String FormInstanceID;

    private String SysType;

    private String URL;

    private String ActivityName;

    private String GoodWaySoft;


//    ===============================
    private String TaskName;

    private String OriginID;

    private String SystemName;

    private String ViewUrl;

//    =========================
    private String DefFlowCode;

    private String Title;

    private String SysPT;

//    ==============
    private String ExecURL;
}
