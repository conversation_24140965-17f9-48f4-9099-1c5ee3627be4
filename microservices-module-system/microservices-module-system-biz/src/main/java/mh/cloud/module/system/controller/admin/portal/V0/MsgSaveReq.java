package mh.cloud.module.system.controller.admin.portal.V0;

import lombok.Data;

@Data
public class MsgSaveReq {
    //回复ID
    private String ReplyID;
    //方法
    private String Func;
    //文件id
    private String AttachFileIDs;
    //消息内容
    private String Content;
    //消息内容
    private String ContentText;
    //删除时间
    private String DeleteTime;
    //ID
    private String ID;
    //是否重要
    private String Importance;
    //是否删除
    private String IsDeleted;
    //是否已读回复
    private String IsReadReceipt;
    //发送删除
    private String IsSenderDeleted;
    //是否系统消息
    private String IsSystemMsg;
    //外链接
    private String LinkUrl;
    //父ID
    private String ParentID;
    //接收部门ID
    private String ReceiverDeptIDs;
    //接收部门名称
    private String ReceiverDeptNames;
    //接收人ID
    private String ReceiverIDs;
    //接收人名称
    private String ReceiverNames;
    //发送时间
    private String SendTime;
    //发送人
    private String SenderID;
    //发送人名
    private String SenderName;
    //系统编码
    private String SystemCode;
    //标题
    private String Title;
    //类型
    private String Type;
    //状态
    private String _state;
}
