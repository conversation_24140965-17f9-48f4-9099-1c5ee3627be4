package mh.cloud.module.system.controller.admin.enumcontroller;

import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.module.system.controller.admin.enumcontroller.vo.EnumDefVO;
import mh.cloud.module.system.controller.admin.enumcontroller.vo.EnumItemVO;
import mh.cloud.module.system.service.enumservice.EnumService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "管理后台 - 字典数据")
@RestController
@RequestMapping("/system/enum")
@Validated
public class EnumController {
    @Resource
    private EnumService enumService;


    @RequestMapping("/deflist")
    public CommonResult<List<EnumDefVO>> deflist(String code) {
        return CommonResult.success(enumService.getEnumDefsByCode(code));
    }

    @RequestMapping("/itemlist")
    public CommonResult<List<EnumItemVO>> itemlist(String defID) {
        return CommonResult.success(enumService.getEnumItemsByDefID(defID));
    }
}
