package mh.cloud.module.system.service.otherjobrelation;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import mh.cloud.module.system.controller.admin.otherjobrelation.vo.*;
import mh.cloud.module.system.dal.dataobject.otherjobrelation.OtherjobRelationDO;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.pojo.PageParam;
import mh.cloud.framework.common.util.object.BeanUtils;

import mh.cloud.module.system.dal.mysql.otherjobrelation.OtherjobRelationMapper;

import static mh.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static mh.cloud.module.system.enums.ErrorCodeConstants.*;

/**
 * 兼职关系 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class OtherjobRelationServiceImpl implements OtherjobRelationService {

    @Resource
    private OtherjobRelationMapper otherjobRelationMapper;

    @Override
    public String createOtherjobRelation(OtherjobRelationSaveReqVO createReqVO) {
        // 插入
        OtherjobRelationDO otherjobRelation = BeanUtils.toBean(createReqVO, OtherjobRelationDO.class);
        otherjobRelationMapper.insert(otherjobRelation);
        // 返回
        return otherjobRelation.getId();
    }

    @Override
    public void updateOtherjobRelation(OtherjobRelationSaveReqVO updateReqVO) {
        // 校验存在
        validateOtherjobRelationExists(updateReqVO.getId());
        // 更新
        OtherjobRelationDO updateObj = BeanUtils.toBean(updateReqVO, OtherjobRelationDO.class);
        otherjobRelationMapper.updateById(updateObj);
    }

    @Override
    public void deleteOtherjobRelation(String id) {
        // 校验存在
        validateOtherjobRelationExists(id);
        // 删除
        otherjobRelationMapper.deleteById(id);
    }

    private void validateOtherjobRelationExists(String id) {
        if (otherjobRelationMapper.selectById(id) == null) {
            throw exception(OTHERJOB_RELATION_NOT_EXISTS);
        }
    }

    @Override
    public OtherjobRelationDO getOtherjobRelation(String id) {
        return otherjobRelationMapper.selectById(id);
    }

    @Override
    public PageResult<OtherjobRelationDO> getOtherjobRelationPage(OtherjobRelationPageReqVO pageReqVO) {
        return otherjobRelationMapper.selectPage(pageReqVO);
    }

}