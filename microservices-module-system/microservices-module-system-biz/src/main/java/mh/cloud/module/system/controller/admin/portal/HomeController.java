package mh.cloud.module.system.controller.admin.portal;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.framework.security.core.LoginUser;
import mh.cloud.module.system.api.portal.SystemApi;
import mh.cloud.module.system.controller.admin.portal.V0.OneLevelMenu;
import mh.cloud.module.system.controller.admin.portal.V0.ProjectTree;
import mh.cloud.module.system.controller.admin.portal.service.HomeService;
import mh.cloud.module.system.dal.dataobject.auser.AUserDO;
import mh.cloud.module.system.service.auser.AUserService;
import mh.cloud.module.system.service.db.SQLHelper;
import mh.cloud.module.system.util.ConfigurationHelper;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static mh.cloud.framework.common.pojo.CommonResult.success;
import static mh.cloud.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import static mh.cloud.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "首页接口")
@RestController
@RequestMapping("/Portal/Home")
@Validated
@Slf4j
public class HomeController {

    @Resource
    private HomeService homeService;

    @Resource
    private AUserService aUserService;

    @Resource
    private ConfigurationHelper configurationHelper;

    @Resource
    private SystemApi systemApi;
    public static ConcurrentHashMap<String, String> baseImage = new ConcurrentHashMap<>();

    @PostMapping("/getYnfw")
    @Operation(summary = "院发文")
    public Page<Map<String, Object>> getYnfw(@RequestParam("page") int pageNumber, @RequestParam("pageSize") int pageSize) {

        return homeService.getYnfw(pageNumber, pageSize);

    }

    @PostMapping("/getGsxw")
    @Operation(summary = "院通知")
    public Page<Map<String, Object>> getGsxw(@RequestParam("page") int pageNumber, @RequestParam("pageSize") int pageSize) {
        return homeService.getGsxw(pageNumber, pageSize);
    }

    @PostMapping("/getTzgg")
    @Operation(summary = "院公告")
    public Page<Map<String, Object>> getTzgg(@RequestParam("page") int pageNumber, @RequestParam("pageSize") int pageSize) {
        return homeService.getTzgg(pageNumber, pageSize);
    }

    @PostMapping("/getZxxx")
    @Operation(summary = "获取咨询信息")
    public Page<Map<String, Object>> getZxxx(@RequestParam(name = "page", defaultValue = "1") int pageNumber, @RequestParam(name = "pageSize", defaultValue = "10") int pageSize, @RequestParam("code") String code) {
        return homeService.getZxxx(pageNumber, pageSize, code);
    }

    @PostMapping("/GetXmzxUnitInfos")
    @Operation(summary = "查询项目中心信息")
    public CommonResult<List<ProjectTree>> GetXmzxUnitInfos() {
        LoginUser loginUser = getLoginUser();
        return success(homeService.queryXmzxUnitInfos(loginUser.getId()));
    }

    @PostMapping("/GetSysMenu")
    @Operation(summary = "获取用户门户系统菜单")
    public List<Map<String, Object>> GetSysMenu() {
        AUserDO aUser = aUserService.getAUser(getLoginUserId());
        String strsql = SQLHelper.format("SELECT*FROM ( "
                        + "SELECT ROW_NUMBER () OVER (PARTITION BY t.XTLB ORDER BY t.SortIndex ASC) AS RID,t.FZ,t.CDMC AS ResID,t.CDMCName AS ResName,t.XSMC AS Name,t.Url,t.SortIndex,t.Parms,OpenType,t.XTLB, t.ResImage FROM (\n" +
                        "  SELECT s.FZ,sm.CDMC,sm.CDMCName,sm.XSMC,si.Url AS Url,sm.PXH AS SortIndex,sm.cs AS Parms,si.OpenType,si.XTLB,si.ResImage FROM OA.dbo.T_Page_SysMenuAuth a INNER JOIN oa.dbo.T_Page_SysMenu s ON a.sysmenu =s.ID AND isnull(s.IsDeleted,0)=0 INNER JOIN oa.dbo.T_Page_SysMenu_Menu sm ON s.id =sm.T_Page_SysMenuID AND isnull(sm.IsDeleted,0)=0 INNER JOIN oa.dbo.T_Page_SysMenuItem si ON si.id =sm.CDMC AND isnull(si.IsDeleted,0)=0 INNER JOIN KMYZH_SystemDataBase.dbo.A_GroupRelation b ON a.Role LIKE '%'+b.ParentGroupID +'%' INNER JOIN KMYZH_SystemDataBase.dbo.A_Group c ON b.ParentGroupID =c.ID AND c.GroupType ='Role' INNER JOIN KMYZH_SystemDataBase.dbo.A_User d ON d.id =b.ChildGroupID WHERE d.WorkNo ='{0}') t) t1 WHERE t1.RID =1 ORDER BY t1.SortIndex ASC "
                , aUser.getWorkNo());
        try (SQLHelper oa = SQLHelper.createSqlHelper("OA")) {
            return oa.selectRows(strsql);
        }
    }

    @PostMapping("/GetSysMenu1l")
    @Operation(summary = "获取用户门户一级菜单")
    public CommonResult<List<OneLevelMenu>> GetSysMenu1l(HttpServletRequest request) {
        String authorization = request.getHeader("Authorization");
        String token;
        if (!ObjUtil.isEmpty(authorization)) {
            token = authorization.split(" ")[1];
        } else {
            token = "";
        }

        //如果有遗留，超过20清理一次，防止内存溢出
        if (baseImage.size() > 20) {
            baseImage.clear();
        }
        AUserDO aUser = aUserService.getAUser(getLoginUserId());

        String strsql = SQLHelper.format("SELECT * FROM ( "
                        + "SELECT ROW_NUMBER () OVER (PARTITION BY t.XTLB ORDER BY t.SortIndex ASC) AS RID,t.FZ,t.CDMC AS ResID,t.CDMCName AS ResName,t.XSMC AS Name,t.FBT AS Eng, t.Url,t.SortIndex,t.Parms,OpenType,t.XTLB, t.ResImage FROM (\n" +
                        "  SELECT s.FZ,sm.CDMC,sm.CDMCName,sm.XSMC,si.Url AS Url,sm.PXH AS SortIndex,sm.cs AS Parms,si.OpenType,si.XTLB,si.ResImage,si.FBT FROM OA.dbo.T_Page_SysMenuAuth a INNER JOIN oa.dbo.T_Page_SysMenu s ON a.sysmenu =s.ID AND isnull(s.IsDeleted,0)=0 INNER JOIN oa.dbo.T_Page_SysMenu_Menu sm ON s.id =sm.T_Page_SysMenuID AND isnull(sm.IsDeleted,0)=0 INNER JOIN oa.dbo.T_Page_SysMenuItem si ON si.id =sm.CDMC AND isnull(si.IsDeleted,0)=0 INNER JOIN KMYZH_SystemDataBase.dbo.A_GroupRelation b ON a.Role LIKE '%'+b.ParentGroupID +'%' INNER JOIN KMYZH_SystemDataBase.dbo.A_Group c ON b.ParentGroupID =c.ID AND c.GroupType ='Role' INNER JOIN KMYZH_SystemDataBase.dbo.A_User d ON d.id =b.ChildGroupID WHERE d.WorkNo ='{0}') t) t1 WHERE t1.RID =1 ORDER BY t1.SortIndex ASC "
                , aUser.getWorkNo());

        try (SQLHelper oa = SQLHelper.createSqlHelper("OA")) {
            List<Map<String, Object>> maps = oa.selectRows(strsql);
            LocalDate now = LocalDate.now();
            List<OneLevelMenu> oneLevelMenus = maps.parallelStream()
                    .map(map -> {
                        String url = map.get("Url") == null ? "" : map.get("Url").toString();
                        String eng = map.get("Eng") == null ? "" : map.get("Eng").toString();
                        String pathName = map.get("ResID") == null ? "" : map.get("ResID").toString();
                        String name = map.get("Name") == null ? "" : map.get("Name").toString();
                        String resImage = map.get("ResImage") == null ? "" : map.get("ResImage").toString();
                        String openType = map.get("OpenType") == null ? "" : map.get("OpenType").toString();
                        String icon = systemApi.getSettingValue("downloadFileUrl") + "/BasicApplication/DownloadFile/IndexAnonymous?FileID=" + resImage;

                        OneLevelMenu oneLevelMenu = new OneLevelMenu()
                                .setUrl(url)
                                .setIconUrl(icon + "&token=" + token)
                                .setName(name)
                                .setEng(eng)
                                .setOpenType(openType)
                                .setPathName(pathName);

                        if ("办公中心".equals(name)) {
                            oneLevelMenu.setActive(true);
                        }

                        return oneLevelMenu;
                    })
                    .collect(Collectors.toList());
            return CommonResult.success(oneLevelMenus);
        }
    }

    @PostMapping("/getMsgCount")
    @Operation(summary = "获取当前登录用户待办工作数，未读消息数")
    public CommonResult<Map<String, Object>> getMsgCount() {
        String userId = getLoginUserId();
        Map<String, Object> map = homeService.getMsgCount(userId);
        return CommonResult.success(map);
    }

    @PostMapping("/Get4ALoginUrl")
    @Operation(summary = "4A认证接口处理")
    public CommonResult<Object> AuthenticateBy4A(HttpServletRequest request) {
        AUserDO aUser = aUserService.getUserSimpInfo(getLoginUserId());
        return CommonResult.success(homeService.AuthenticateBy4A(request.getRemoteAddr(), aUser));
    }

    @GetMapping("/set4ACookie")
    @Operation(summary = "4A认证Cookie处理")
    public CommonResult<Object> set4ACookie(HttpServletRequest request) {
        LoginUser loginUser = getLoginUser();
        return CommonResult.success(homeService.set4ACookie(request.getRemoteAddr(), loginUser.getId()));
    }


    @GetMapping("/GetVerificationCode")
    @Operation(summary = "获取发送电建通验证码")
    @PermitAll
    public CommonResult<String> GetVerificationCode(@RequestParam String workNo,
                                                    @RequestParam(defaultValue = "0") Integer sendType,
                                                    @RequestParam String appWorkNo,
                                                    HttpServletRequest request) {
        return CommonResult.success(homeService.GetVerificationCode(workNo, sendType,appWorkNo, request));
    }

    @GetMapping("/getZXZXHeaderImg")
    @Operation(summary = "获取资讯中心的头部图片")
    @PermitAll
    public CommonResult<List<Map<String,Object>>> getZXZXHeaderImg() {
        return CommonResult.success(homeService.getZXZXHeaderImg());
    }
}
