package mh.cloud.module.system.controller.admin.portal;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.module.system.dal.dataobject.auser.AUserDO;
import mh.cloud.module.system.service.portal.UserCenterService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static mh.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "主门户接口")
@RestController
@RequestMapping("/system/Portal/UserCenter")
public class UserCenterController {

    @Resource
    private UserCenterService userCenterService;


    @GetMapping("/LeftTopMenu")
    @Operation(summary = "获得用户左边用户中心列表")
    public CommonResult<List<Map<String, Object>>> getLeftTopMenu(String templateCode) {
        List<Map<String, Object>> list = userCenterService.getLeftTopMenu(templateCode);
        return success(list);
    }

    @GetMapping("/info")
    @Operation(summary = "获得用户中心用户信息")
    public CommonResult<AUserDO> getUserInfo() {
        return success(userCenterService.getUserInfo());
    }

    @PostMapping("/uploadImg")
    @Operation(summary = "更新用户头像签名")
    public CommonResult<String> updateUserInfo(@RequestPart("file") MultipartFile file,  // 使用@RequestPart替代@RequestBody
                                               @RequestPart("type") String type,
                                               @RequestPart("name") String name) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("file", file);
        params.put("type", type);
        params.put("name", name);
        return success(userCenterService.updateHeadAndSign(params));
    }

//    @GetMapping("/getUserPrc")
//    @Operation(summary = "获取用户头像")
//    public CommonResult<String> getUserPrc(String userId) {
//        return success(userCenterService.getUserPrc(userId));
//    }
//
//    @GetMapping("/getUserSign")
//    @Operation(summary = "获取用户签名")
//    public CommonResult<String> getUserSign(String userId) {
//        return success(userCenterService.getUserSign(userId));
//    }
}
