package mh.cloud.module.system.service.portal.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import mh.cloud.framework.common.exception.ServiceException;
import mh.cloud.framework.common.exception.enums.GlobalErrorCodeConstants;
import mh.cloud.module.system.controller.admin.portal.V0.CopyLayoutVo;
import mh.cloud.module.system.controller.admin.portal.V0.SysLayoutGroupVo;
import mh.cloud.module.system.dal.mysql.portal.LayoutGroupMapper;
import mh.cloud.module.system.service.portal.LayoutDataService;
import mh.cloud.module.system.service.portal.LayoutGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class LayoutGroupServiceImpl extends ServiceImpl<LayoutGroupMapper, SysLayoutGroupVo> implements LayoutGroupService {

    @Resource
    private LayoutDataService layoutDataService;

    @Autowired
    private StringRedisTemplate redisTemplate;


    private final String layout = "{\"x\":10,\"y\":4,\"w\":90,\"h\":30}";

    @Override
    public Object getLayoutData(String category, String type, String userId, String group) {

        List<Map<String, Object>> groupLayoutList = layoutDataService.getGroupLayoutList(category, type, userId, group);

        Map<Object, List<Map<String, Object>>> startUse = groupLayoutList.stream().map(item -> {
            JSONObject jsonObject = JSONUtil.parseObj(item.get("layout"));
            JSONObject content = JSONUtil.parseObj(item.get("content"));
            item.remove("content");
            item.put("itemJson", content);
            if (ObjUtil.isEmpty(jsonObject)) {
                jsonObject = JSONUtil.parseObj(layout);
            }
            item.putAll(jsonObject);
            return item;
        }).collect(Collectors.groupingBy(item -> item.get("startUse")));

        Map<String, List<Map<String, Object>>> map = new HashMap<>();
        map.put("noUse", startUse.get(0));
        map.put("use", startUse.get(1));
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object saveAndUpdateLayoutGroup(List<SysLayoutGroupVo> sysLayoutGroupList, Boolean copy) {

        List<SysLayoutGroupVo> saveList = new ArrayList<>();
        List<SysLayoutGroupVo> updateList = new ArrayList<>();
        for (SysLayoutGroupVo sysLayoutGroupVo : sysLayoutGroupList) {
            if(ObjUtil.isEmpty(sysLayoutGroupVo.getGroupCode())){
                throw new ServiceException(GlobalErrorCodeConstants.BAD_REQUEST.getCode(),GlobalErrorCodeConstants.BAD_REQUEST.getMsg());
            }
            String startUse = sysLayoutGroupVo.getStartUse();
            if (!ObjUtil.isEmpty(startUse) && startUse.equals("1")) {
                if (ObjUtil.isEmpty(sysLayoutGroupVo.getId())) {
                    saveList.add(sysLayoutGroupVo);
                } else {
                    updateList.add(sysLayoutGroupVo);
                }
            } else {
                // 未使用状态下，如果存在布局则删除
                if (!ObjUtil.isEmpty(sysLayoutGroupVo.getId())) {
                    this.removeById(sysLayoutGroupVo.getId());
                }
            }
        }
        try {
            if (!ObjUtil.isEmpty(saveList)) {
                this.saveBatch(saveList);
            }
            if (!ObjUtil.isEmpty(updateList)) {
                this.updateBatchById(updateList);
            }
            return true;
        } catch (Exception e) {
            throw new ServiceException(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR.getCode(), "布局处理异常" + e.getMessage());
        }
    }

    @Override
    public Object copyLayout(List<CopyLayoutVo> copyLayouts) {
        String copyId = UUID.randomUUID().toString();
        String jsonStr = JSONUtil.toJsonStr(copyLayouts);
        redisTemplate.opsForValue().set(copyId, jsonStr, 1, TimeUnit.MINUTES);
        return copyId;
    }

    private Object useLayout(List<SysLayoutGroupVo> sysLayoutGroupList) {

        List<String> dataIds = sysLayoutGroupList.stream().map(SysLayoutGroupVo::getDataId).toList();


        return null;
    }
}
