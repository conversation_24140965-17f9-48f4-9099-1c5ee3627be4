package mh.cloud.module.system.service.portal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import mh.cloud.module.system.controller.admin.portal.V0.FuncLogVo;
import mh.cloud.module.system.controller.admin.portal.V0.MyFlowVo;

import java.util.List;
import java.util.Map;

public interface WorkCenterService {
    /**
     * 获得门户网站左边点击排行列表
     *
     * @return
     */
    List<Map<String, Object>> getLeftTopMenu();

    /**
     * 我的工作
     *
     * @param type   类型： newtask-我的待办, completetask-我的已办, apply-我的申请, myfile-我的文件, focus-我的关注
     * @param activity
     * @return
     */
    Page<Map<String, Object>> getMyWork(String type, Integer page, Integer pageSize, String activity);

    List<Map<String, Object>> getMyShortCut();


    List<Map<String, Object>> getFocus(String activity);

    Boolean EditMyFocus(MyFlowVo flowVo);

    /**
     * 根据关注取待办
     */
    Object GetMyFocusByID(String id);

    Object IsOuterForm(String flowId);


    String DownLoadLogSql(Map<String, Object> file, String optnType, String userIp, String userId, String url);

    /**
     * 更新任务跳蓝
     */
    Object UpdateTaskNameColor(String taskId, String formInstanceId);

    /**
     * 对老系统任务进行处理
     */
    Object GetGWSTaskUrl(String taskId);

    /**
     * 根据文件名查询文件uid
     *
     * @param fileName 文件名称
     * @return 文件uid
     */
    String GetFileToken(String fileName);

    /**
     * 记录功能日志
     */
    Boolean FuncLog(FuncLogVo funcLogVo);

    /**
     * 温馨提示列表
     */
    Page<Map<String, Object>> GetWarmPromptList(Integer page, Integer pageSize);

    /**
     * 查询温馨提示详情
     */
    Map<String, Object> GetWarmPromptDetail(String id);


    Object executeSql(String database, String execSql);

    List<Map<String, Object>> selectSql(String database, String execSql);
}
