package mh.cloud.module.system.controller.admin.portal;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "消息接口")
@RestController
@RequestMapping("/system/Favorite")
@Validated
public class EditFavoriteController {

//    @GetMapping("/GetEditeList")
//    @Operation(summary = "收藏修改列表")
//    public CommonResult<Object> GetEditeList(@RequestParam(required = false) String TempleCode){
//
//    }
}
