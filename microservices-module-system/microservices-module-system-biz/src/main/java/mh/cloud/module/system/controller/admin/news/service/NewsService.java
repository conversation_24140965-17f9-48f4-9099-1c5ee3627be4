package mh.cloud.module.system.controller.admin.news.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import mh.cloud.module.system.controller.admin.news.VO.Menu;
import mh.cloud.module.system.enums.SqlType;

import java.util.List;
import java.util.Map;

public interface NewsService {
    String newsCenterTypeSql(String code, String detailId, String searchContent, SqlType type);

    List<Menu> GetDynamicMenu(String code);

    Page<Map<String, Object>> exceNewsSql(String s, int pageSize, int pageNum);
}
