package mh.cloud.module.system.controller.admin.auser.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import mh.cloud.framework.excel.core.annotations.DictFormat;
import mh.cloud.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 个人设置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AUserRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "9967")
    @ExcelProperty("ID")
    private String id;

    @Schema(description = "姓名", example = "李四")
    @ExcelProperty("姓名")
    private String name;

    @Schema(description = "登录名", example = "李四")
    @ExcelProperty("登录名")
    private String loginName;

    @Schema(description = "工号")
    @ExcelProperty("工号")
    private String workNo;

    @Schema(description = "排序")
    @ExcelProperty("排序")
    private Integer sortIndex;

    @Schema(description = "部门ID", example = "30427")
    @ExcelProperty("部门ID")
    private String deptID;

    @Schema(description = "部门名称", example = "张三")
    @ExcelProperty("部门名称")
    private String deptName;

    @Schema(description = "部门完整ID", example = "9846")
    @ExcelProperty("部门完整ID")
    private String deptFullID;

    @Schema(description = "兼职部门", example = "32557")
    @ExcelProperty("兼职部门")
    private String parttimeDeptID;

    @Schema(description = "兼职部门", example = "luohang")
    @ExcelProperty("兼职部门")
    private String parttimeDeptName;

    @Schema(description = "是否授权")
    @ExcelProperty(value = "是否授权", converter = DictConvert.class)
    @DictFormat("infra_boolean_string") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private String isAuth;

    @Schema(description = "是否删除", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "是否删除", converter = DictConvert.class)
    @DictFormat("infra_boolean_string") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private String isDeleted;

    @Schema(description = "删除时间")
    @ExcelProperty("删除时间")
    private LocalDateTime deleteTime;

    @Schema(description = "最后登录时间")
    @ExcelProperty("最后登录时间")
    private LocalDateTime lastLoginTime;

    @Schema(description = "最后登录IP")
    @ExcelProperty("最后登录IP")
    private String lastLoginIP;

    @Schema(description = "错误次数", example = "23752")
    @ExcelProperty("错误次数")
    private Integer errorCount;

    @Schema(description = "错误时间")
    @ExcelProperty("错误时间")
    private LocalDateTime errorTime;

    @Schema(description = "修改时间")
    @ExcelProperty("修改时间")
    private LocalDateTime modifyTime;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String description;

    @Schema(description = "性别")
    @ExcelProperty(value = "性别", converter = DictConvert.class)
    @DictFormat("system_user_sex") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private String sex;

    @Schema(description = "入职日期")
    @ExcelProperty("入职日期")
    private LocalDateTime inDate;

    @Schema(description = "离职日期")
    @ExcelProperty("离职日期")
    private LocalDateTime outDate;

    @Schema(description = "电话")
    @ExcelProperty("电话")
    private String phone;

    @Schema(description = "手机")
    @ExcelProperty("手机")
    private String mobilePhone;

    @Schema(description = "Emai")
    @ExcelProperty("Emai")
    private String email;

    @Schema(description = "地址")
    @ExcelProperty("地址")
    private String address;

    @Schema(description = "职务")
    @ExcelProperty("职务")
    private String duties;

    @Schema(description = "生日")
    @ExcelProperty("生日")
    private LocalDateTime birthday;

    @Schema(description = "默认IP")
    @ExcelProperty("默认IP")
    private String clientIp;

    @Schema(description = "签名密码")
    @ExcelProperty("签名密码")
    private String signPwd;

    @Schema(description = "子系统编号")
    @ExcelProperty("子系统编号")
    private String systemCode;

    @Schema(description = "SortIndex1")
    @ExcelProperty("SortIndex1")
    private Integer sortIndex1;

    @Schema(description = "SortIndex2")
    @ExcelProperty("SortIndex2")
    private Integer sortIndex2;

    @Schema(description = "SortIndex3")
    @ExcelProperty("SortIndex3")
    private Integer sortIndex3;

    @Schema(description = "SortIndex4")
    @ExcelProperty("SortIndex4")
    private Integer sortIndex4;

    @Schema(description = "SortIndex5")
    @ExcelProperty("SortIndex5")
    private Integer sortIndex5;

    @Schema(description = "是否接收手机短信")
    @ExcelProperty("是否接收手机短信")
    private String acceptMobileMsg;

    @Schema(description = "状态", example = "2")
    @ExcelProperty("状态")
    private String status;

    @Schema(description = "身份证照片")
    @ExcelProperty("身份证照片")
    private byte[] iDCardImg;

    @Schema(description = "用户照片")
    @ExcelProperty("用户照片")
    private byte[] userImg;

    @Schema(description = "身份证反面")
    @ExcelProperty("身份证反面")
    private byte[] iDCardImgF;

    @Schema(description = "Ext1")
    @ExcelProperty("Ext1")
    private String ext1;

    @Schema(description = "OfficeId", example = "18634")
    @ExcelProperty("OfficeId")
    private String officeId;

    @Schema(description = "OfficeName", example = "李四")
    @ExcelProperty("OfficeName")
    private String officeName;

    @Schema(description = "ucmobile")
    @ExcelProperty("ucmobile")
    private String ucmobile;

    @Schema(description = "uctitle")
    @ExcelProperty("uctitle")
    private String uctitle;

    @Schema(description = "ucshort")
    @ExcelProperty("ucshort")
    private String ucshort;

    @Schema(description = "ucmail")
    @ExcelProperty("ucmail")
    private String ucmail;

    @Schema(description = "thirdPartMail")
    @ExcelProperty("thirdPartMail")
    private String thirdPartMail;

    @Schema(description = "voipNumber")
    @ExcelProperty("voipNumber")
    private String voipNumber;

    @Schema(description = "OutKey")
    @ExcelProperty("OutKey")
    private String outKey;

    @Schema(description = "身份证号")
    @ExcelProperty("身份证号")
    private String idCard;

    @Schema(description = "用户类型", example = "2")
    @ExcelProperty("用户类型")
    private String userType;

    @Schema(description = "isAuthority")
    @ExcelProperty("isAuthority")
    private String isAuthority;

    @Schema(description = "民族")
    @ExcelProperty("民族")
    private String nation;

    @Schema(description = "曾用名", example = "张三")
    @ExcelProperty("曾用名")
    private String beforeName;

    @Schema(description = "政治面貌")
    @ExcelProperty("政治面貌")
    private String politics;

    @Schema(description = "OfficeFullID", example = "22573")
    @ExcelProperty("OfficeFullID")
    private String officeFullID;

}