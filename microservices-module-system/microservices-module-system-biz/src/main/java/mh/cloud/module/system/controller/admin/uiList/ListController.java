package mh.cloud.module.system.controller.admin.uiList;

import mh.cloud.module.system.service.db.SqlService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import mh.cloud.framework.common.pojo.PageParam;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.framework.common.util.object.BeanUtils;
import static mh.cloud.framework.common.pojo.CommonResult.success;

import mh.cloud.framework.excel.core.util.ExcelUtils;

import mh.cloud.framework.apilog.core.annotation.ApiAccessLog;
import static mh.cloud.framework.apilog.core.enums.OperateTypeEnum.*;

import mh.cloud.module.system.controller.admin.uiList.vo.*;
import mh.cloud.module.system.dal.dataobject.uiList.ListDO;
import mh.cloud.module.system.service.uiList.ListService;

@Tag(name = "管理后台 - 列表配置")
@RestController
@RequestMapping("/system/uilist")
@Validated
public class ListController {
    @Resource
    private SqlService service;

    @Resource
    private ListService listService;

    @PostMapping("/create")
    @Operation(summary = "创建列表配置")
    @PreAuthorize("@ss.hasPermission('system:list:create')")
    public CommonResult<String> createList(@Valid @RequestBody ListSaveReqVO createReqVO) {
        return success(listService.createList(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新列表配置")
    @PreAuthorize("@ss.hasPermission('system:list:update')")
    public CommonResult<Boolean> updateList(@Valid @RequestBody ListSaveReqVO updateReqVO) {
        listService.updateList(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除列表配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:list:delete')")
    public CommonResult<Boolean> deleteList(@RequestParam("id") String id) {
        listService.deleteList(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得列表配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:list:query')")
    public CommonResult<ListRespVO> getList(@RequestParam("id") String id) {
        ListDO list = listService.getList(id);
        return success(BeanUtils.toBean(list, ListRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得列表配置分页")
    @PreAuthorize("@ss.hasPermission('system:list:query')")
    public CommonResult<PageResult<ListRespVO>> getListPage(@Valid ListPageReqVO pageReqVO) {
        PageResult<ListDO> pageResult = listService.getListPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ListRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出列表配置 Excel")
    @PreAuthorize("@ss.hasPermission('system:list:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportListExcel(@Valid ListPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ListDO> list = listService.getListPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "列表配置.xls", "数据", ListRespVO.class,
                        BeanUtils.toBean(list, ListRespVO.class));
    }

}
