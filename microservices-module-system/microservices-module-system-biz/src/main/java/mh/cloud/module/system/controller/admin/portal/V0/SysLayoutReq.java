package mh.cloud.module.system.controller.admin.portal.V0;

import cn.hutool.json.JSONObject;
import lombok.Data;

@Data
public class SysLayoutReq extends SysLayoutDataReq {

    private JSONObject coordinates;

    private Integer i;

    private Integer startUse;

    public Object getX() {
        if (coordinates == null || coordinates.isEmpty()) {
            return 0;
        }
        return coordinates.get("x");
    }

    public Object getY() {
        if (coordinates == null || coordinates.isEmpty()) {
            return 0;
        }
        return coordinates.get("y");
    }

    public Object getW() {
        if (coordinates == null || coordinates.isEmpty()) {
            return 0;
        }
        return coordinates.get("w");
    }

    public Object getH() {
        if (coordinates == null || coordinates.isEmpty()) {
            return 0;
        }
        return coordinates.get("h");
    }
}
