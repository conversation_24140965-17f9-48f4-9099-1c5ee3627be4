package mh.cloud.module.system.controller.admin.portal;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.module.system.controller.admin.portal.V0.SysDocument;
import mh.cloud.module.system.controller.admin.portal.service.CommonService;
import mh.cloud.module.system.controller.admin.portal.service.DocumentService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/Portal/helpDocument")
@Validated
@Tag(name = "门户帮助文档")
public class DocumentController {

    @Resource
    private DocumentService documentService;

    @RequestMapping("/getHelpDocument")
    @Operation(summary = "获得帮助文档")
    public CommonResult<Map<String, Object>> getHelpDocument(@RequestParam("name") String routeName) {
        Map<String, Object> list = documentService.selectDocList(routeName);
        return CommonResult.success(list);
    }

    @PostMapping("/updateHelpDocument")
    @Operation(summary = "更新帮助文档")
    @PreAuthorize("@ss.hasPermission('system:document:update')")
    public CommonResult<Integer> updateHelpDocument(@RequestBody SysDocument sysDocument) {
        return CommonResult.success(documentService.updateDoc(sysDocument));
    }

}
