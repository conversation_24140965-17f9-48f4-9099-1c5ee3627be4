package mh.cloud.module.system.service.portal.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import mh.cloud.framework.common.exception.ServiceException;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.framework.security.core.LoginUser;
import mh.cloud.framework.security.core.util.SecurityFrameworkUtils;
import mh.cloud.module.system.api.portal.SystemApi;
import mh.cloud.module.system.controller.admin.portal.V0.FuncLogVo;
import mh.cloud.module.system.controller.admin.portal.V0.MyFlow;
import mh.cloud.module.system.controller.admin.portal.V0.MyFlowVo;
import mh.cloud.module.system.dal.dataobject.auser.AUserDO;
import mh.cloud.module.system.dal.db.StringHelper;
import mh.cloud.module.system.dal.mysql.portal.WorkCenterMapper;
import mh.cloud.module.system.service.auser.AUserService;
import mh.cloud.module.system.service.db.SQLHelper;
import mh.cloud.module.system.service.logger.SearchLogRecordService;
import mh.cloud.module.system.service.portal.ApplyService;
import mh.cloud.module.system.service.portal.VO.CompleteTaskList;
import mh.cloud.module.system.service.portal.WorkCenterService;
import mh.cloud.module.system.util.ConfigurationHelper;
import mh.cloud.module.system.util.storedProcedures.TaskListProvider;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static mh.cloud.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import static mh.cloud.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * 门户网站
 */

@Service
@Slf4j
public class WorkCenterServiceImpl implements WorkCenterService {
    public static final String MY_FOCUS = "SELECT * FROM dbo.WF_MyFlow WHERE CreateUserID = ?  ORDER BY CreateTime DESC";
    public static final String MY_FILE = "SELECT '/BasicApplication/DownloadFile?FileID=' + \n" +
            "ISNULL(B.Icon, (SELECT Icon FROM OA.dbo.T_M_FileExtIcon WHERE IsUse = '1' AND ExtName = 'other')) Icon,\n" +
            "ISNULL(t.AliasName,t.FileName) NewFileName,t.*  \n" +
            "FROM (SELECT Id,'|' + FileKey AS FileKey,FileName,UserId,CreateTime,DeptId,Cgroup,SortNum,OpFlag,AliasName,ExtName\n" +
            " FROM dbo.F_FilesMyFav \n" +
            " WHERE UserId = ? ORDER BY CreateTime DESC ) t \n" +
            "LEFT JOIN OA.dbo.T_M_FileExtIcon B ON B.ExtName = t.ExtName AND B.IsUse = '1' AND B.ExtName IS NOT NULL AND B.Icon IS NOT NULL";
    @Resource
    private AUserService userService;
    @Resource
    private ApplyService applyService;
    @Resource
    private SystemApi systemApi;
    @Resource
    private ConfigurationHelper configurationHelper;
    @Resource
    private SearchLogRecordService searchLogRecordService;
    @Resource
    private WorkCenterMapper workCenterMapper;
    @Resource
    private AUserService aUserService;

    public static String myUrlDecode(String str, Charset encoding) {
        if (encoding == null) {
            Charset utf8 = StandardCharsets.UTF_8;
            // 首先用 utf-8 进行解码
            String code = URLDecoder.decode(str.toUpperCase(), utf8);
            // 将已经解码的字符再次进行编码.
            String encode = URLEncoder.encode(code, utf8).toUpperCase();
            if (str.equals(encode))
                encoding = StandardCharsets.UTF_8;
            else
                encoding = Charset.forName("GB2312");
        }
        return URLDecoder.decode(str, encoding);
    }

    //将查询字符串解析转换为名值集合.
    public static Map<String, Object> GetQueryStringFromUrl(String urlString) {
        //Uri uri = new Uri(urlString);
        if (!urlString.contains("?")) {
            return new HashMap<>();
        }
        String queryString = urlString.split("\\?")[0];
        queryString = queryString.replace("?", "");
        Map<String, Object> result = new HashMap<>();
        if (ObjUtil.isNotEmpty(queryString)) {
            int count = queryString.length();
            for (int i = 0; i < count; i++) {
                int startIndex = i;
                int index = -1;
                while (i < count) {
                    char item = queryString.charAt(i);
                    if (item == '=') {
                        if (index < 0) {
                            index = i;
                        }
                    } else if (item == '&') {
                        break;
                    }
                    i++;
                }
                String key = null;
                String value = null;
                if (index >= 0) {
                    key = queryString.substring(startIndex, index - startIndex);
                    value = queryString.substring(index + 1, (i - index) - 1);
                } else {
                    key = queryString.substring(startIndex, i - startIndex);
                }
                if (ObjUtil.isNotEmpty(key)) {
                    result.put(myUrlDecode(key, null).toLowerCase(), myUrlDecode(value, null));
                } else {
                    result.put(key.toLowerCase(), value);
                }
                if ((i == (count - 1)) && (queryString.charAt(i) == '&') && ObjUtil.isNotEmpty(key)) {
                    result.put(key.toLowerCase(), null);
                }
            }
        }
        return result;
    }

    @Override
    public List<Map<String, Object>> getLeftTopMenu() {
        LoginUser loginUser = getLoginUser();
        AUserDO aUser = aUserService.getAUser(loginUser.getId());
        String sql = "   SELECT TOP(?) t.*,a.Url\n" +
                "        FROM (\n" +
                "        SELECT\n" +
                "        MenuId,\n" +
                "        FuncName,\n" +
                "        COUNT(FuncName) C_Num\n" +
                "        FROM\n" +
                "        dbo.T_Page_FunctionLog\n" +
                "        WHERE WorkNo = ?" +
                "        GROUP BY FuncName,MenuId\n" +
                "        ) t\n" +
                "        LEFT JOIN KMYZH_SystemDataBase.dbo.A_Res a ON t.MenuId = a.ID WHERE a.Url != '' AND a.Url IS NOT NULL ORDER BY t.C_Num DESC";
        SQLHelper sqlHelper = SQLHelper.createSqlHelper("Log");
        List<Map<String, Object>> maps = null;
        try {
            maps = sqlHelper.executeReader(sql, 6, aUser.getWorkNo());
        } catch (Exception e) {
            log.error("getLeftTopMenu error", e);
        } finally {
            sqlHelper.close();
        }
        return maps;
    }

    @Override
    public Page<Map<String, Object>> getMyWork(String type, Integer page, Integer pageSize, String activity) {
        if (activity != null && activity != "") {
            Map<String, Object> searchLogMap = new HashMap<>();
            searchLogMap.put("searchKey", activity);
            searchLogMap.put("moduleFlag", "IndexMenu");
            searchLogRecordService.createSearchLog(searchLogMap);
        }
        Page<Map<String, Object>> maps = new Page<>();
        switch (type) {
            case "newtask":
                // 我的待办
                maps = getAllNewTasks(type, page, pageSize, activity);
                //判断任务是否关注
                maps.setRecords(checkIsFocus(maps.getRecords()));
                break;
            case "completetask":
                //hutool获取当前日期减3个月的日期
                Date endDate = new Date();
                // 减去 3 个月
                Date startDate = DateUtil.offsetMonth(endDate, -3);
                maps = getCompleteTasksAll(page, pageSize, startDate, endDate, "");
                // 如果后续需要实现功能，这里可以添加相应的代码
                break;
            case "apply":
                // 同上，如果需要实现功能，添加相应代码
                LocalDateTime now = LocalDateTime.now();
                LocalDateTime startTime = now.minusMonths(3);
                maps = GetPageApply(page, pageSize, startTime, now, "");
//                maps = getCompleteTasksAll(page, pageSize, DateUtil.offsetMonth(new Date(), -3), new Date(), "");
                break;
            case "myfile":
                // 我的文件
                maps = getMyFile(page, pageSize);
                break;
            case "focus":
                // 我的关注
                maps = getMyFocus(page, pageSize);
                break;
            case "msg":
                // 全部
                int msgCount = getMsgCount();
                List<Map<String, Object>> myMsgDetail = getMyMsgDetail(4);
                maps = new Page<>();
                maps.setRecords(myMsgDetail);
                maps.setTotal(msgCount);
                break;
            default:
                // 处理未知类型，可以抛出异常或设置默认返回值
                throw new IllegalArgumentException("Unsupported type: " + type);
        }
        return maps;
    }

    private List<Map<String, Object>> checkIsFocus(List<Map<String, Object>> mapList) {
        //查询关注列表
        List<Map<String, Object>> focus = getFocus("");
        //判断任务是否关注
        return mapList.stream().map(map -> {
            String GoodWaySoft = map.get("GoodWaySoft").toString();
            if (("NK".equals(GoodWaySoft) || "RIIT".equals(GoodWaySoft) || "KunMing".equals(GoodWaySoft)) && focus.stream().filter(f -> f.get("TaskID").equals(map.get("ID"))).toList().size() == 1) {
                map.put("IsFocus", "true");
            } else if (focus.stream().filter(f -> f.get("CreateUserID").equals(map.get("ExecUserID")) && f.get("ExecURL").equals(map.get("URL"))).count() == 1) {
                map.put("IsFocus", "true");
            } else {
                map.put("IsFocus", "false");
            }
            return map;
        }).collect(Collectors.toList());
    }

    private int getMsgCount() {
        String sql = "SELECT COUNT(1) FROM KMYZH_SystemDataBase.dbo.V_MsgReceiveList WHERE UserID='{0}' AND AlreadyRead = 0";
        sql = SQLHelper.format(sql, getLoginUserId());
        SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core");
        try {
            Map<String, Object> map = sqlHelper.selectFirstRow(sql);
            if (map != null && map.get("COUNT") != null) {
                Object o = map.get("COUNT");
                return (int) o;
            }
            return 0;
        } finally {
            sqlHelper.close();
        }
    }

    private List<Map<String, Object>> getMyMsgDetail(int pageSize) {
        String sql = "SELECT TOP({0}) * FROM KMYZH_SystemDataBase.dbo.V_MsgReceiveList WHERE UserID='{1}' AND AlreadyRead = 0 ORDER BY AlreadyRead ASC,SendTime DESC";
        sql = SQLHelper.format(sql, pageSize, getLoginUserId());
//        sql = SQLHelper.format(sql, pageSize, "USER2427");
        try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core")) {
            return sqlHelper.selectRows(sql);
        }

    }

    @Override
    public List<Map<String, Object>> getMyShortCut() {
        SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core");
        try {
            String sqlStr = "SELECT A.Res ID, A.SortIndex, A.ResName AS [Name], ISNULL(A.Alias, A.ResName) AS Alias, B.MenuImage AS IconUrl, B.[Url], B.[Description], CASE WHEN ISNULL(B.OtherFile, '')='' AND ISNULL(B.Institutions, '')='' AND ISNULL(B.ServiceDirectory, '')='' THEN '0' ELSE '1' END IsEnable\n" +
                    "        FROM KMYZH_SystemDataBase.dbo.C_MyShortCut A\n" +
                    "        INNER JOIN KMYZH_SystemDataBase.dbo.A_Res B ON B.ID=A.Res\n" +
                    "        WHERE A.ResName IS NOT NULL AND A.ParentID IS NOT NULL AND A.CreateUserID='" + getLoginUserId() + "' AND(B.IsDeleted='0' OR B.IsDeleted IS NULL)\n" +
                    "        ORDER BY A.SortIndex ASC;";
            List<Map<String, Object>> maps = sqlHelper.selectRows(sqlStr);
            return maps;
        } finally {
            sqlHelper.close();
        }
    }

    public Page<Map<String, Object>> getMyFocus(Integer page, Integer pageSize) {
        LoginUser loginUser = getLoginUser();
        Page<Map<String, Object>> list = null;
        SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core");
        try {
            list = sqlHelper.ExecuteDataTable(MY_FOCUS, page, pageSize, loginUser.getId());
//            list = sqlHelper.ExecuteDataTable(sql, page, pageSize, "USER1689");
        } catch (Exception e) {
            log.error("getNewTask error", e);
        } finally {
            sqlHelper.close();
        }
        List<Map<String, Object>> mapList = new ArrayList<>();
        for (Map<String, Object> record : list.getRecords()) {
            List<Map<String, Object>> flowStateList = getFlowState(record.get("SysType").toString(), record.get("FormInstanceID").toString(), record.get("TaskID").toString());
            if (!flowStateList.isEmpty()) {
                String flowState = flowStateList.get(0).get("FlowState").toString();
                record.put("FlowState", flowState);
                if ("流程中".equals(flowState)) {
                    List<Map<String, Object>> stepList = GetNewTaskFlowStepByID(record.get("SysType").toString(), record.get("FormInstanceID").toString(), record.get("TaskID").toString());
                    record.put("CurrentStep", stepList.get(0).get("FlowStep").toString());
                } else {
                    record.put("CurrentStep", "-");
                }
            } else {
                record.put("CurrentStep", "-");
            }
            mapList.add(record);
        }
        list.setRecords(mapList);
        return list;
    }

    /**
     * 我的文件
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<Map<String, Object>> getMyFile(Integer page, Integer pageSize) {
        LoginUser loginUser = getLoginUser();
        Page<Map<String, Object>> list = null;
        SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core");
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();
        String token = SecurityFrameworkUtils.obtainAuthorization(request, "Authorization", "token");
        try {
            list = sqlHelper.ExecuteDataTable(MY_FILE, page, pageSize, loginUser.getId());
        } catch (Exception e) {
            log.error("getNewTask error", e);
        } finally {
            sqlHelper.close();
        }
        return list;
    }

    /**
     * 跟我相关的任务
     *
     * @param type
     * @return
     */
    public Page<Map<String, Object>> getAllNewTasks(String type, Integer page, Integer pageSize, String activity) {
        LoginUser loginUser = getLoginUser();

        Map<String, String> info = loginUser.getInfo();
        String sql = configurationHelper.GetSettingValue("IsUseKD4").equals("true") ? getNewTask("KD4", activity, loginUser) : null;

        String duties = "处级,副处级,科级,副科级,一级,二级,三级,七岗,六岗";
        List<String> dutiesList = Arrays.asList(duties.split(","));
        String dept = "01AAA006";
        List<String> deptList = Arrays.asList(dept.split(","));

        String sql2 = null;
        if ((ObjUtil.isNotEmpty(info.get(LoginUser.INFO_KEY_DEPT_ID)) && deptList.contains(info.get(LoginUser.INFO_KEY_DEPT_ID))) || (ObjUtil.isNotEmpty(info.get(LoginUser.DUTIES)) && dutiesList.contains(info.get(LoginUser.DUTIES)))) {
            sql2 = configurationHelper.GetSettingValue("IsUseCWPT").equals("true") ? getNewTask("CWPT", activity, loginUser) : null;
        }
        //拼接sql
        if (ObjUtil.isNotEmpty(sql2)) {
            sql = "SELECT * FROM ((" + sql + ") UNION (" + sql2 + "))";
        }
        sql = sql + " ORDER BY CreateTime DESC";

        SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core");
        Page<Map<String, Object>> maps = new Page<>();
        try {
            if (ObjUtil.isEmpty(page) && ObjUtil.isEmpty(pageSize)) {
                //没有分页数据表示查询所有
                List<Map<String, Object>> mapsAll = sqlHelper.selectRows(sql);
                if (!ObjUtil.isEmpty(mapsAll)) {
                    maps.setRecords(mapsAll);
                }
            } else {
                maps = sqlHelper.ExecuteDataTable(sql, page, pageSize);
            }
        } catch (Exception e) {
            log.error("getNewTask error", e);
        } finally {
            sqlHelper.close();
        }
        return maps;
    }

    /**
     * 获取我的待办
     *
     * @param type
     * @param loginUser
     * @return
     */
    public String getNewTask(String type, String activity, LoginUser loginUser) {

        Boolean sptaskuser = sptaskuser(loginUser.getId());
        activity = ObjUtil.isEmpty(activity) ? "" : " AND a.ActivityName LIKE '%" + activity + "%'";
        String sql = "";
        switch (type) {
            case "KD4":
                if (sptaskuser) {
                    //行政收文人员的待办任务要过滤移至待处理区的任务
                    sql = "SELECT ID, FormInstanceID, ActivityName, URL, FlowCreateTime, CreateUserName, CreateDeptName, FlowType, FlowID," +
                            "FlowName , GoodWaySoft, TaskType, RelateType, Importance,CreateTime, ISNULL(ExtsField,'#5577ff') ExtsField, " +
                            "FromUserNames,FromUserIDs,FromDeptName,FlowCode, (SELECT ISNULL(ShortName,Name) FROM dbo.A_Group WHERE id IN (SELECT DeptID FROM dbo.A_User WHERE ID=a.FromUserIDs)) AS ShortDeptName,'nowSys' AS Groups " +
                            "FROM dbo.V_NewTaskList a WHERE ExecUserID = '" + loginUser.getId() + "'" + activity +
                            "AND ID NOT IN (SELECT Nv200_1 FROM OA.dbo.T_Page_a7a700b29f6e409fb74c0efd2115944b WHERE (IsDeleted <> '1'OR IsDeleted IS NULL) " +
                            "union all SELECT Nv200_1 FROM OA.dbo.T_Page_a7a700b29f6e409fb74c0efd2115944b_Party WHERE (IsDeleted <> '1'OR IsDeleted IS NULL))";
                } else {
                    sql = "SELECT ID, FormInstanceID, ActivityName, URL, FlowCreateTime, CreateUserName, CreateDeptName, FlowType, FlowID , " +
                            "FlowName , GoodWaySoft, TaskType, RelateType, Importance,CreateTime, ISNULL(ExtsField,'#5577ff') ExtsField, " +
                            "FromUserNames,FromUserIDs,FromDeptName, FlowCode,(SELECT ISNULL(ShortName,Name) FROM dbo.A_Group WHERE id IN (SELECT DeptID FROM dbo.A_User WHERE ID=a.FromUserIDs)) AS ShortDeptName,'nowSys' AS Groups " +
                            "FROM dbo.V_NewTaskList a WHERE ExecUserID = '" + loginUser.getId() + "'" + activity;
                }
                break;
            case "CWPT":
                // 第三方任务数据
                sql = "SELECT CAST(Id AS NVARCHAR(50)) AS ID,'1' AS FormInstanceID,Name AS ActivityName,Url AS URL,Fqtime FlowCreateTime, Cusern CreateUserName, Cdeptn CreateDeptName,NULL AS FlowType, CAST(FlowId AS NVARCHAR(50)) AS FlowID,'智慧共享业务流程' AS FlowName," +
                        "Sys AS Goodwaysoft,Type  AS TaskType,NULL AS RelateType,Urgencyn AS Importance, Ctime AS CreateTime,'#5577ff' AS ExtsField," +
                        "Dwusern AS FromUserNames,Dwuser AS FromUserIDs,Dwdeptn AS FromDeptName,NULL AS FlowCode,NULL AS ShortDeptName,'rests' AS Groups " +
                        "FROM dbo.ThitskTasklist WHERE FlowState<>'Complete' AND FlowState<>'Stop' AND  Dwuser='" + loginUser.getInfo().get(LoginUser.WORK_NO) + "'" + activity;
                break;
            default:
                throw new ServiceException(type + "类型查询暂未开放");
        }

        return sql;

    }

    public Page<Map<String, Object>> getCompleteTasksAll(int page, int pageSize, java.util.Date beginTime, java.util.Date endTime, String activity) {
        Page<Map<String, Object>> completeTasks1 = getCompleteTasks(page, pageSize, beginTime, endTime, activity);
        List<Map<String, Object>> completeTasks = completeTasks1.getRecords();
        TypeReference<List<CompleteTaskList>> typeRef = new TypeReference<List<CompleteTaskList>>() {
        };
        List<CompleteTaskList> completeTaskLists = Convert.convert(typeRef, completeTasks);
        List<Map<String, Object>> focus = getFocus(activity);

        List<Map<String, Object>> completeList = new ArrayList<>();
        for (CompleteTaskList complete : completeTaskLists) {
            // 1. 处理关注状态
            if ((complete.getSystemName().equals("NK") || complete.getSystemName().equals("RIIT") || complete.getSystemName().equals("MINIUI"))
                    && focus.stream().anyMatch(f -> f.get("TaskID").equals(complete.getOriginID()))) {
                complete.IsFocus = "true";
            } else {
                complete.IsFocus = "false";
            }

            // 2. 处理部门信息（仅在 CreateUserID 有效时查询）
            complete.CreateUserDeptName = ""; // 默认值
            complete.CreateDeptName = "";    // 默认值

            if (StringUtils.isNotBlank(complete.CreateUserID)) {
                try {
                    // 使用参数化查询防止 SQL 注入
                    String sql = "SELECT ISNULL(Y.ShortName, Y.Name) AS CreateUserDeptName, " +
                            "ISNULL(X.DeptName, Y.Name) AS OriginalDeptName " +
                            "FROM dbo.A_User AS X " +
                            "INNER JOIN dbo.A_Group AS Y ON X.DeptID = Y.ID " +
                            "WHERE X.ID = ?";  // 使用 ? 占位符

                    try (SQLHelper core = SQLHelper.CreateSqlHelper("Core")) {
                        // 使用参数化查询（根据 SQLHelper 的实现调整）
                        List<Map<String, Object>> maps = core.selectRows(sql, complete.CreateUserID);

                        if (maps != null && !maps.isEmpty()) {
                            complete.CreateUserDeptName = Optional.ofNullable(maps.get(0).get("CreateUserDeptName"))
                                    .map(Object::toString)
                                    .orElse("");
                            complete.CreateDeptName = Optional.ofNullable(maps.get(0).get("OriginalDeptName"))
                                    .map(Object::toString)
                                    .orElse("");
                        }
                    }
                } catch (Exception e) {
                    log.error("查询用户部门信息失败，UserID: {}", complete.CreateUserID, e);
                }
            }

            // 3. 转换结果并格式化时间
            Map<String, Object> objectMap = BeanUtil.beanToMap(complete, false, false);
            objectMap.put("CreateTime", DateUtil.format(new Date(objectMap.get("CreateTime").toString()), "yyyy-MM-dd HH:mm"));
            completeList.add(objectMap);
        }

        completeTasks1.setRecords(completeList);
        return completeTasks1;
    }

    public Page<Map<String, Object>> getCompleteTasks(int page, int pageSize, java.util.Date beginTime, java.util.Date endTime, String activity) {
        AUserDO user = userService.getAUser(getLoginUserId());
        String whereStr = "";
        if (activity != null && !activity.isEmpty()) {
            whereStr = " AND (TaskName LIKE '%" + activity + "%' OR FlowName LIKE '%" + activity + "%' OR SenderName LIKE '%" + activity + "%' OR CreateUserName LIKE '%" + activity + "%')";
        }
        beginTime = beginTime == null || beginTime.toString().isEmpty() ? new java.util.Date(System.currentTimeMillis() - 31536000000L) : beginTime;
        endTime = endTime == null || endTime.toString().isEmpty() ? new java.util.Date() : endTime;

        String beginTimeStr = DateUtil.format(beginTime, "yyyy-MM-dd HH:mm:ss");
        String endTimeStr = DateUtil.format(endTime, "yyyy-MM-dd HH:mm:ss");

        try {
            Page<Map<String, Object>> completeTaskList = TaskListProvider.getCompleteTaskList(page, pageSize, user.getId(), beginTimeStr, endTimeStr, whereStr);
            return completeTaskList;
        } catch (SQLException e) {
            throw new RuntimeException("查询已完成任务失败", e); // 更友好的错误提示
        }
    }

    public List<Map<String, Object>> getFocus(String activity) {
        AUserDO user = userService.getAUser(getLoginUserId());
        String whereStr = "";
        if (activity != null && !activity.isEmpty()) {
            whereStr = "AND FlowName LIKE '%" + activity + "%'";
        }

        String sql = "SELECT * FROM dbo.WF_MyFlow WHERE CreateUserID = ? " + whereStr + " ORDER BY CreateTime DESC";
        try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core")) {
            return sqlHelper.selectRows(sql, user.getId());
        }
    }

    /**
     * 关注取关
     */
    @Override
    public Boolean EditMyFocus(MyFlowVo myFlowVo) {

        String tabName = myFlowVo.getTabName();
        String type = myFlowVo.getType();
        MyFlow myFlow = BeanUtil.toBean(new JSONObject(myFlowVo.getMyFlow()), MyFlow.class);
        AUserDO user = userService.getAUser(getLoginUserId());

        Boolean flag = true;
        if (ObjUtil.isNotEmpty(tabName)) {
            switch (tabName) {
                case "newtask":
                    flag = ExecMyFocus(user, type, myFlow.getFlowID(), myFlow.getActivityName(), myFlow.getID(), myFlow.getGoodWaySoft(), myFlow.getURL(), myFlow.getFormInstanceID());
                    break;
                case "completetask":
                    flag = ExecMyFocus(user, type, myFlow.getFlowID(), myFlow.getTaskName(), myFlow.getOriginID(), myFlow.getSystemName(), myFlow.getViewUrl(), myFlow.getFormInstanceID());
                    break;
                case "apply":
                    flag = ExecMyFocus(user, type, myFlow.getDefFlowCode(), myFlow.getTitle(), myFlow.getID(), myFlow.getSysPT(), myFlow.getURL(), myFlow.getFormInstanceID());
                    break;
                case "focus":
                    flag = ExecMyFocus(user, type, myFlow.getFlowID(), myFlow.getFlowName(), myFlow.getTaskID(), myFlow.getSysType(), myFlow.getExecURL(), myFlow.getFormInstanceID());
                    break;
                case "other":
                    String execData = myFlowVo.getExecData();
                    if (ObjUtil.isNotEmpty(execData)) {
                        ObjectMapper mapper = new ObjectMapper();
                        List<MyFlow> list;
                        try {
                            list = mapper.readValue(execData, mapper.getTypeFactory().constructCollectionType(List.class, MyFlow.class));
                        } catch (JsonProcessingException e) {
                            throw new RuntimeException(e);
                        }
                        for (MyFlow flow : list) {
                            flag = ExecMyFocus(user, type, flow.getFlowID(), flow.getFlowName(), flow.getTaskID(), flow.getSysType(), flow.getExecURL(), flow.getFormInstanceID());
                        }
                    }
                    break;
            }
        }
        return flag;
    }

    public Boolean ExecMyFocus(AUserDO user, String type, String FlowID, String TaskName, String TaskID, String SysType, String ExecURL, String FormInstanceID) {
        SysType = "KUNMING".equalsIgnoreCase(SysType) ? "MINIUI" : "NK".equalsIgnoreCase(SysType) ? "KD4" : SysType.toUpperCase();
        String sql;
        Boolean flag = true;

        //取消关注，删除记录
        if ("0".equals(type)) {
            switch (SysType) {
                case "KD4":
                case "RIIT":
                case "MINIUI":
//                    sql = "DELETE dbo.WF_MyFlow WHERE CreateUserID = '{user.ID}' AND TaskID = '{TaskID}'";
                    sql = SQLHelper.format("DELETE dbo.WF_MyFlow WHERE CreateUserID = '{0}' AND TaskID = '{1}'", user.getId(), TaskID);
                    break;
                default:
//                    sql = "DELETE dbo.WF_MyFlow WHERE CreateUserID = '{user.ID}' AND ExecURL = '{ExecURL}'";
                    sql = SQLHelper.format("DELETE dbo.WF_MyFlow WHERE CreateUserID = '{0}' AND ExecURL = '{1}'", user.getId(), ExecURL);
                    break;
            }
            try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core")) {
                sqlHelper.executeNonQuery(sql);
            }
        } else {
            //判断有没有重复插入
            try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core")) {
                List<Map<String, Object>> maps = sqlHelper.selectRows("SELECT * FROM dbo.WF_MyFlow WHERE CreateUserID = ? AND TaskID = ?", user.getId(), TaskID);
                if (ObjUtil.isNotEmpty(maps)) {
                    throw new RuntimeException("请勿重复关注");
                }
            }

            sql = "INSERT INTO dbo.WF_MyFlow(ID, CreateUser, CreateUserID, CreateTime, FlowID, FlowName, TaskID, FormInstanceID,SysType,ExecURL)" +
                    "VALUES('{0}','{1}','{2}','{3}','{4}','{5}','{6}','{7}','{8}','{9}')";

            String id = UUID.randomUUID().toString().replace("_", "");

            String format = SQLHelper.format(sql, id, user.getName(), user.getId(), DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss"), FlowID, TaskName, TaskID, FormInstanceID, SysType, ExecURL);

            try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core")) {
                if (sqlHelper.executeNonQuery(format) < 1) {
                    flag = false;
                }
            }
        }
        return flag;
    }

    /**
     * 我的申请
     *
     * @return
     */
    public Page<Map<String, Object>> GetPageApply(int page, int pageSize, LocalDateTime BeginTime, LocalDateTime EndTime, String Activity) {
        List<Map<String, Object>> list = GetApplyList(BeginTime, EndTime, Activity);
        Page<Map<String, Object>> mapPage = new Page<>();
        mapPage.setTotal(list.size());

        if (!list.isEmpty()) {
            page--;
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            list = list.stream().sorted((l1, l2) -> {
                LocalDateTime date1 = LocalDateTime.parse(l1.get("CreateTime").toString(), dtf);
                LocalDateTime date2 = LocalDateTime.parse(l2.get("CreateTime").toString(), dtf);
                return date2.compareTo(date1);
            }).skip(page).limit(pageSize).toList();
        }
        List<Map<String, Object>> focus = getFocus(Activity);

        List<Map<String, Object>> mapList = new ArrayList<>();
        //处理当前审批步骤信息
        for (Map<String, Object> objectMap : list) {
            String flowState = objectMap.get("FlowState").toString();
            String formInstanceID = objectMap.get("FormInstanceID").toString();
            String defFlowCode = objectMap.get("DefFlowCode").toString();
            String sysPT = objectMap.get("SysPT").toString();
            String url = objectMap.get("URL").toString();

            if ("流程中".equals(flowState)) {
                List<Map<String, Object>> maps = GetNewTaskFlowStepByID(sysPT.toUpperCase(), formInstanceID, defFlowCode);
                String flowStep = maps.get(0).get("FlowStep").toString();
                objectMap.put("CurrentStep", flowStep);
            } else {
                objectMap.put("CurrentStep", "-");
            }

            if ("MINIUI".equalsIgnoreCase(sysPT)) {
                objectMap.put("URL", "/UIBuilder/UIViewer/TempFormViewer?TempletCode=TempForm_ac02011202724323a4aff170b76ec32c&pa=" + objectMap.get("TempletCode") + "|" + defFlowCode + "|" + formInstanceID);
            } else if ("KD4".equalsIgnoreCase(sysPT)) {
                String templetCode = StringHelper.getQueryStringFromUrl(url).get("TempletCode");
                objectMap.put("URL", "/UIBuilder/UIViewer/FlowPage?TempletCode=" + templetCode + "&ID=" + formInstanceID + "&FlowCode=" + defFlowCode + "&FuncType=view");
            } else {
                String templetCode = StringHelper.getQueryStringFromUrl(url).get("TempletCode");
                objectMap.put("URL", "http://10.10.1.74/UIBuilder/UIViewer/FlowPage?TempletCode=" + templetCode + "&ID=" + flowState + "&FlowCode=" + defFlowCode + "&FuncType=view");
            }
            // 处理我的申请关注的
            if (("KD4".equals(sysPT) || "RIIT".equals(sysPT) || "MINIUI".equals(sysPT)) &&
                    focus.stream().anyMatch(c -> c.get("TaskID").equals(objectMap.get("ID")))) {
                objectMap.put("IsFocus", true);
            } else {
                objectMap.put("IsFocus", false);
            }
            mapList.add(objectMap);
        }
        mapPage.setRecords(mapList);
        return mapPage;
    }

    private List<Map<String, Object>> GetApplyList(LocalDateTime BeginTime, LocalDateTime EndTime, String Activity) {
        String beginTimeStr = DateUtil.format(BeginTime, "yyyy-MM-dd HH:mm:ss");
        //endTime转字符串
        String endTimeStr = DateUtil.format(EndTime, "yyyy-MM-dd HH:mm:ss");

        List<Map<String, Object>> maps1 = GetApply("KD4", beginTimeStr, endTimeStr, Activity);
//        List<Map<String, Object>> maps2 = GetApply("MINIUI", beginTimeStr, endTimeStr, Activity);
//        List<Map<String, Object>> maps3 = GetApply("RIIT", beginTimeStr, endTimeStr, Activity);
        List<Map<String, Object>> list = new ArrayList<>();
        if (!maps1.isEmpty()) {
            list.addAll(maps1);
        }
//        if (!maps2.isEmpty()) {
//            list.addAll(maps2);
//        }
//        if (!maps3.isEmpty()) {
//            list.addAll(maps3);
//        }
        return list;
    }

    private List<Map<String, Object>> GetApply(String type, String beginTime, String endTime, String Activity) {
        String whereStr = "";
        String sql;
        List<Map<String, Object>> mapList = new ArrayList<>();
        if (ObjUtil.isNotEmpty(Activity)) {
            whereStr = "KD4".equals(type) || "RIIT".equals(type) ? "AND B.[Name] like '%" + Activity + "%'" : "AND TaskName LIKE '%" + Activity + "%'";
        }
        switch (type) {
            case "KD4":
                sql = "SELECT A.ID,B.[Name] Title,B.[URL],C.CreateUser,C.CreateUserID,C.CreateTime, " +
                        "CASE C.[Status]WHEN 'Processing' THEN '流程中' " +
                        "WHEN 'Complete' THEN '已完成' ELSE '已终止' " +
                        "END FlowState, C.DefFlowCode, C.FormInstanceID, '' TempletCode, '昆明院综合办公系统' SysType, '{0}' SysPT " +
                        "FROM dbo.WF_Task A LEFT JOIN dbo.WF_Activity B ON B.ID = A.ActivityID " +
                        "LEFT JOIN dbo.WF_Flow C ON C.ID = B.FlowID " +
                        "WHERE A.ExecUserID = '{1}' " +
                        "AND A.PreTaskID IS NULL AND C.MainFlowID IS NULL AND C.Status<> 'Cancel' {2} " +
                        "AND C.CreateTime BETWEEN '{3}' AND '{4}' ";
//                String kd4Sql = SQLHelper.format(sql, type, user.getId(), whereStr, beginTime, endTime);
                String kd4Sql = SQLHelper.format(sql, type, getLoginUserId(), whereStr, beginTime, endTime);
                try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core")) {
                    mapList = sqlHelper.selectRows(kd4Sql);
                }
                break;
            case "RIIT":
                sql = "SELECT A.ID,B.[Name] Title,B.[URL],C.CreateUser,C.CreateUserID,C.CreateTime, " +
                        "CASE C.[Status]WHEN 'Processing' THEN '流程中' " +
                        "WHEN 'Complete' THEN '已完成' ELSE '已终止' " +
                        "END FlowState, C.DefFlowCode, C.FormInstanceID, '' TempletCode, '信息院综合办公系统' SysType, '{0}' SysPT " +
                        "FROM dbo.WF_Task A LEFT JOIN dbo.WF_Activity B ON B.ID = A.ActivityID " +
                        "LEFT JOIN dbo.WF_Flow C ON C.ID = B.FlowID WHERE A.ExecUserID = '{1}' " +
                        "AND A.PreTaskID IS NULL AND C.MainFlowID IS NULL AND C.Status<> 'Cancel' {2} " +
                        "AND C.CreateTime BETWEEN '{3}' AND '{4}' ";
                String riitSql = SQLHelper.format(sql, type, getLoginUserId(), whereStr, beginTime, endTime);

                try (SQLHelper sqlHelper = SQLHelper.CreateSqlHelper("RIITDB")) {
                    mapList = sqlHelper.selectRows(riitSql);
                }
                break;
            case "MINIUI":
                sql = "SELECT A.Id ID,A.TaskName Title,A.ExecUrl [URL],A.OwnerUserName CreateUser,A.OwnerUserId CreateUserID,A.CreateTime, " +
                        "CASE WHEN (SELECT COUNT(1) FROM dbo.WfWorkList WHERE FlowId = A.FlowId AND RelateId = A.RelateId AND State = 'New') >0 " +
                        "THEN '流程中' WHEN(SELECT COUNT(1)FROM dbo.WfWorkList WHERE FlowId = A.FlowId AND RelateId = A.RelateId AND State = 'End'AND FinishTime IS NULL) > 0 " +
                        "THEN '已终止' ELSE '已完成' " +
                        "END FlowState, A.FlowId DefFlowCode, A.RelateId FormInstanceID, B.sKey TempletCode, '昆明院综合办公系统' SysType, '{0}' " +
                        "SysPT FROM(SELECT * FROM KunMing_BeAdmin.dbo.WfWorkList WHERE SendUserId = '{1}' " +
                        "AND REPLACE(ParentId, ' ', '') = 'WFW-1' {2} " +
                        "AND CreateTime BETWEEN '{3}' AND '{4}' )A " +
                        "INNER JOIN StepTable.dbo.GwSendTable B ON B.Id = A.StepTableId ";
                String minSql = SQLHelper.format(sql, type, getLoginUserId(), whereStr, beginTime, endTime);
                try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core")) {
                    mapList = sqlHelper.selectRows(minSql);
                }
                break;
        }
        return mapList;
    }

    /**
     * 根据任务ID以及对应平台标识获取任务信息
     */
    private List<Map<String, Object>> GetNewTaskFlowStepByID(String type, String formInstanceID, String defFlowCode) {
        String sql;
        List<Map<String, Object>> mapList = new ArrayList<>();
        switch (type) {
            case "KD4":
                sql = "SELECT STUFF((SELECT ',' + t.FlowStep FROM (" +
                        "SELECT C.Name + '(' + B.ExecUserName + ')' FlowStep FROM dbo.WF_Activity A " +
                        "LEFT JOIN dbo.WF_Task B ON B.ActivityID = A.ID INNER JOIN dbo.WF_DefActivity C ON C.ID = A.DefActivityID " +
                        "WHERE A.FormInstanceID = '{0}' AND B.ExecTime IS NULL) t FOR XML PATH('')),1,1,'') FlowStep";
                String kd4Sql = SQLHelper.format(sql, formInstanceID);
                try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core")) {
                    mapList = sqlHelper.selectRows(kd4Sql);
                }
                break;
            case "RIIT":
                sql = "SELECT STUFF((SELECT ',' + t.FlowStep FROM (" +
                        "SELECT C.Name + '(' + B.ExecUserName + ')' FlowStep FROM dbo.WF_Activity A " +
                        "LEFT JOIN dbo.WF_Task B ON B.ActivityID = A.ID INNER JOIN dbo.WF_DefActivity C ON C.ID = A.DefActivityID " +
                        "WHERE A.FormInstanceID = '{0}' AND B.ExecTime IS NULL) t FOR XML PATH('')),1,1,'') FlowStep";

                String riitSql = SQLHelper.format(sql, formInstanceID);
                try (SQLHelper sqlHelper = SQLHelper.CreateSqlHelper("RIITDB")) {
                    mapList = sqlHelper.selectRows(riitSql);
                }
                break;
            case "MINIUI":
                sql = "SELECT STUFF((SELECT ',' + t.FlowStep " +
                        "FROM (SELECT st.步骤名称 + '(' + wf.ReciveUserName + ')' FlowStep " +
                        "FROM ( SELECT * FROM StepTable.dbo.GwSendTable WHERE StepKey = '{0}' AND GwId = {1} AND GwState = 'New') wf " +
                        "LEFT JOIN (SELECT * FROM StepTable.dbo.SetStepTable WHERE StepKey = '{2}') st ON st.步骤序号 = wf.NowStep) t FOR XML PATH('')),1,1,'') FlowStep";
                String minSql = SQLHelper.format(sql, defFlowCode, formInstanceID.replace("Gw.", ""), defFlowCode);
                try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core")) {
                    mapList = sqlHelper.selectRows(minSql);
                }
                break;
        }
        return mapList;
    }

    // 根据平台标识、流程ID、表单记录ID获取任务状态
    public List<Map<String, Object>> getFlowState(String type, String formInstanceID, String taskID) {
        String sql;
        List<Map<String, Object>> list = new ArrayList<>();
        switch (type) {
            case "KD4":
                sql = "SELECT CASE [Status] WHEN 'Processing' THEN '流程中' WHEN 'Complete' THEN '已完成' ELSE '已终止' END FlowState FROM dbo.WF_Flow " +
                        "WHERE FormInstanceID = '{0}' AND MainFlowID IS NULL";
                String kd4Format = SQLHelper.format(sql, formInstanceID);
                try (SQLHelper sqlHelper = SQLHelper.CreateSqlHelper("Core")) {
                    list = sqlHelper.selectRows(kd4Format);
                }
                break;
            case "RIIT":
                sql = "SELECT CASE [Status] WHEN 'Processing' THEN '流程中' WHEN 'Complete' THEN '已完成' ELSE '已终止' END FlowState FROM dbo.WF_Flow " +
                        "WHERE FormInstanceID = '{0}' AND MainFlowID IS NULL";
                String tiitFormat = SQLHelper.format(sql, formInstanceID);

                try (SQLHelper sqlHelper = SQLHelper.CreateSqlHelper("RIITDB")) {
                    list = sqlHelper.selectRows(tiitFormat);
                }
                break;
            case "MINIUI":
                sql = "SELECT CASE WHEN (SELECT COUNT(1) FROM dbo.WfWorkList " +
                        "WHERE FlowId = A.FlowId AND RelateId = A.RelateId AND State = 'New') > 0 THEN '流程中' " +
                        "WHEN (SELECT COUNT(1) FROM dbo.WfWorkList " +
                        "WHERE FlowId = A.FlowId AND RelateId = A.RelateId AND State = 'End' AND FinishTime IS NULL) > 0 " +
                        "THEN '已终止' ELSE '已完成' END FlowState FROM ( SELECT * FROM KunMing_BeAdmin.dbo.WfWorkList WHERE Id = '{0}') A ";
                String miniutFormat = SQLHelper.format(sql, taskID);
                try (SQLHelper sqlHelper = SQLHelper.CreateSqlHelper("Core")) {
                    list = sqlHelper.selectRows(miniutFormat);
                }
                break;
        }
        return list;
    }

    /**
     * 根据关注取待办
     *
     * @param id
     */
    @Override
    public Object GetMyFocusByID(String id) {
        String sql = "SELECT * FROM WF_MyFlow WHERE ID = '{0}'";
        String formatSql = SQLHelper.format(sql, id);
        Map<String, Object> wFMyFlow = new HashMap<>();
        try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core")) {
            wFMyFlow = sqlHelper.selectFirstRow(formatSql);
        }

        List<Map<String, Object>> taskList = getAllNewTasks("", null, null, null).getRecords();

        //判断关注的流程是否属于新任务
        Map<String, Object> newTask = new HashMap<>();
        for (Map<String, Object> task : taskList) {
            String taskId = wFMyFlow.get("TaskID").toString();
            if (task.get("ID").equals(taskId)) {
                newTask.put("newtask", task);
                break;
            }
        }
        return newTask;
    }

    @Override
    public Object IsOuterForm(String flowId) {
        String sql = "SELECT FormType FROM WF_DefFlow WHERE Code IN (SELECT DefFlowCode FROM WF_Flow WHERE ID = '{0}')";
        try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core")) {
            Map<String, Object> objectMap = sqlHelper.selectFirstRow(sql, flowId);
            if (objectMap != null && !objectMap.isEmpty()) {
                if (objectMap.get("FormType") == null) {
                    return false;
                } else {
                    if (objectMap.get("FormType").toString().isEmpty()) {
                        return false;
                    } else {
                        return objectMap.get("FormType").toString().equals("outer");
                    }
                }
            }
            return false;
        }
    }

    /**
     * 更新任务跳蓝
     */
    @Override
    public Object UpdateTaskNameColor(String taskId, String formInstanceId) {

        String sql = "IF(SELECT COUNT(*) FROM OA.dbo.IncomingFile " +
                "WHERE ID = '{0}') > 0 " +
                "BEGIN Exec dbo.SetTaskColor '{1}','R',0 END " +
                "ELSE IF (SELECT COUNT(*) FROM OA.dbo.PartyAffairsIncomingFile " +
                "WHERE ID = '{2}') > 0 " +
                "BEGIN EXEC dbo.P_PW_SetTaskColor '{3}','R',0 END ";
        String formatSql = SQLHelper.format(sql, formInstanceId, taskId, formInstanceId, taskId);
        try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("OA")) {
            sqlHelper.executeNonQuery(formatSql);
            return true;
        } catch (Exception e) {
            log.error("更新任务跳蓝,异常：日志信息：{}" + e.getMessage());
            return false;
        }
    }

    /**
     * 对老系统任务进行处理
     */
    @Override
    public Object GetGWSTaskUrl(String taskId) {
        //TODO data_1_3这表不存在，暂时不做处理
        String sql = "SELECT Id,[Type],FlowId,RelateId,RelateType,[System],ExecUrl FROM dbo.WfWorkList WHERE Id = '" + taskId + "'";
        try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("data_1_3")) {
            Map<String, Object> dt = sqlHelper.selectFirstRow(sql);
            if (ObjUtil.isEmpty(dt)) {
                return "任务出错，请联系管理员！";
            }
            return getUrlParams(dt, taskId);
        }
    }

    @Override
    public String GetFileToken(String fileName) {
        String sql = "SELECT Guid FROM dbo.F_FsFile WHERE Name = '" + fileName + "'";
        try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core")) {
            Map<String, Object> guid = sqlHelper.selectFirstRow(sql);
            return guid.get("Guid").toString();
        }
    }

    /**
     * 记录功能日志
     *
     * @param funcLogVo
     */
    @Override
    public Boolean FuncLog(FuncLogVo funcLogVo) {
        String createTime = DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss");
        String sql = "INSERT INTO dbo.T_Page_FunctionLog" +
                " (ID,WorkNo,PageName,PageUrl,FuncName,FuncUrl,ClientIP,CreateTime,MenuId) " +
                "VALUES ('{0}','{1}','{2}','{3}','{4}','{5}','{6}','{7}','{8}')";
        String id = UUID.randomUUID().toString();
        String formatSql = SQLHelper.format(sql, id, funcLogVo.getWorkno(), funcLogVo.getPagename(),
                funcLogVo.getPageurl(), funcLogVo.getFuncname(), funcLogVo.getFuncurl(), funcLogVo.getClientIp(), createTime, funcLogVo.getMenuId());

        try (SQLHelper sqlHelper = SQLHelper.CreateSqlHelper("Log")) {
            return sqlHelper.executeNonQuery(formatSql) > 0;
        }
    }

    /**
     * 温馨提示列表
     *
     * @param page
     * @param pageSize
     */
    @Override
    public Page<Map<String, Object>> GetWarmPromptList(Integer page, Integer pageSize) {
        //定义sql
        String sql = "SELECT ROW_NUMBER() OVER (ORDER BY CAST(OrderIndex AS INT) ASC) AS RID,ID, Code, Type, Title, OrderIndex, PhotoAddress, StartTime, EndTime, CreateTime \n" +
                "FROM dbo.MH_WarmPrompt \n" +
                "WHERE (IsDeleted <> '1' OR IsDeleted IS NULL) \n" +
                "ORDER BY CAST(OrderIndex AS INT) ASC\n" +
                "OFFSET {0} ROWS FETCH NEXT {1} ROWS ONLY";
        String formatSql = SQLHelper.format(sql, (page - 1) * pageSize, pageSize);
        Page<Map<String, Object>> mapPage = new Page<>(page, pageSize);
        try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("OA")) {
            List<Map<String, Object>> list = sqlHelper.selectRows(formatSql);
            mapPage.setRecords(checkTime(list));
        }
        try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("OA")) {
            Map<String, Object> objectMap = sqlHelper.selectFirstRow("SELECT count(ID) AS count FROM dbo.MH_WarmPrompt WHERE (IsDeleted <> '1' OR IsDeleted IS NULL)");
            Object count = objectMap.get("count");
            mapPage.setTotal(Long.parseLong(count.toString()));
        }
        return mapPage;
    }

    /**
     * 查询温馨提示详情
     *
     * @param id 温馨提示id
     */
    @Override
    public Map<String, Object> GetWarmPromptDetail(String id) {
        String sql = "SELECT ROW_NUMBER() OVER (ORDER BY CAST(OrderIndex AS INT) ASC) AS RID,ID, Code, Type, Title, OrderIndex, PhotoAddress, StartTime, EndTime, CreateTime,CONTENT " +
                "FROM dbo.MH_WarmPrompt WHERE (IsDeleted <> '1' OR IsDeleted IS NULL) AND ID = '" + id + "'";

        try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("OA")) {
            Map<String, Object> objectMap = sqlHelper.selectFirstRow(sql);
            String content = (String) objectMap.get("CONTENT");
            String pattern = "<img\\b[^<>]*>";

            Pattern regexPattern = Pattern.compile(pattern);
            Matcher matcher = regexPattern.matcher(content);
            StringBuffer sb = new StringBuffer();

            while (matcher.find()) {
                String group = matcher.group();
                String htl = "<p style = 'text-align: center;'>" + group + "</p>";
                matcher.appendReplacement(sb, htl);
            }
            matcher.appendTail(sb);
            objectMap.replace("CONTENT", sb.toString());
            return objectMap;
        }
    }


    @Override
    public Object executeSql(String database, String execSql) {
        try (SQLHelper sqlHelper = SQLHelper.createSqlHelper(database)) {
            int i = sqlHelper.executeNonQuery(execSql);
            return CommonResult.execSql(i);
        }
    }

    @Override
    public List<Map<String, Object>> selectSql(String database, String execSql) {
        try (SQLHelper sqlHelper = SQLHelper.createSqlHelper(database)) {
            return sqlHelper.selectRows(execSql);
        }
    }

    private List<Map<String, Object>> checkTime(List<Map<String, Object>> modelList) {
        List<Map<String, Object>> resList = new ArrayList<>();
        DateTimeFormatter dft = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime now = LocalDateTime.now();

        for (Map<String, Object> model : modelList) {
            String endTime = (String) model.get("EndTime");
            String startTime = (String) model.get("StartTime");

            if (ObjUtil.isEmpty(startTime) && ObjUtil.isEmpty(endTime)) {
                resList.add(model);
            } else if (!ObjUtil.isEmpty(startTime) && !ObjUtil.isEmpty(endTime)) {
                LocalDateTime start = LocalDateTime.parse(startTime, dft);
                LocalDateTime end = LocalDateTime.parse(endTime, dft);

                if (start.isBefore(now) && end.isAfter(now)) {
                    resList.add(model);
                }
            } else if (!ObjUtil.isEmpty(startTime) && ObjUtil.isEmpty(endTime)) {
                LocalDateTime start = LocalDateTime.parse(startTime, dft);
                if (start.isBefore(now)) {
                    resList.add(model);
                }
            } else if (ObjUtil.isEmpty(startTime) && !ObjUtil.isEmpty(endTime)) {
                LocalDateTime end = LocalDateTime.parse(endTime, dft);
                if (end.isAfter(now)) {
                    resList.add(model);
                }
            }
        }
        return resList;
    }

    private String getUrlParams(Map<String, Object> dt, String wId) {
        String urlParams = "";
        String goodwaySoft = "KunMing";
        String taskType = dt.get("Type").toString();
        String flowID = dt.get("FlowId").toString();
        String relateId = dt.get("RelateId").toString(); //老系统RelateId字段
        String RelateType = dt.get("RelateType").toString();
        String system = dt.get("System").toString();
        String execUrl = dt.get("ExecUrl").toString();
        String taskserver = (execUrl.trim().startsWith("http://") ? "" : "http://*********:8081");
        String passCode = "khidi";
        if (taskType.equals("NewAuditFlow")) {
            urlParams = "[{url:'" + taskserver + "/workflow/businessframe/TaskBusNew.aspx?WorkItemId" + wId + "',width: 1000, height:630,scrollbars:'yes',callBack:function(data){ getBlockContent('MyTask');}}]";
        } else {
            if (goodwaySoft.equals("KunMing")) {
                if (system.equals("Market")) {
                    urlParams = "[{url:'" + taskserver + "/workflow/businessframe/TaskBus.aspx?WorkItemId=" + wId + "',width: 520, height:200,callBack:function(data){ getBlockContent('MyTask');}}]";
                } else if (system.equals("Project")) {
                    urlParams = "[{url:'" + taskserver + "/Project/WorkSpace/PrjNormalTaskBus.aspx?FlowId=" + flowID + "&ItemId=" + wId + "',width: 820, height:600,callBack:function(data){ getBlockContent('MyTask');}}]";
                } else if (system.equals("Hr")) {
                    urlParams = "[{url:'" + taskserver + "/workflow/businessframe/TaskBus.aspx?WorkItemId=" + wId + "',width: 1000, height:600,callBack:function(data){ getBlockContent('MyTask');}}]";
                } else if (system.equals("TQ")) {
                    urlParams = "[{url:'" + taskserver + "/workflow/businessframe/TaskBus.aspx?WorkItemId=" + wId + "',width: 830, height:630,callBack:function(data){ getBlockContent('MyTask');}}]";
                } else if (system.equals("Other")) {
                    urlParams = "[{url:'" + taskserver + "/workflow/businessframe/TaskBus.aspx?WorkItemId=" + wId + "',width: 820, height:600,callBack:function(data){ getBlockContent('MyTask');}}]";
                } else //Free
                {
                    if (RelateType.contains("PA_IncomingFile")) {
                        urlParams = taskserver + "/officeauto/PartyAffairs/IncomingFile/DoForm.aspx?FormType=E&RelateType=PA_IncomingFile";
                    } else if (RelateType.contains("PA_OutgoingFile")) {
                        urlParams = taskserver + "/officeauto/PartyAffairs/OutgoingFile/DoForm.aspx?FormType=E&RelateType=PA_OutgoingFile";
                    } else {
                        urlParams = taskserver + execUrl + "&RelateType=IncomingFile";
                    }

                    if (urlParams.contains("MiniUIRedirect") && urlParams.contains("sKey=xxzxsolft33246-1209.Step") && urlParams.contains("StepKey=xxzxsolft33246-1432")) {
                        urlParams = urlParams.replace("&WfId", "$WfId");
                        urlParams = urlParams.replace("&UserId", "$UserId");
                        urlParams = urlParams.replace("&WorkNo", "$WorkNo");
                    } else {

                        urlParams = urlParams + " &ItemId=" + wId + "&FlowId=" + flowID + "&RelateId=" + relateId;
                    }
                    //if (execUrl.indexOf("/CommInterface/MiniUIRedirect.aspx?url=FlowPage.aspx&SendId=") != -1) {
                    //    urlParams = "http://10.10.1.28:8080/CommInterface2/FlowPage.aspx?Id=" + StringHelper.GetQueryStringFromUrl(execUrl).Get("SendId");
                    //}
                    urlParams = "[{url:'" + urlParams + "',width: 830, height:600,callBack:function(data){getBlockContent('MyTask');}}]";
                }
            } else if (goodwaySoft.equals("KMJL")) {

                if (ObjUtil.isEmpty(taskType) || taskType.equals("AuditFlow") || taskType.equals("MainFlow")) {
                    if (system.equals("Project")) {

                        urlParams = systemApi.getSettingValue("ConvertServerAddress") + "/Project/WorkSpace/PrjNormalTaskBus.aspx?FlowId=" + flowID + "&ItemId=" + wId + "&PassCode=" + passCode;
                    } else {
                        urlParams = systemApi.getSettingValue("ConvertServerAddress") + "/workflow/businessframe/TaskBus.aspx?WorkItemId=" + wId + "&PassCode=" + passCode;
                    }
                    urlParams = "[{url:'" + urlParams + "',width:1000,height:650,callBack:function(data){getBlockContent('MyTask');}}]";
                } else if (taskType.equals("NewAuditFlow")) {
                    urlParams = systemApi.getSettingValue("ConvertServerAddress") + "/ContainerFrameworkWeb/FlowBus/TaskBusNew.aspx?WorkItemId=" + wId + "&PassCode=" + passCode;
                    urlParams = "[{url:'" + urlParams + "',width:1000,height:650,callBack:function(data){getBlockContent('MyTask');}}]";
                } else if (taskType.equals("RemitFreeFlow")) {
                    urlParams = systemApi.getSettingValue("ConvertServerAddress") + "/workflow/businessframe/RemitFlowBus.aspx?WorkItemId=" + wId + "&PassCode=" + passCode;
                    urlParams = "[{url:'" + urlParams + "',width:1000,height:650,callBack:function(data){getBlockContent('MyTask');}}]";
                } else if (taskType.equals("NewRemitFreeFlow")) {
                    urlParams = systemApi.getSettingValue("ConvertServerAddress") + "/ContainerFrameworkWeb/FlowBus/RemitFlowBusNew.aspx?WorkItemId=" + wId + "&PassCode=" + passCode;
                    urlParams = "[{url:'" + urlParams + "',width:1000,height:650,callBack:function(data){getBlockContent('MyTask');}}]";
                } else if (taskType.equals("AuditTask")) {
                    urlParams = systemApi.getSettingValue("ConvertServerAddress") + "/project/workspace/PrjMyAudit.aspx?FlowId=" + flowID + "&amp;TaskKey=" + wId + "&PassCode=" + passCode;
                    urlParams = "[{url:'" + urlParams + "',width:1000,height:600,callBack:function(data){getBlockContent('MyTask');}}]";
                } else if (taskType.equals("FileFlowEx")) {
                    urlParams = systemApi.getSettingValue("ConvertServerAddress") + "/workflow/businessframe/fileframe/FileBusEx.aspx?WorkItemId=" + wId + "&PassCode=" + passCode;
                    urlParams = "[{url:'" + urlParams + "',width:1000,height:670,callBack:function(data){getBlockContent('MyTask');}}]";
                } else if (taskType.equals("FileFlow")) {
                    urlParams = systemApi.getSettingValue("ConvertServerAddress") + "/workflow/fileflowframe/FileBus.aspx?WorkItemId=" + wId + "&PassCode=" + passCode;
                    urlParams = "[{url:'" + urlParams + "',width:1000,height:650}]";
                } else if (taskType.equals("AutoFormFlow") || taskType.equals("CustomFormFlow")) {
                    urlParams = systemApi.getSettingValue("ConvertServerAddress") + "/workflow/customformflowframe/CustomFormBus.aspx?WorkItemId=" + wId + "&PassCode=" + passCode;
                    urlParams = "[{url:'" + urlParams + "',width:1000,height:650,callBack:function(data){getBlockContent('MyTask');}}]";
                } else if (taskType.equals("ProjectAuditFlow")) {
                    urlParams = systemApi.getSettingValue("ConvertServerAddress") + "/workflow/businessframe/AuditBus.aspx?FlowId=" + flowID + "&WorkItemId=" + wId + "&PassCode=" + passCode;
                    urlParams = "[{url:'" + urlParams + "',width:1000,height:600,callBack:function(data){getBlockContent('MyTask');}}]";
                } else {
                    urlParams = systemApi.getSettingValue("ConvertServerAddress") + execUrl + "&WorkItemId=" + wId + "&FlowId=" + flowID + "&PassCode=" + passCode;
                    urlParams = "[{url:'" + urlParams + "',width:1150,height:600,callBack:function(data){getBlockContent('MyTask');}}]";
                }
            } else if (goodwaySoft.equals("KMY53")) {
                urlParams = "http://*********:8082" + execUrl + "?TaskExecID=" + wId + "&ID=" + relateId;
                urlParams = "[{url:'" + urlParams + "',width:'800px',height:'700px',callBack:function(data){getBlockContent('MyTask');}}]";
            } else if (goodwaySoft.equals("HydroBIMEPC")) {
                urlParams = "[{url:'" + taskserver + execUrl + "',width:1200,height:700,callBack:function(data){getBlockContent('MyTask');}}]";
            } else if (goodwaySoft.equals("JiuQiCaiWu")) {
                urlParams = "[{url:'" + taskserver + execUrl + "',width:1200,height:700,callBack:function(data){getBlockContent('MyTask');}}]";
            }
        }
        return urlParams;
    }

    @Override
    public String DownLoadLogSql(Map<String, Object> file, String optnType, String userIp, String userId, String url) {
        {
            return "INSERT INTO NK_Log.dbo.T_Files_DownloadLog (ID, CreateUser, CreateUserID, CreateTime, DeptID, DeptName, OptnType, UserName, UserID, UserIP, Url, FileID, FileName, ExtName, Src, WorkNo) SELECT NEWID(),Name,ID,GETDATE(),DeptID,DeptName,'{OptnType}',Name,ID,'{UserIP}','{Url}','{file.ID}','{file.Name}','{file.ExtName}','{file.Src}', WorkNo FROM dbo.A_User WHERE ID = '{UserID}'";
        }
    }

    private Boolean sptaskuser(String userId) {
        String sql = "SELECT JSYH FROM A_Group WHERE Code = 'GW_IncomingUsers'";
        try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core")) {
            List<Map<String, Object>> maps = sqlHelper.selectRows(sql);
            if (ObjUtil.isEmpty(maps) || maps.isEmpty()) {
                return false;
            }
            String jsyh = maps.get(0).get("JSYH").toString();
            List<String> list = Arrays.asList(jsyh.split(","));
            return list.contains(userId);
        }
    }
}
