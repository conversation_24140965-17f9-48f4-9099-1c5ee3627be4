package mh.cloud.module.system.service.portal.impl;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.TypeReference;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import mh.cloud.module.system.dal.db.StringHelper;
import mh.cloud.module.system.service.db.SQLHelper;
import mh.cloud.module.system.service.portal.ApplyService;
import mh.cloud.module.system.service.portal.VO.Apply;
import mh.cloud.module.system.service.portal.VO.WFMyFlow;
import mh.cloud.module.system.service.portal.WorkCenterService;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static mh.cloud.framework.web.core.util.WebFrameworkUtils.getLoginUserId;
@Service
@Slf4j
public class ApplyServiceImpl implements ApplyService {
    @Resource
    private WorkCenterService workCenterService;
    public List<Map<String, Object>> getApply(String type, Timestamp beginTime, Timestamp endTime, String activity) {
        try (SQLHelper sqlHelper = SQLHelper.CreateSqlHelper("Core");
            /* SQLHelper db13sqlHelper = SQLHelper.CreateSqlHelper("data_1_3");
             SQLHelper helperTemp = SQLHelper.createSqlHelper("RIITDB");*/
        ) {
            String whereStr = "";
            String sql;
            List<Map<String, Object>>  dt = new ArrayList<>();
            if (activity != null && !activity.isEmpty()) {
                whereStr = "KD4".equals(type) || "RIIT".equals(type) ? " AND B.Name like '%" + activity + "%'" : " AND TaskName LIKE '%" + activity + "%'";
            }
            switch (type) {
                case "KD4":
                    sql = "SELECT A.ID, B.Name Title, B.URL, C.CreateUser, C.CreateUserID, C.CreateTime, " +
                            "CASE C.Status WHEN 'Processing' THEN '流程中' WHEN 'Complete' THEN '已完成' ELSE '已终止' END FlowState, " +
                            "C.DefFlowCode, C.FormInstanceID, '' TempletCode, '昆明院综合办公系统' SysType, '" + type + "' SysPT " +
                            "FROM dbo.WF_Task A LEFT JOIN dbo.WF_Activity B ON B.ID = A.ActivityID " +
                            "LEFT JOIN dbo.WF_Flow C ON C.ID = B.FlowID " +
                            "WHERE A.ExecUserID = '" + getLoginUserId() + "' AND A.PreTaskID IS NULL " +
                            "AND C.MainFlowID IS NULL AND C.Status <> 'Cancel' " + whereStr + " AND C.CreateTime BETWEEN '" + beginTime + "' AND '" + endTime + "'";
                    dt = sqlHelper.selectRows(sql);
                    break;
                /*case "RIIT":
                    sql = "SELECT A.ID, B.Name Title, B.URL, C.CreateUser, C.CreateUserID, C.CreateTime, " +
                            "CASE C.Status WHEN 'Processing' THEN '流程中' WHEN 'Complete' THEN '已完成' ELSE '已终止' END FlowState, " +
                            "C.DefFlowCode, C.FormInstanceID, '' TempletCode, '信息院综合办公系统' SysType, '" + type + "' SysPT " +
                            "FROM dbo.WF_Task A LEFT JOIN dbo.WF_Activity B ON B.ID = A.ActivityID " +
                            "LEFT JOIN dbo.WF_Flow C ON C.ID = B.FlowID " +
                            "WHERE A.ExecUserID = '" + getLoginUserId() + "' AND A.PreTaskID IS NULL " +
                            "AND C.MainFlowID IS NULL AND C.Status <> 'Cancel' " + whereStr + " AND C.CreateTime BETWEEN '" + beginTime + "' AND '" + endTime + "'";

                    dt = helperTemp.selectRows(sql);
                    break;
                case "MINIUI":
                    sql = "SELECT A.Id ID, A.TaskName Title, A.ExecUrl URL, A.OwnerUserName CreateUser, A.OwnerUserId CreateUserID, A.CreateTime, " +
                            "CASE WHEN (SELECT COUNT(1) FROM dbo.WfWorkList WHERE FlowId = A.FlowId AND RelateId = A.RelateId AND State = 'New') > 0 THEN '流程中' " +
                            "WHEN (SELECT COUNT(1) FROM dbo.WfWorkList WHERE FlowId = A.FlowId AND RelateId = A.RelateId AND State = 'End' AND FinishTime IS NULL) > 0 THEN '已终止' " +
                            "ELSE '已完成' END FlowState, A.FlowId DefFlowCode, A.RelateId FormInstanceID, B.sKey TempletCode, '昆明院综合办公系统' SysType, '" + type + "' SysPT " +
                            "FROM ( SELECT * FROM KunMing_BeAdmin.dbo.WfWorkList WHERE SendUserId = '" + getLoginUserId() + "' " +
                            "AND REPLACE(ParentId, ' ', '') = 'WFW-1' " + whereStr + " AND CreateTime BETWEEN '" + beginTime + "' AND '" + endTime + "' ) A " +
                            "INNER JOIN StepTable.dbo.GwSendTable B ON B.Id = A.StepTableId ";
                    dt = db13sqlHelper.selectRows(sql);
                    break;*/
            }
            return dt;
        }
    }

    public List<Apply> getApplyList(Timestamp beginTime, Timestamp endTime, String activity) {
        List<Map<String, Object>> dt1 = getApply("KD4", beginTime, endTime, activity);

/*        List<Map<String, Object>> dt2 = getApply("MINIUI", beginTime, endTime, activity);

        List<Map<String, Object>> dt3 = getApply("RIIT", beginTime, endTime, activity);*/

        List<Apply> list = new ArrayList<>();
        TypeReference<List<Apply>> typeRef = new TypeReference<>() {};
        if (dt1.size() > 0) {
            list.addAll(Convert.convert(typeRef, dt1));
        }
        /*if (dt2.size() > 0) {
            list.addAll(Convert.convert(typeRef, dt2));
        }
        if (dt3.size() > 0) {
            list.addAll(Convert.convert(typeRef, dt3));
        }
*/
        return list;
    }


    public Map<String, List<Apply>> getPageApply(int page, int pageSize, Timestamp beginTime, Timestamp endTime, String activity) {
        Map<String, List<Apply>> dic = new HashMap<>();
        List<Apply> list = getApplyList(beginTime, endTime, activity);
        dic.put("OldList", list);

        if (!list.isEmpty()) {
            page--;
            list = list.stream()
                    .sorted(Comparator.comparing(Apply::getCreateTime).reversed())
                    .skip(page * pageSize)
                    .limit(pageSize)
                    .collect(Collectors.toList());
        }

        List<Map<String, Object>> focus = workCenterService.getFocus(activity);
        TypeReference<List<WFMyFlow>> typeRef = new TypeReference<>() {};
        List<WFMyFlow> myflowlist = Convert.convert(typeRef, focus);

        // 处理当前审批步骤信息
        for (Apply app : list) {
            if ("流程中".equals(app.getFlowState())) {
                List<Map<String, Object>> dt = getNewTaskFlowStepByID(app.getSysPT().toUpperCase(), app.getFormInstanceID(), app.getDefFlowCode());
                if (IterUtil.isNotEmpty(dt)) {
                    app.setCurrentStep(dt.get(0).get("FlowStep").toString());
                } else {
                    app.setCurrentStep("-");
                }
            } else {
                app.setCurrentStep("-");
            }

            if ("MINIUI".equals(app.getSysPT().toUpperCase())) {
                app.setURL("/UIBuilder/UIViewer/TempFormViewer?TempletCode=TempForm_ac02011202724323a4aff170b76ec32c&pa=" + app.getTempletCode() + "|" + app.getDefFlowCode() + "|" + app.getFormInstanceID());
            } else if ("KD4".equals(app.getSysPT().toUpperCase())) {
                String templetCode = StringHelper.getQueryStringFromUrl(app.getURL()).get("TempletCode");
                app.setURL("/UIBuilder/UIViewer/FlowPage?TempletCode=" + templetCode + "&ID=" + app.getFormInstanceID() + "&FlowCode=" + app.getDefFlowCode() + "&FuncType=view");
            } else {
                String templetCode = StringHelper.getQueryStringFromUrl(app.getURL()).get("TempletCode");
                app.setURL("http://10.10.1.74/UIBuilder/UIViewer/FlowPage?TempletCode=" + templetCode + "&ID=" + app.getFormInstanceID() + "&FlowCode=" + app.getDefFlowCode() + "&FuncType=view");
            }
            // 处理我的申请关注的
            if (("KD4".equals(app.getSysPT()) || "RIIT".equals(app.getSysPT()) || "MINIUI".equals(app.getSysPT())) &&
                    myflowlist.stream().anyMatch(c -> c.getTaskID().equals(app.getID()))) {
                app.setIsFocus("true");
            } else {
                app.setIsFocus("false");
            }
        }

        dic.put("NewList", list);
        return dic;
    }
    public List<Map<String, Object>> getNewTaskFlowStepByID(String type, String formInstanceID, String defFlowCode) {
        try(SQLHelper sqlHelper = SQLHelper.CreateSqlHelper("Core");
            SQLHelper riitHelper = SQLHelper.createSqlHelper("RIITDB");
            SQLHelper db11sqlHelper = SQLHelper.CreateSqlHelper("data_1_1");
        ){
            String sql;
            List<Map<String, Object>> result = new ArrayList<>();
            switch (type) {
                case "KD4":
                    sql = "SELECT STUFF((SELECT ',' + t.FlowStep FROM (" +
                            "SELECT C.Name + '(' + B.ExecUserName + ')' FlowStep FROM dbo.WF_Activity A" +
                            " LEFT JOIN dbo.WF_Task B ON B.ActivityID = A.ID" +
                            " INNER JOIN dbo.WF_DefActivity C ON C.ID = A.DefActivityID" +
                            " WHERE A.FormInstanceID = ? AND B.ExecTime IS NULL) t" +
                            " FOR XML PATH('')),1,1,'') FlowStep";
                    result = sqlHelper.selectRows(sql, formInstanceID);
                    break;
                case "RIIT":
                    sql = "SELECT STUFF((SELECT ',' + t.FlowStep FROM (" +
                            "SELECT C.Name + '(' + B.ExecUserName + ')' FlowStep FROM dbo.WF_Activity A" +
                            " LEFT JOIN dbo.WF_Task B ON B.ActivityID = A.ID" +
                            " INNER JOIN dbo.WF_DefActivity C ON C.ID = A.DefActivityID" +
                            " WHERE A.FormInstanceID = ? AND B.ExecTime IS NULL) t" +
                            " FOR XML PATH('')),1,1,'') FlowStep";

                    result = riitHelper.selectRows(sql, formInstanceID);
                    break;
                case "MINIUI":
                    sql = "SELECT STUFF((SELECT ',' + t.FlowStep FROM (" +
                            "SELECT st.步骤名称 + '(' + wf.ReciveUserName + ')' FlowStep FROM (" +
                            "SELECT * FROM StepTable.dbo.GwSendTable WHERE StepKey = ? AND GwId = ? AND GwState = 'New'" +
                            ") wf LEFT JOIN (SELECT * FROM StepTable.dbo.SetStepTable WHERE StepKey = ?) st ON st.步骤序号 = wf.NowStep) t" +
                            " FOR XML PATH('')),1,1,'') FlowStep";
                    result = db11sqlHelper.selectRows(sql, defFlowCode, formInstanceID.replace("Gw.", ""), defFlowCode);
                    break;
            }

            return result;
        }

    }


}
