package mh.cloud.module.system.service.portal.VO;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
public class Apply {
    public String ID ;
    /// <summary>
    /// 标题
    /// </summary>
    public String Title ;

    /// <summary>
    /// 浏览地址
    /// </summary>
    public String URL ;
    /// <summary>
    /// 创建人
    /// </summary>
    public String CreateUser ;
    /// <summary>
    /// 创建人ID
    /// </summary>
    public String CreateUserID ;
    /// <summary>
    /// 申请时间
    /// </summary>
    public String CreateTime ;
    /// <summary>
    /// 流程状态
    /// </summary>
    public String FlowState ;
    /// <summary>
    /// 流程标识
    /// </summary>
    public String DefFlowCode ;
    /// <summary>
    /// 记录ID
    /// </summary>
    public String FormInstanceID ;
    /// <summary>
    /// 表单标识
    /// </summary>
    public String TempletCode ;
    /// <summary>
    /// 当前审批步骤
    /// </summary>
    public String CurrentStep ;
    /// <summary>
    /// 系统来源
    /// </summary>
    public String SysType ;
    /// <summary>
    /// 系统平台
    /// </summary>
    public String SysPT ;
    /// <summary>
    /// 是否关注
    /// </summary>
    public String IsFocus ;

}
