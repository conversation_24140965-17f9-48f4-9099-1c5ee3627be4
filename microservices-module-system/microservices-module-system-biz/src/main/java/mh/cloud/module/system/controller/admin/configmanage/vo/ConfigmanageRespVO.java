package mh.cloud.module.system.controller.admin.configmanage.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 系统配置结构树 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ConfigmanageRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "4372")
    @ExcelProperty("ID")
    private String id;

    @Schema(description = "父ID", example = "24317")
    @ExcelProperty("父ID")
    private String parentID;

    @Schema(description = "全ID", example = "14198")
    @ExcelProperty("全ID")
    private String fullID;

    @Schema(description = "排序索引")
    @ExcelProperty("排序索引")
    private Integer sortIndex;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("名称")
    private String name;

    @Schema(description = "code")
    @ExcelProperty("code")
    private String code;

    @Schema(description = "节点类型", example = "2")
    @ExcelProperty("节点类型")
    private String type;

    @Schema(description = "iconcls")
    @ExcelProperty("iconcls")
    private String iconCls;

    @Schema(description = "主界面地址", example = "https://www.iocoder.cn")
    @ExcelProperty("主界面地址")
    private String url;

    @Schema(description = "控制类型", example = "2")
    @ExcelProperty("控制类型")
    private String ctrlType;

    @Schema(description = "权限，可以为页面控件ID，数据的查询条件")
    @ExcelProperty("权限，可以为页面控件ID，数据的查询条件")
    private String auth;

    @Schema(description = "描述", example = "你猜")
    @ExcelProperty("描述")
    private String description;

    @Schema(description = "子系统编号")
    @ExcelProperty("子系统编号")
    private String systemCode;

    @Schema(description = "创建人")
    @ExcelProperty("创建人")
    private String createUser;

    @Schema(description = "创建用户id", example = "7867")
    @ExcelProperty("创建用户id")
    private String createUserID;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改用户")
    @ExcelProperty("修改用户")
    private String modifyUser;

    @Schema(description = "修改用户id", example = "31093")
    @ExcelProperty("修改用户id")
    private String modifyUserID;

    @Schema(description = "修改时间")
    @ExcelProperty("修改时间")
    private LocalDateTime modifyTime;

    @Schema(description = "节点状态（未发布，已发布）", example = "2")
    @ExcelProperty("节点状态（未发布，已发布）")
    private String status;

    @Schema(description = "节点编辑权限")
    @ExcelProperty("节点编辑权限")
    private String editAuth;

    @Schema(description = "节点编辑权限")
    @ExcelProperty("节点编辑权限")
    private String editAuthUser;

    @Schema(description = "节点的连接页面", example = "https://www.iocoder.cn")
    @ExcelProperty("节点的连接页面")
    private String configUrl;

    @Schema(description = "主数据库连接")
    @ExcelProperty("主数据库连接")
    private String mainDBConn;

    @Schema(description = "relateId", example = "2096")
    @ExcelProperty("relateId")
    private String relateID;

    @Schema(description = "关联表")
    @ExcelProperty("关联表")
    private String relateTable;

    @Schema(description = "是否主界面", example = "https://www.iocoder.cn")
    @ExcelProperty("是否主界面")
    private String isMainUrl;

    @Schema(description = "是否删除")
    @ExcelProperty("是否删除")
    private String isDeleted;

    @Schema(description = "isStandard")
    @ExcelProperty("isStandard")
    private String isStandard;

}