package mh.cloud.module.system.controller.admin.portal.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.servlet.http.HttpServletRequest;
import mh.cloud.module.system.controller.admin.portal.V0.ProjectTree;
import mh.cloud.module.system.dal.dataobject.auser.AUserDO;

import java.util.List;
import java.util.Map;

public interface HomeService {
    Page<Map<String, Object>> getYnfw(int pageNumber, int pageSize);

    Page<Map<String, Object>> getGsxw(int pageNumber, int pageSize);

    Page<Map<String, Object>> getTzgg(int pageNumber, int pageSize);

    Page<Map<String, Object>> getZxxx(int pageNumber, int pageSize, String code);

    /**
     * 查询项目中心信息
     *
     * @param id 用户ID
     * @return 项目中心信息
     */
    List<ProjectTree> queryXmzxUnitInfos(String id);

    /**
     * 获取当前登录用户待办工作数，未读消息数
     *
     * @return 待办工作数，未读消息数
     */
    Map<String, Object> getMsgCount(String userId);

    /**
     * 4A认证接口处理
     *
     * @return 认证url
     */
    boolean AuthenticateBy4A(String remoteAddr, AUserDO aUserDO);

    String set4ACookie(String remoteAddr, String userId);

    /**
     * 处理获取发送电建通验证码
     *
     * @param workNo    登录用户
     * @param sendType  发送类型
     * @param appWorkNo 电建通账号
     * @param request
     * @return 验证码
     */
    String GetVerificationCode(String workNo, Integer sendType, String appWorkNo, HttpServletRequest request);


    List<Map<String,Object>> getZXZXHeaderImg();
}
