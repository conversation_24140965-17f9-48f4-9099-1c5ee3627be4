package mh.cloud.module.system.controller.admin.grouprelation.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 分组内部关联新增/修改 Request VO")
@Data
public class GrouprelationSaveReqVO {

    @Schema(description = "A_Groupd的id", requiredMode = Schema.RequiredMode.REQUIRED, example = "18823")
    private String parentGroupID;

    @Schema(description = "UserID", requiredMode = Schema.RequiredMode.REQUIRED, example = "14312")
    private String childGroupID;

    @Schema(description = "全路径ID", example = "17758")
    private String deptFullID;

    @Schema(description = "类型", example = "2")
    private String relationType;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "创建时间不能为空")
    private LocalDateTime createTime;

}