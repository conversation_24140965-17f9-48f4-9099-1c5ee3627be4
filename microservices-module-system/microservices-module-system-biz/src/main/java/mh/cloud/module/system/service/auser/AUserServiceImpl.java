package mh.cloud.module.system.service.auser;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.util.object.BeanUtils;
import mh.cloud.framework.tenant.core.context.TenantContextHolder;
import mh.cloud.module.system.controller.admin.auser.vo.AUserPageReqVO;
import mh.cloud.module.system.controller.admin.auser.vo.AUserSaveReqVO;
import mh.cloud.module.system.controller.admin.auser.vo.ResetPwdReqVO;
import mh.cloud.module.system.controller.admin.auth.vo.AuthUpdatePasswordReqVO;
import mh.cloud.module.system.dal.dataobject.auser.AUserDO;
import mh.cloud.module.system.dal.dataobject.auser.AUserDoChild;
import mh.cloud.module.system.dal.dataobject.auser.AUserPassword;
import mh.cloud.module.system.dal.dataobject.group.AGroupDO;
import mh.cloud.module.system.dal.dataobject.grouprelation.GrouprelationDO;
import mh.cloud.module.system.dal.dataobject.logger.ChangePasswordLogDO;
import mh.cloud.module.system.dal.dataobject.otherjobrelation.OtherjobRelationDO;
import mh.cloud.module.system.dal.mysql.auser.AUserMapper;
import mh.cloud.module.system.dal.mysql.group.AGroupMapper;
import mh.cloud.module.system.dal.mysql.grouprelation.GrouprelationMapper;
import mh.cloud.module.system.dal.mysql.logger.ChangePasswordLogMapper;
import mh.cloud.module.system.dal.mysql.otherjobrelation.OtherjobRelationMapper;
import mh.cloud.module.system.service.db.SQLHelper;
import mh.cloud.module.system.service.oauth2.OAuth2TokenService;
import mh.cloud.module.system.util.oauth2.OAuth2Utils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static mh.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static mh.cloud.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static mh.cloud.module.system.enums.ErrorCodeConstants.*;

/**
 * 个人设置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AUserServiceImpl implements AUserService {

    @Resource
    private AUserMapper aUserMapper;

    @Resource
    private ChangePasswordLogMapper changePasswordLogMapper;

    @Resource
    private GrouprelationMapper grouprelationMapper;

    @Resource
    private AGroupMapper groupMapper;

    @Resource
    private OtherjobRelationMapper otherjobRelationMapper;

    @Resource
    private RedisTemplate redisTemplate;


    @Resource
    private OAuth2TokenService oAuth2TokenService;

    @Override
    public String createAUser(AUserSaveReqVO createReqVO) {
        // 插入
        AUserDO aUser = BeanUtils.toBean(createReqVO, AUserDO.class);
        aUserMapper.insert(aUser);
        /*saveRelation(aUser.getId(), createReqVO.getDeptID());
        saveOtherRelation(aUser.getId(),createReqVO.getDeptID());*/
        // 返回
        return aUser.getId();
    }

    private void updateRelation(String userId, String deptId) {
        //先清理原有的关系数据，组织数据
        int i = grouprelationMapper.deleteAllRelationData(userId);
        //新建组织数据
        saveRelation(userId, deptId);
    }

    private void updateRelationOther(String userId, String deptId) {
        //先清理原有的关系数据，组织数据
        int i = otherjobRelationMapper.deleteAllRelationData(userId);
        //新建组织数据
        saveOtherRelation(userId, deptId);
    }

    private void saveRelation(String userId, String deptId) {
        AGroupDO aGroupDO = groupMapper.selectById(deptId);
        GrouprelationDO ra = new GrouprelationDO();
        ra.setRelationType("Org");
        ra.setCreateTime(LocalDateTime.now());
        Long tenantId = TenantContextHolder.getTenantId();
        ra.setTenantId(tenantId + "");
        ra.setChildGroupID(userId);
        ra.setParentGroupID(deptId);
        ra.setDeptFullID(aGroupDO.getFullID());
        ra.setRelationType("User");
        grouprelationMapper.insert(ra);
        if (StrUtil.isNotEmpty(aGroupDO.getParentID())) {
            saveRelation(userId, aGroupDO.getParentID());
        }
    }

    private void saveOtherRelation(String userId, String deptId) {
        AGroupDO aGroupDO = groupMapper.selectById(deptId);
        OtherjobRelationDO ra = new OtherjobRelationDO();
        ra.setRelationType("Org");
        ra.setCreateTime(LocalDateTime.now());
        Long tenantId = TenantContextHolder.getTenantId();
        ra.setTenantId(tenantId + "");
        ra.setChildGroupID(userId);
        ra.setParentGroupID(deptId);
        ra.setDeptFullID(aGroupDO.getFullID());
        ra.setRelationType("User");
        otherjobRelationMapper.insert(ra);
        if (StrUtil.isNotEmpty(aGroupDO.getParentID())) {
            saveOtherRelation(userId, aGroupDO.getParentID());
        }
    }

    @Override
    public void updateAUser(AUserSaveReqVO updateReqVO) {
        //部门关系表单独处理
       /* String deptId = updateReqVO.getDeptID();
        String userId = updateReqVO.getId();
        updateRelation(userId, deptId);
        //兼职关系表单独处理
        updateRelationOther(userId,deptId);*/
        // 校验存在
        validateAUserExists(updateReqVO.getId());
        // 更新
        AUserDO updateObj = BeanUtils.toBean(updateReqVO, AUserDO.class);
        aUserMapper.updateById(updateObj);
    }

    @Override
    public void deleteAUser(String id) {
        //删除关系信息
//        grouprelationMapper.deleteAllRelationData(id);
//        otherjobRelationMapper.deleteAllRelationData(id);

        // 校验存在

        validateAUserExists(id);
        // 删除
        aUserMapper.deleteById(id);
    }

    private void validateAUserExists(String id) {
        if (aUserMapper.selectById(id) == null) {
            throw exception(A_USER_NOT_EXISTS);
        }
    }

    @Override
    public AUserDO getAUser(String id) {
        //查询用户
        AUserDO aUserDO = aUserMapper.selectById(id);
        String avatarByUserId = null;
        try {
            avatarByUserId = aUserMapper.getAvatarByUserId(id).get(0);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StrUtil.isNotEmpty(avatarByUserId)) {
            aUserDO.setAvatar(avatarByUserId);
        }
        return aUserDO;
    }

    /**
     * 获得个人设置
     *
     * @param id 编号
     * @return 个人设置
     */
    @Override
    public AUserDO getAUserInfo(String id) {
        return aUserMapper.selectById(id);
    }

    @Override
    public PageResult<AUserDO> getAUserPage(AUserPageReqVO pageReqVO) {
        return aUserMapper.selectPage(pageReqVO);
    }

    @Override
    public void updateUserLastTime(AUserSaveReqVO createReqVO) {
        LambdaUpdateWrapper<AUserDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(AUserDO::getLastLoginTime, createReqVO.getModifyTime())
                .set(AUserDO::getModifyTime, createReqVO.getModifyTime())
                .set(AUserDO::getLastLoginIP, createReqVO.getLastLoginIP())
                .eq(AUserDO::getId, createReqVO.getId());
        aUserMapper.update(wrapper);
    }

    @Override
    public AUserDO getByUserName(String username) {
        return aUserMapper.selectOne(AUserDO::getLoginName, username);
    }

    @Override
    public Boolean resetPwd(ResetPwdReqVO reqVO) {
        AUserDO aUser = this.getAUser(reqVO.getUserId());
        if (aUser == null) {
            throw exception(A_USER_NOT_EXISTS);
        }
        UpdateWrapper<AUserDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("Password", OAuth2Utils.encryptionPassword(reqVO.getPassword()));
        updateWrapper.eq("ID", reqVO.getUserId());
        aUserMapper.update(updateWrapper);
        return true;
    }

    @Override
    public PageResult<AUserDoChild> getCustomAUserPage(AUserPageReqVO pageReqVO) {
        Page<AUserDoChild> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        QueryWrapper<AUserDoChild> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("IsDeleted", "0");
        List<String> deptIds = pageReqVO.getDeptIds();
        Page<AUserDoChild> list = aUserMapper.selectCustomPage(page, queryWrapper, deptIds == null ? null : deptIds.get(0));
        PageResult<AUserDoChild> r = new PageResult<>();
        r.setTotal(list.getTotal());
        r.setList(list.getRecords());
        return r;
    }

    @Override
    public Integer updateLoginFailCount(String userId, Integer count) {
        LambdaUpdateWrapper<AUserDO> wrapperX = new LambdaUpdateWrapper<>();
        wrapperX.eq(AUserDO::getId, userId)
                .set(AUserDO::getErrorCount, count)
                .set(AUserDO::getErrorTime, LocalDateTime.now());
        return aUserMapper.update(wrapperX);
    }

    @Override
    @Transactional(rollbackFor = Exception.class) // 添加事务，异常则回滚所有修改数据
    public Boolean updateAUserPassword(AuthUpdatePasswordReqVO reqVO) {
        // 参数校验
        if (reqVO == null) {
            throw exception(HTTP_REQ_CODE_PARAMETER_ERROR);
        }

        // 获取用户信息
        AUserDO user = this.getUserSimpInfoByWorkNo(reqVO.getWorkNo());
        if (user == null) {
            throw exception(AUTH_UPDATE_PASSWORD_ERROR);
        }

        // 根据不同的修改方式处理
        switch (reqVO.getActive()) {
            case "1": // 直接修改密码
                validateOldPassword(user, reqVO.getOldPassword());
                break;
            case "2": // 电建验证码修改
                validateVerificationCode(reqVO.getCodeId(), reqVO.getAppVerCode());
                break;
            default:
                throw exception(HTTP_REQ_CODE_PARAMETER_ERROR);
        }

        // 记录日志并更新密码
        changePasswordLog(user, reqVO);
        return updateEncryptionPassword(reqVO.getNewPassword(), user.getId());
    }
    private void validateOldPassword(AUserDO user, String oldPassword) {
        if (!OAuth2Utils.encryptionPassword(oldPassword).equals(user.getPassword())) {
            throw exception(AUTH_UPDATE_PASSWORD_ERROR);
        }
    }

    private void validateVerificationCode(String codeId, String verificationCode) {
        if (!CheckVerificationCode(codeId, verificationCode)) {
            throw exception(SMS_CODE_EXPIRED);
        }
    }
    // 记录日志并更新密码
    private Boolean changePasswordLog(AUserDO aUser, AuthUpdatePasswordReqVO reqVO) {
        String newPassword =  new String(Base64.getDecoder().decode(reqVO.getNewPassword()));
        String oldPassword =  new String(Base64.getDecoder().decode(reqVO.getOldPassword()));
        String newPas = OAuth2Utils.encryptionPassword(reqVO.getNewPassword());
        String oldPas = OAuth2Utils.encryptionPassword(reqVO.getOldPassword());
        String md5NewPas = SecureUtil.md5(newPassword).toUpperCase();
        String md5OldPas = SecureUtil.md5(oldPassword).toUpperCase();
        ChangePasswordLogDO changePasswordLogDO = new ChangePasswordLogDO();
        changePasswordLogDO.setUserId(aUser.getId());
        changePasswordLogDO.setUserName(aUser.getName());
        changePasswordLogDO.setLastPassword(oldPas);
        changePasswordLogDO.setCurrentPassword(newPas);
        changePasswordLogDO.setCurrentChangeTime(LocalDateTime.now());
        changePasswordLogDO.setLastPasswordMD5(md5OldPas);
        changePasswordLogDO.setCurrentPasswordMD5(md5NewPas);
        changePasswordLogDO.setWorkNo(aUser.getWorkNo());
        changePasswordLogDO.setRemark("");
        return changePasswordLogMapper.insert(changePasswordLogDO) >= 1;
    }


    @Override
    @Transactional(rollbackFor = Exception.class) // 添加事务，异常则回滚所有修改数据
    public Boolean resetUserPassword(Map<String, AUserPassword> reqVO) {
        AUserPassword user = reqVO.get("user");
        if (user == null) {
            return false; // 无有效数据
        }

        boolean  Success = true;
        if (!updatePassword(user.getPwd(), user.getUserId())) {
            Success = false;
        }
        return Success;
    }


    @Override
    @Transactional(rollbackFor = Exception.class) // 添加事务，异常则回滚所有修改数据
    public Boolean batchSettingsPassword(Map<String, List<AUserPassword>> reqVO) {
        // 1. 校验当前用户权限
        AUserDO currentUser = this.getAUser(getLoginUserId());
        if (!"2018147".equals(currentUser.getWorkNo())) {
            return false; // 无权限
        }

        // 2. 校验输入数据
        List<AUserPassword> users = reqVO.get("users");
        if (users == null || users.isEmpty()) {
            return false; // 无有效数据
        }

        // 3. 批量更新密码
        boolean allSuccess = true;
        for (AUserPassword user : users) {
            if (!updatePassword(user.getPwd(), user.getUserId())) {
                allSuccess = false; // 修改失败
            }
        }
        return allSuccess;
    }

    private Boolean updatePassword(String newPas, String userId) {
        if (!ObjUtil.isAllNotEmpty(newPas, userId)) {
            throw exception(AUTH_UPDATE_PARTEM_ERROR);
        }
        //修改密码
        LambdaUpdateWrapper<AUserDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(AUserDO::getPassword, OAuth2Utils.encryptionDefaultSPassword(newPas))
                .eq(AUserDO::getId, userId);
        return aUserMapper.update(wrapper) >= 1;
    }

    private Boolean updateEncryptionPassword(String newPas, String userId) {
        if (!ObjUtil.isAllNotEmpty(newPas, userId)) {
            throw exception(AUTH_UPDATE_PARTEM_ERROR);
        }
        //修改密码
        LambdaUpdateWrapper<AUserDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(AUserDO::getPassword, OAuth2Utils.encryptionPassword(newPas))
                .eq(AUserDO::getId, userId);
        return aUserMapper.update(wrapper) >= 1;
    }


    @Override
    public void sendCode(AuthUpdatePasswordReqVO reqVO) {

    }

    public final static String powerChinaCachePrefix = "powerchina-verificationcode";
    public final static String mobileTextMessageCachePrefix = "mobile-verificationcode";
    public final static String verificationCodeJsonDataPrefix = "verification-code-json-data";

    //生成发送数据相关
    public Map<String, String> createCode(AuthUpdatePasswordReqVO reqVO) {
        //生成验证码
        SecureRandom secureRandom = new SecureRandom();
        // 生成一个六位数的随机数
        String code = String.valueOf(100000 + secureRandom.nextInt(900000));
        //缓存验证码到redis
        Integer timeOut = 0;
        if (reqVO.getActive().equals("2")) {
            timeOut = 300;
            //注释redis 验证码使用
            redisTemplate.opsForValue().set(powerChinaCachePrefix + reqVO.getWorkNo(), code, timeOut, TimeUnit.SECONDS);
        } else if (reqVO.getActive().equals("3")) {
            timeOut = 1000;
            redisTemplate.opsForValue().set(mobileTextMessageCachePrefix + reqVO.getWorkNo(), code, timeOut, TimeUnit.SECONDS);
        }
        //获取短信/电建通消息格式

        //日志存入数据库

        //拼接发送数据
        return null;
    }

    /**
     * 获取用户签名
     *
     * @param userId 用户id
     * @return 签名信息
     */
    @Override
    public String getSignImg(String userId) {
        return aUserMapper.selectSinggImg(userId);
    }

    @Override
    public List<AUserDO> getUserAll(String name) {
        return aUserMapper.selectUserLimit("%" + name + "%");
    }

    /**
     * 获取用户简单信息
     */
    @Override
    public AUserDO getUserSimpInfo(String loginUserId) {
        AUserDO aUserDO = aUserMapper.selectOne(Wrappers.<AUserDO>lambdaQuery()
                .select(AUserDO::getId, AUserDO::getName, AUserDO::getWorkNo, AUserDO::getLoginName, AUserDO::getDeptID, AUserDO::getDuties)
                .eq(AUserDO::getId, loginUserId));
        //处理特殊格式
        if ("".equals(aUserDO.getDuties())) {
            aUserDO.setDuties(null);
        }
        return aUserDO;
    }

    @Override
    public AUserDO getUserSimpInfoByWorkNo(String workNo) {
        return aUserMapper.selectOne(Wrappers.<AUserDO>lambdaQuery()
                .select(AUserDO::getId, AUserDO::getName, AUserDO::getWorkNo, AUserDO::getLoginName, AUserDO::getDeptID, AUserDO::getDuties, AUserDO::getPassword, AUserDO::getUserType)
                .eq(AUserDO::getWorkNo, workNo));
    }

    @Override
    public AUserDO selectUserNameAndIsDeleted(String name, String password) {
        AUserDO aUserDO = aUserMapper.selectUserNameAndIsDeleted(name, password);
        return aUserDO;
    }


    private Boolean CheckVerificationCode(String id, String code) {
        String sql = "SELECT ID, UserID, WorkNo, Code,ExpireTime,InsertTime FROM A_UserVerificationCode WHERE ID = '" + id + "'";
        try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core")) {
            Map<String, Object> map = sqlHelper.selectFirstRow(sql);
            if (ObjUtil.isEmpty(map)) {
                throw exception(SMS_CODE_NOT_FOUND);
            }
            String checkCode = (String) map.get("Code");
            String insertTime = map.get("InsertTime") + "";
            String expireTime = map.get("ExpireTime") + "";
            if (ObjUtil.isEmpty(checkCode) || !checkCode.equals(code)) {
                throw exception(SMS_CODE_NOT_CORRECT);
            }
            //获取到过期日期
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime parse = LocalDateTime.parse(insertTime, dtf);
            LocalDateTime expire = parse.plusSeconds(Long.parseLong(expireTime));
            //过期时间在当前时间之后，未过期
            return expire.isAfter(now);
        }
    }
}
