package mh.cloud.module.system.controller.admin.auser;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import mh.cloud.framework.apilog.core.annotation.ApiAccessLog;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.framework.common.pojo.PageParam;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.util.object.BeanUtils;
import mh.cloud.framework.excel.core.util.ExcelUtils;
import mh.cloud.module.system.controller.admin.auser.vo.AUserPageReqVO;
import mh.cloud.module.system.controller.admin.auser.vo.AUserRespVO;
import mh.cloud.module.system.controller.admin.auser.vo.AUserSaveReqVO;
import mh.cloud.module.system.controller.admin.auser.vo.ResetPwdReqVO;
import mh.cloud.module.system.dal.dataobject.auser.AUserDO;
import mh.cloud.module.system.dal.dataobject.auser.AUserDoChild;
import mh.cloud.module.system.service.ares.AResService;
import mh.cloud.module.system.service.auser.AUserService;
import mh.cloud.module.system.service.group.AGroupService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static mh.cloud.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static mh.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 个人设置")
@RestController
@RequestMapping("/system/A-user")
@Validated
public class AUserController {
    @Resource
    private AResService aResService;


    @Resource
    private AUserService aUserService;

    @Resource
    private AGroupService aGroupService;

    @PostMapping("/resetPwd")
    @PermitAll
    public CommonResult<Boolean> resetPwd(@RequestBody @Valid ResetPwdReqVO reqVO) {
        return success(aUserService.resetPwd(reqVO));
    }

    @PostMapping("/create")
    @Operation(summary = "创建个人设置")
    @PreAuthorize("@ss.hasPermission('system:A-user:create')")
    public CommonResult<String> createAUser(@Valid @RequestBody AUserSaveReqVO createReqVO) {
        return success(aUserService.createAUser(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新个人设置")
    @PreAuthorize("@ss.hasPermission('system:A-user:update')")
    public CommonResult<Boolean> updateAUser(@Valid @RequestBody AUserSaveReqVO updateReqVO) {
        aUserService.updateAUser(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除个人设置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:A-user:delete')")
    public CommonResult<Boolean> deleteAUser(@RequestParam("id") String id) {
        aUserService.deleteAUser(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得个人设置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:A-user:query')")
    public CommonResult<AUserRespVO> getAUser(@RequestParam("id") String id) {
        AUserDO aUser = aUserService.getAUser(id);
        return success(BeanUtils.toBean(aUser, AUserRespVO.class));
    }


    @GetMapping("/page")
    @Operation(summary = "获得个人设置分页")
    @PreAuthorize("@ss.hasPermission('system:A-user:query')")
    public CommonResult<PageResult<AUserRespVO>> getAUserPage(@Valid AUserPageReqVO pageReqVO) {
        if (StrUtil.isNotEmpty(pageReqVO.getDeptID())) {
            List<String> groupIds = aGroupService.getAllGroupIdsByDeptId(pageReqVO.getDeptID());
            pageReqVO.setDeptIds(groupIds);
        } else {
            return success(new PageResult<>(new ArrayList<AUserRespVO>(), 0L));
        }
        PageResult<AUserDoChild> pageResult = aUserService.getCustomAUserPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AUserRespVO.class));
    }

    @GetMapping("/page2")
    @Operation(summary = "单表分页")
    public CommonResult<PageResult<AUserRespVO>> getAUserPage2(@Valid AUserPageReqVO pageReqVO) {
        PageResult<AUserDO> pageResult = aUserService.getAUserPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AUserRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出个人设置 Excel")
    @PreAuthorize("@ss.hasPermission('system:A-user:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAUserExcel(@Valid AUserPageReqVO pageReqVO,
                                 HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AUserDO> list = aUserService.getAUserPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "个人设置.xls", "数据", AUserRespVO.class,
                BeanUtils.toBean(list, AUserRespVO.class));
    }

    @PostMapping("/unlock")
    @Operation(summary = "删除个人设置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:A-user:delete')")
    public CommonResult<Boolean> unlock(@RequestParam("id") String id) {
        aUserService.updateLoginFailCount(id, 0);
        return success(true);
    }

    @GetMapping("/getAUserPage")
    @Operation(summary = "获得个人设置分页")
    @PreAuthorize("@ss.hasPermission('system:A-user:query')")
    public CommonResult<PageResult<AUserRespVO>> getUserPage(@Valid AUserPageReqVO pageReqVO) {

        PageResult<AUserDO> aUserPage = aUserService.getAUserPage(pageReqVO);
        return success(BeanUtils.toBean(aUserPage, AUserRespVO.class));
    }

    @GetMapping("/getUserAll")
    @Operation(summary = "获得所有用户精简数据")
    public CommonResult<Object> getUserAll(String name) {

        return success(aUserService.getUserAll(name));
    }

}
