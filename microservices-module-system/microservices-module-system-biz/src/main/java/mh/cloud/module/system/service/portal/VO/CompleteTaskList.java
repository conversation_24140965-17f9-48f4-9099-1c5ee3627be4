package mh.cloud.module.system.service.portal.VO;

import lombok.Data;

import java.util.Date;
@Data
public class CompleteTaskList {
    public String TaskID ;
    /// <summary>
    /// 任务名称
    /// </summary>
    public String TaskName ;
    /// <summary>
    /// 流程ID
    /// </summary>
    public String FlowID ;
    /// <summary>
    /// 流程名称
    /// </summary>
    public String FlowName ;
    /// <summary>
    /// 流程发起人ID
    /// </summary>
    public String CreateUserID ;
    /// <summary>
    /// 流程发起人
    /// </summary>
    public String CreateUserName ;
    /// <summary>
    /// 流程发起人
    /// </summary>
    public String CreateUserDeptName ;
    /// <summary>
    /// 发送时间
    /// </summary>
    public Date CreateTime ;
    /// <summary>
    /// 完成时间
    /// </summary>
    public String ExecTime ;
    /// <summary>
    /// 执行人ID
    /// </summary>
    public String ExecUserID ;
    /// <summary>
    /// 执行人
    /// </summary>
    public String ExecUserName ;
    /// <summary>
    /// 发送人ID
    /// </summary>
    public String SenderID ;
    /// <summary>
    /// 发送人
    /// </summary>
    public String SenderName ;
    /// <summary>
    /// 平台标识
    /// </summary>
    public String SystemName ;
    /// <summary>
    /// 任务查看地址
    /// </summary>
    public String ViewUrl ;
    /// <summary>
    /// 任务ID
    /// </summary>
    public String OriginID ;
    /// <summary>
    /// 记录ID
    /// </summary>
    public String FormInstanceID ;
    /// <summary>
    /// 是否关注
    /// </summary>
    public String IsFocus ;
    public String CreateDeptName ;
}
