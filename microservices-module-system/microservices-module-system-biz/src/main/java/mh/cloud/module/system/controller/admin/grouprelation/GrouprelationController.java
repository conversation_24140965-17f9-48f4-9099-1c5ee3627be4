package mh.cloud.module.system.controller.admin.grouprelation;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import mh.cloud.framework.common.pojo.PageParam;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.framework.common.util.object.BeanUtils;
import static mh.cloud.framework.common.pojo.CommonResult.success;

import mh.cloud.framework.excel.core.util.ExcelUtils;

import mh.cloud.framework.apilog.core.annotation.ApiAccessLog;
import static mh.cloud.framework.apilog.core.enums.OperateTypeEnum.*;

import mh.cloud.module.system.controller.admin.grouprelation.vo.*;
import mh.cloud.module.system.dal.dataobject.grouprelation.GrouprelationDO;
import mh.cloud.module.system.service.grouprelation.GrouprelationService;

@Tag(name = "管理后台 - 分组内部关联")
@RestController
@RequestMapping("/system/grouprelation")
@Validated
public class GrouprelationController {

    @Resource
    private GrouprelationService grouprelationService;

    @PostMapping("/create")
    @Operation(summary = "创建分组内部关联")
    @PreAuthorize("@ss.hasPermission('system:grouprelation:create')")
    public CommonResult<String> createGrouprelation(@Valid @RequestBody GrouprelationSaveReqVO createReqVO) {
        return success(grouprelationService.createGrouprelation(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新分组内部关联")
    @PreAuthorize("@ss.hasPermission('system:grouprelation:update')")
    public CommonResult<Boolean> updateGrouprelation(@Valid @RequestBody GrouprelationSaveReqVO updateReqVO) {
        grouprelationService.updateGrouprelation(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除分组内部关联")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:grouprelation:delete')")
    public CommonResult<Boolean> deleteGrouprelation(@RequestParam("id") String id) {
        grouprelationService.deleteGrouprelation(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得分组内部关联")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:grouprelation:query')")
    public CommonResult<GrouprelationRespVO> getGrouprelation(@RequestParam("id") String id) {
        GrouprelationDO grouprelation = grouprelationService.getGrouprelation(id);
        return success(BeanUtils.toBean(grouprelation, GrouprelationRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得分组内部关联分页")
    @PreAuthorize("@ss.hasPermission('system:grouprelation:query')")
    public CommonResult<PageResult<GrouprelationRespVO>> getGrouprelationPage(@Valid GrouprelationPageReqVO pageReqVO) {
        PageResult<GrouprelationDO> pageResult = grouprelationService.getGrouprelationPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, GrouprelationRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出分组内部关联 Excel")
    @PreAuthorize("@ss.hasPermission('system:grouprelation:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportGrouprelationExcel(@Valid GrouprelationPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<GrouprelationDO> list = grouprelationService.getGrouprelationPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "分组内部关联.xls", "数据", GrouprelationRespVO.class,
                        BeanUtils.toBean(list, GrouprelationRespVO.class));
    }

}
