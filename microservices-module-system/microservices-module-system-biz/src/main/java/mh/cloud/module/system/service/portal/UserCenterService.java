package mh.cloud.module.system.service.portal;

import mh.cloud.module.system.controller.admin.portal.V0.HeadAndSign;
import mh.cloud.module.system.dal.dataobject.auser.AUserDO;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface UserCenterService {
    /**
     * 查询用户中心，左侧列表
     */
    List<Map<String, Object>> getLeftTopMenu(String templateCode);

    AUserDO getUserInfo();

    /**
     * 用户中心修改用户
     */
    Boolean updateUserInfo(HeadAndSign headAndSign);

    /**
     * 存储用户图片文件数据
     * @param name 文件信息
     * @param type 头像或sign
     */
    String updateHeadAndSign(Map<String,Object> params) throws IOException;
}
