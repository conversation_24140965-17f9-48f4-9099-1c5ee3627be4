package mh.cloud.module.system.controller.admin.business;


import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.module.system.dal.dataobject.job.SysJob;
import mh.cloud.module.system.dal.dataobject.job.SysJobVo;
import mh.cloud.module.system.job.core.utils.CronUtils;
import mh.cloud.module.system.service.job.IJobService;
import org.quartz.SchedulerException;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * 调度任务信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/quartz/job")
@Tag(name = "后台管理-任务调度接口")
public class JobController {

    @Resource
    private IJobService jobService;


    /**
     * 新增定时任务
     */
    @GetMapping("/getList")
    @Operation(summary = "获取任务列表")
    public CommonResult<Page<SysJob>> getList(@RequestParam(defaultValue = "0") Integer pageNo,
                                              @RequestParam(defaultValue = "10") Integer pageSize,
                                              String jobName,
                                              String jobHandlerName,
                                              String status
    ) {
        Page<SysJob> page = new Page<>(pageNo, pageSize);
        jobService.page(page, Wrappers.<SysJob>lambdaQuery()
                .eq(ObjUtil.isNotEmpty(status), SysJob::getStatus, status)
                .eq(ObjUtil.isNotEmpty(jobHandlerName), SysJob::getJobHandlerName, jobHandlerName)
                .like(ObjUtil.isNotEmpty(jobName), SysJob::getJobName, jobName));
        return CommonResult.success(page);
    }

    @GetMapping("/getJob")
    @Operation(summary = "获取任务列表")
    public CommonResult<SysJobVo> getJob(String jobId) {
        return CommonResult.success(jobService.getJob(jobId));
    }

    @GetMapping("/getNextFireTime")
    @Operation(summary = "获取下次执行时间")
    public CommonResult<Date> getNextFireTime(String jobId, String jobHandlerName) {
        return CommonResult.success(jobService.getNextFireTime(jobId, jobHandlerName));
    }


    /**
     * 新增定时任务
     */
    @PostMapping("/add")
    @Operation(summary = "添加调度任务")
    public CommonResult<Boolean> addJob(@RequestBody SysJobVo job) {
        if (!CronUtils.isValid(job.getCronExpression())) {
            return CommonResult.error(400, "新增任务'" + job.getJobName() + "'失败，Cron表达式不正确");
        }
        return CommonResult.success(jobService.insertJob(job));
    }

    @PostMapping("/update")
    @Operation(summary = "修改调度任务")
    public CommonResult<Boolean> updateJob(@RequestBody SysJobVo job) throws SchedulerException {
        if (!CronUtils.isValid(job.getCronExpression())) {
            return CommonResult.error(400, "更新任务'" + job.getJobName() + "'失败，Cron表达式不正确");
        }
        return CommonResult.success(jobService.updateJob(job));
    }


    /**
     * 恢复定时任务
     */
    @PostMapping("/resume")
    @Operation(summary = "恢复定时任务")
    public CommonResult<Boolean> resumeJob(String jobId) {

        return CommonResult.success(jobService.resumeJob(jobId));
    }

    /**
     * 恢复定时任务
     */
    @PostMapping("/trigger")
    @Operation(summary = "立即执行一次")
    public CommonResult<Boolean> trigger(String jobId) {

        return CommonResult.success(jobService.trigger(jobId));
    }


    /**
     * 停止定时任务
     */
    @PostMapping("/pause")
    @Operation(summary = "停止定时任务")
    public CommonResult<Boolean> pauseJob(String jobId) {

        return CommonResult.success(jobService.pauseJob(jobId));
    }

    /**
     * 删除定时任务
     */
    @PostMapping("/deleteJob")
    @Operation(summary = "删除定时任务")
    public CommonResult<Boolean> deleteJob(String jobId) {
        return CommonResult.success(jobService.deleteJob(jobId));
    }


}
