package mh.cloud.module.system.service.pagenewscentermenu;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import mh.cloud.module.system.controller.admin.pagenewscentermenu.vo.*;
import mh.cloud.module.system.dal.dataobject.pagenewscentermenu.PageNewscentermenuDO;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.pojo.PageParam;
import mh.cloud.framework.common.util.object.BeanUtils;

import mh.cloud.module.system.dal.mysql.pagenewscentermenu.PageNewscentermenuMapper;

import static mh.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static mh.cloud.module.system.enums.ErrorCodeConstants.*;

/**
 * 菜单管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@DS("OA")
public class PageNewscentermenuServiceImpl implements PageNewscentermenuService {

    @Resource
    private PageNewscentermenuMapper pageNewscentermenuMapper;

    @Override
    public String createPageNewscentermenu(PageNewscentermenuSaveReqVO createReqVO) {
        // 插入
        PageNewscentermenuDO pageNewscentermenu = BeanUtils.toBean(createReqVO, PageNewscentermenuDO.class);
        pageNewscentermenuMapper.insert(pageNewscentermenu);
        // 返回
        return pageNewscentermenu.getId();
    }

    @Override
    public void updatePageNewscentermenu(PageNewscentermenuSaveReqVO updateReqVO) {
        // 校验存在
        validatePageNewscentermenuExists(updateReqVO.getId());
        // 更新
        PageNewscentermenuDO updateObj = BeanUtils.toBean(updateReqVO, PageNewscentermenuDO.class);
        pageNewscentermenuMapper.updateById(updateObj);
    }

    @Override
    public void deletePageNewscentermenu(String id) {
        // 校验存在
        validatePageNewscentermenuExists(id);
        // 删除
        pageNewscentermenuMapper.deleteById(id);
    }

    private void validatePageNewscentermenuExists(String id) {
        if (pageNewscentermenuMapper.selectById(id) == null) {
            throw exception(PAGE_NEWSCENTERMENU_NOT_EXISTS);
        }
    }

    @Override
    public PageNewscentermenuDO getPageNewscentermenu(String id) {
        return pageNewscentermenuMapper.selectById(id);
    }

    @Override
    public PageResult<PageNewscentermenuDO> getPageNewscentermenuPage(PageNewscentermenuPageReqVO pageReqVO) {
        return pageNewscentermenuMapper.selectPage(pageReqVO);
    }

}