package mh.cloud.module.system.controller.admin.portal.V0;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

@TableName("system_layout_config")
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysLayoutConfigVo {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    @TableField(value = "user_id")
    private String userId;


    @TableField(value = "layout_config")
    private String layoutConfig;

    @TableField(value = "is_default")
    private Boolean isDefault;

    @TableField(value = "show_group")
    private Boolean showGroup;

    @TableField(value = "show_remove")
    private Boolean showRemove;

    @TableField(value = "draggable")
    private Boolean draggable;

    @TableField(value = "resizable")
    private Boolean resizable;

    @TableField(value = "alignment")
    private Boolean alignment;

    @TableField(value = "tenant_id")
    private String tenantId;
}
