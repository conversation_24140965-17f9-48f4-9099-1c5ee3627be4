package mh.cloud.module.system.controller.admin.portal.service;

import jakarta.annotation.Resource;
import mh.cloud.module.system.controller.admin.logger.vo.searchLog.SearchLogCreateReqVO;
import mh.cloud.module.system.controller.admin.portal.V0.EntryMenu;
import mh.cloud.module.system.controller.admin.portal.V0.OutSystemV0;
import mh.cloud.module.system.dal.dataobject.auser.AUserDO;
import mh.cloud.module.system.service.auser.AUserService;
import mh.cloud.module.system.service.auth.AdminAuthService;
import mh.cloud.module.system.service.db.SQLHelper;
import mh.cloud.module.system.service.logger.SearchLogRecordService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;

import static mh.cloud.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;


@Service
public class ServiceCenterImpl implements ServiceCenterService {
    private static final String DEFAULT_VALUE = "";
    private static final String DEFAULT_SERVICE_DIRECTORY = "服务<br/>指南";
    @Resource
    private AUserService aUserService;

    @Resource
    private SearchLogRecordService searchLogRecordService;

    @Resource
    private AdminAuthService adminAuthService;

    @Override
    public List<EntryMenu> List(String search) {
        List<EntryMenu> list = new ArrayList<>();
        List<EntryMenu> secondLevelMenuList = new ArrayList<>();
        List<EntryMenu> thirdLevelMenuList = new ArrayList<>();

        AUserDO aUser = aUserService.getAUser(getLoginUserId());
        var sql = "call dbo.Pro_PortalMenuUser('" + aUser.getId() + "','" + search + "')";
        SQLHelper _oaHelper = SQLHelper.createSqlHelper("OA");
        try {
            List<Map<String, Object>> dt = _oaHelper.executeReader(sql);
            if (!dt.isEmpty()) {
                for (Map<String, Object> item : dt) {
                    EntryMenu menu = new EntryMenu();
                    menu.setID(item.get("ID") == null ? "" : item.get("ID").toString());

                    // 使用 Optional 来简化 null 值处理
                    menu.setParent(Optional.ofNullable(item.get("ParentID"))
                            .map(Object::toString)
                            .orElse(DEFAULT_VALUE));

                    menu.setIcon(Optional.ofNullable(item.get("Icon"))
                            .map(Object::toString)
                            .orElse("file-text"));

                    menu.setName(Optional.ofNullable(item.get("Name"))
                            .map(Object::toString)
                            .orElse(DEFAULT_VALUE));

                    menu.setUrl(Optional.ofNullable(item.get("Url"))
                            .map(Object::toString)
                            .orElse(DEFAULT_VALUE));

                    menu.setImg(Optional.ofNullable(item.get("Img"))
                            .map(Object::toString)
                            .orElse(DEFAULT_VALUE));

                    menu.setServiceDirectory(Optional.ofNullable(item.get("ServiceDirectory"))
                            .map(Object::toString)
                            .orElse(DEFAULT_SERVICE_DIRECTORY));
//
//                    menu.setServiceDirectoryUrl(Optional.ofNullable(item.get("ServiceDirectoryUrl"))
//                            .map(Object::toString)
//                            .orElse(DEFAULT_VALUE));

                    menu.setLevel(Optional.ofNullable(item.get("Lv"))
                            .map(Object::toString)
                            .orElse(DEFAULT_VALUE));

                    menu.setServiceDirectoryID(Optional.ofNullable(item.get("CDMC"))
                            .map(Object::toString)
                            .orElse(DEFAULT_VALUE));

                    menu.setIsEnable(Optional.ofNullable(item.get("IsEnable"))
                            .map(Object::toString)
                            .orElse(DEFAULT_VALUE));

//                    menu.setExecType(Optional.ofNullable(item.get("ExecType"))
//                            .map(Object::toString)
//                            .orElse(DEFAULT_VALUE));

// 注意: ServiceDirectory 被赋值两次，这里保留最后一次赋值
//                    menu.ServiceDirectory = DEFAULT_SERVICE_DIRECTORY;

                    if (menu.getLevel().equals("1")) {
                        list.add(menu);
                    } else if (menu.getLevel().equals("2")) {
                        secondLevelMenuList.add(menu);
                    } else if (menu.getLevel().equals("3")) {

                        //2021-9-7 zk 单击菜单时，根据菜单“打开方式”处理URL
                        //“工作区打开页面”，需要打开子门户，然后打开tab选项卡显示该功能；
//                    menu.Url = !menu.Url.equals("") ? menu.Url + (menu.Url.contains("?") ? "&" : "?") + "token=" + aUserService.GetToken(user.ID) : menu.Url;
//                    if (menu.ExecType.toLower().Equals("openworkarea"))
//                    {
//                        var subsysurl = "/portal/home/<USER>" + menu.ID + "&menuname=" + menu.Name + "&reurl=" + menu.Url;
//                        menu.Url = subsysurl;
//                    }

                        thirdLevelMenuList.add(menu);
                    }
                }


            };
            Map<String, Object> dic = new HashMap<>();
            dic.put("searchKey", search);
            dic.put("moduleFlag", "IndexMenu");
            searchLogRecordService.createSearchLog(dic);
            return ConstructData(list, secondLevelMenuList, thirdLevelMenuList);
        } finally {
            _oaHelper.close();
        }

    }

    /// <summary>
    /// 构造右侧入口菜单数据
    /// </summary>
    /// <param name="firstList">第一级菜单</param>
    /// <param name="secondList">二级菜单</param>
    /// <param name="thirdList">三级菜单</param>
    /// <returns></returns>
    private List<EntryMenu> ConstructData(List<EntryMenu> firstList, List<EntryMenu> secondList, List<EntryMenu> thirdList) {
        List<EntryMenu> list = new ArrayList<>();
        for (EntryMenu firstMenu : firstList) {
            var menu = new EntryMenu();
            menu = firstMenu;
            //二级菜单
            List<EntryMenu> subList = new ArrayList<>();
            for (EntryMenu secondMenu : secondList) {
                if (Objects.equals(secondMenu.getParent(), firstMenu.getID())) {
                    //三级菜单
                    var subMenu = new EntryMenu();
                    subMenu = secondMenu;
                    for (EntryMenu thirdMenu : thirdList) {
                        if (Objects.equals(thirdMenu.getParent(), secondMenu.getID())) {
                            if (subMenu.getSubMenu() == null) {
                                subMenu.setSubMenu(new ArrayList<>());
                            }
                            subMenu.getSubMenu().add(thirdMenu);
                        }

                    }
                    subList.add(subMenu);
                }

            }
            menu.setSubMenu(subList);
//            menu.setSubMenu(subList);
            list.add(menu);
        }
        return list;
    }

    @Override
    public List<Map<String, Object>> getLeftListCat() {
        String sql = "SELECT Code,Name FROM C_EnumItem WHERE EnumDefID='ad8700b2-5d2c-4adb-9cc7-bb9a73aa9f1b' ORDER BY SortIndex asc";
        SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core");
        List<Map<String, Object>> dt = sqlHelper.executeReader(sql);
        return dt;
    }

    @Override
    public OutSystemV0 getOutSystemUrl(OutSystemV0 outSystem) {
        AUserDO aUser = aUserService.getAUser(getLoginUserId());
        //查询token
        String token = adminAuthService.getOutSystemToken(aUser.getId());
        String url = outSystem.getUrl();
        if (url.contains("systoken")){
            url = removeParamFromUrl(url, "systoken");
        }
        if (url.endsWith("&")) {
            url = url.substring(0, url.length() - 1);
        }
        url += "&systoken=" + token;
        outSystem.setUrl(url);
        return outSystem;
    }

    public String removeParamFromUrl(String url, String removeParam) {
        try {
            URI uri = new URI(url);
            Map<String, String> queryParams = new HashMap<>();
            String query = uri.getQuery();
            if (query != null) {
                for (String param : query.split("&")) {
                    String[] parts = param.split("=");
                    if (parts.length == 2) {
                        queryParams.put(parts[0], parts[1]);
                    }
                }
                queryParams.remove(removeParam);
                StringBuilder newQuery = new StringBuilder();
                for (Map.Entry<String, String> entry : queryParams.entrySet()) {
                    if (!newQuery.isEmpty()) {
                        newQuery.append("&");
                    }
                    newQuery.append(entry.getKey()).append("=").append(entry.getValue());
                }

                return (StringUtils.isEmpty(uri.getScheme()) ? "" : uri.getScheme() + "://" + uri.getHost() + ":" + uri.getPort()) + uri.getPath() + (!newQuery.isEmpty() ? "?" + newQuery : "");
            }
            return url;
        } catch (URISyntaxException e) {
            return url;
        }
    }

}
