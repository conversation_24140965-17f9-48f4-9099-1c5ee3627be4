package mh.cloud.module.system.service.portal;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import mh.cloud.module.system.controller.admin.portal.V0.*;

import java.util.List;
import java.util.Map;

public interface LayoutService extends IService<SysLayoutVo> {
    /**
     * 获取布局数据列表
     *
     * @return 列表
     */
    Map<String, Object> getLayout(String category);

    /**
     * 保存修改数据
     */
    Object saveOrUpdateLayout(LayoutSaveReq layoutSaveReq);


    /**
     * 保存配置数据
     */
    Boolean saveLayoutConfig(SysLayoutConfigVo configVo);


    /**
     * 删除布局数据
     */
    Boolean deleteLayout(List<String> ids);

    /**
     * 启用删除布局
     */
    Boolean startDelLayout(String id);

    /**
     * 获取布局配置
     */
    Object getLayoutConfig();
}
