package mh.cloud.module.system.controller.admin.portal.constant;

/**
 * 一些常用的公共sql
 */
public class SQLConstant {
    /**
     * 根据类型获取角色组织信息
     */
    public static final String GetGroupByType = "select ID, Name, ShortName, Code, ParentID, FullID, GroupType, Type, SortIndex, IsDeleted, DeleteTime, OrgLevel, \n" +
            "                Description, Location, SystemCode, OutKey, ConnName, UserSQL, FullName, OrgRole, CategoryID, CategoryName, \n" +
            "                UseCategory, PropCategory from A_Group where Type=? and IsDeleted='0'";

    /**
     * 获取用户角色组织信息
     */
    public static final String GetUserRelation = "select DeptFullID,OrgRole from A_GroupRelation left join A_Group on A_GroupRelation.ParentGroupID = A_Group.ID where ChildGroupID= ?";
}
