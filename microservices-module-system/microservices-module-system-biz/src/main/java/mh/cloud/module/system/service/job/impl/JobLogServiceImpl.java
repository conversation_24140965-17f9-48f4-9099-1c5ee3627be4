package mh.cloud.module.system.service.job.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import mh.cloud.module.system.dal.dataobject.job.SysJobLog;
import mh.cloud.module.system.dal.dataobject.job.TaskOptions;
import mh.cloud.module.system.dal.mysql.job.JobLogMapper;
import mh.cloud.module.system.job.core.service.JobLogFrameworkService;
import mh.cloud.module.system.service.job.IJobLogService;
import mh.cloud.module.system.service.job.TaskOptionsService;
import mh.cloud.module.system.api.portal.SystemApi;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.UUID;
@Slf4j
@Service
public class JobLogServiceImpl implements JobLogFrameworkService, IJobLogService {

    @Resource
    private JobLogMapper jobLogMapper;

    @Resource
    private TaskOptionsService taskOptionsService;

    @Resource
    private SystemApi systemApi;

    private static final String insertLogSql =
            "INSERT INTO T_TaskTimerRunLog(ID,Task,TaskName,TaskGroup,TaskContect,LogType,CreateTime,RunTime) " +
                    "VALUES ('{}','{}','{}','{}','{}','{}','{}','{}')";

    private static final String updateLogSql = "UPDATE T_TaskTimerRunLog SET ModifyTime = '{}',TaskContect='{}',RunTime= '{}' WHERE ID = '{}'";

    /**
     * 创建 Job 日志
     *
     * @param jobId     任务编号
     * @param beginTime 开始时间
     * @return Job 日志的编号
     */
    @Override
    public String createJobLog(String jobId, LocalDateTime beginTime, TaskOptions options) {
        log.info("创建任务日志开始，任务ID: {}, 任务名称: {}", jobId, options.getTaskName());

        String id = UUID.randomUUID().toString();
        // RunTime字段存储任务开始执行的时间点（datetime格式）
        String beginTimeStr = DateUtil.format(beginTime, "yyyy-MM-dd HH:mm:ss");

        String format = StrUtil.format(insertLogSql,
                id,
                jobId,
                options.getTaskName(),
                options.getTaskGroup(),
                null,
                "任务执行",
                beginTimeStr, // CreateTime
                beginTimeStr); // RunTime存储开始时间（datetime格式）
        log.info("执行插入SQL: {}, 开始时间: {}", format, beginTimeStr);

        Object object = systemApi.executeSql("Log", format);
        log.info("插入结果: {}, 日志ID: {}", object, id);

        //修改任务最后运行时间
        taskOptionsService.lambdaUpdate()
                .set(TaskOptions::getLastRunTime, beginTime)
                .eq(TaskOptions::getID, jobId).update();

        return id;
    }

    /**
     * 更新 Job 日志的执行结果
     *
     * @param logId    日志编号
     * @param endTime  结束时间。因为是异步，避免记录时间不准去
     * @param duration 运行时长，单位：毫秒
     * @param success  是否成功
     * @param result   成功数据
     * @param jobId
     */
    @Override
    public void updateJobLogResultAsync(String logId, LocalDateTime endTime, Integer duration, boolean success, String result, String jobId) {
        log.info("更新任务日志开始，日志ID: {}, 任务ID: {}, 执行结果: {}", logId, jobId, success ? "成功" : "失败");

        if (ObjUtil.isNotEmpty(logId)) {
            // RunTime字段存储的是任务开始执行的时间点（datetime格式），不是执行耗时
            // 计算开始时间：endTime - duration（毫秒）
            LocalDateTime startTime = endTime.minusNanos(duration * 1000000L); // duration是毫秒，转换为纳秒
            String startTimeStr = DateUtil.format(startTime, "yyyy-MM-dd HH:mm:ss");

            String format = StrUtil.format(updateLogSql,
                    DateUtil.format(endTime, "yyyy-MM-dd HH:mm:ss"),
                    result,
                    startTimeStr, // RunTime存储开始时间（datetime格式）
                    logId);
            log.info("执行更新SQL: {}, 开始时间: {}", format, startTimeStr);

            Object updateResult = systemApi.executeSql("Log", format);
            log.info("更新结果: {}, 日志ID: {}", updateResult, logId);
        } else {
            log.warn("日志ID为空，跳过更新任务日志，任务ID: {}", jobId);
        }

        taskOptionsService.lambdaUpdate()
                .set(TaskOptions::getLastRunEndTime, endTime)
                .eq(TaskOptions::getID, jobId).update();
    }

    @Override
    public Map<String, Object> getJobLogList(String jobId, String beginTime, String endTime, String status, Page<SysJobLog> page) {
        log.info("查询任务日志列表，jobId: {}, beginTime: {}, endTime: {}, status: {}, 当前页: {}, 每页大小: {}",
                jobId, beginTime, endTime, status, page.getCurrent(), page.getSize());

        try {
            // 构建查询SQL，从Log库的T_TaskTimerRunLog表查询
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("SELECT ID, Task, TaskName, TaskGroup, TaskContect, LogType, CreateTime, ModifyTime, RunTime ");
            sqlBuilder.append("FROM T_TaskTimerRunLog WHERE 1=1 ");

            // 添加查询条件
            if (ObjUtil.isNotEmpty(jobId)) {
                sqlBuilder.append("AND Task = '").append(jobId).append("' ");
            }
            if (ObjUtil.isNotEmpty(beginTime)) {
                sqlBuilder.append("AND CreateTime >= '").append(beginTime).append("' ");
            }
            if (ObjUtil.isNotEmpty(endTime)) {
                sqlBuilder.append("AND CreateTime <= '").append(endTime).append("' ");
            }
            if (ObjUtil.isNotEmpty(status)) {
                sqlBuilder.append("AND LogType = '").append(status).append("' ");
            }

            // 添加排序
            sqlBuilder.append("ORDER BY CreateTime DESC ");

            // 添加分页 - 使用更通用的LIMIT语法
            long offset = (page.getCurrent() - 1) * page.getSize();
            log.info("分页计算：当前页={}, 每页大小={}, 偏移量={}", page.getCurrent(), page.getSize(), offset);
            sqlBuilder.append("LIMIT ").append(page.getSize()).append(" OFFSET ").append(offset);

            String querySql = sqlBuilder.toString();
            log.info("执行查询SQL: {}", querySql);

            // 执行查询
            List<Map<String, Object>> logList = systemApi.selectSql("Log", querySql);
            log.info("查询到 {} 条日志记录", logList.size());

            // 查询总数
            String countSql = "SELECT COUNT(*) as total FROM T_TaskTimerRunLog WHERE 1=1 ";
            if (ObjUtil.isNotEmpty(jobId)) {
                countSql += "AND Task = '" + jobId + "' ";
            }
            if (ObjUtil.isNotEmpty(beginTime)) {
                countSql += "AND CreateTime >= '" + beginTime + "' ";
            }
            if (ObjUtil.isNotEmpty(endTime)) {
                countSql += "AND CreateTime <= '" + endTime + "' ";
            }
            if (ObjUtil.isNotEmpty(status)) {
                countSql += "AND LogType = '" + status + "' ";
            }

            List<Map<String, Object>> countResult = systemApi.selectSql("Log", countSql);
            long total = 0;
            if (!countResult.isEmpty()) {
                Object totalObj = countResult.get(0).get("total");
                total = totalObj != null ? Long.parseLong(totalObj.toString()) : 0;
            }

            log.info("查询到总记录数: {}", total);

            // 转换数据格式，保持与前端期望的字段名一致
            List<Map<String, Object>> mapList = new ArrayList<>();
            for (int i = 0; i < logList.size(); i++) {
                Map<String, Object> logRecord = logList.get(i);
                String logId = (String) logRecord.get("ID");

                // 计算执行时长：endTime - beginTime

                Map<String, Object> map = new HashMap<>();
                map.put("row_number", i + 1);
                map.put("StepView", "查看运行步骤日志");
                map.put("ID", logRecord.get("ID"));
                map.put("CreateTime", logRecord.get("CreateTime"));
                map.put("BeginTime", logRecord.get("CreateTime"));
                map.put("EndTime", logRecord.get("ModifyTime"));
                map.put("Status", logRecord.get("LogType"));
                map.put("Duration", 0); // 使用时间差计算的执行时长
                map.put("Task", logRecord.get("Task"));
                map.put("TaskName", logRecord.get("TaskName"));
                map.put("TaskGroup", logRecord.get("TaskGroup"));
                map.put("TaskContect", logRecord.get("TaskContect"));
                map.put("RunTime", logRecord.get("RunTime")); // 开始运行时间
                map.put("Result", logRecord.get("TaskContect"));
                map.put("LogType", logRecord.get("LogType"));
                mapList.add(map);
            }

            Map<String, Object> pageMap = new HashMap<>();
            pageMap.put("total", total);
            pageMap.put("data", mapList);
            Map<String, Object> result = new HashMap<>();
            result.put("data", pageMap);

            log.info("返回日志列表，总数: {}, 当前页数据: {}", total, mapList.size());
            return result;

        } catch (Exception e) {
            log.error("查询任务日志列表失败，jobId: {}", jobId, e);
            // 如果Log库查询失败，返回空结果
            Map<String, Object> pageMap = new HashMap<>();
            pageMap.put("total", 0);
            pageMap.put("data", new ArrayList<>());
            Map<String, Object> result = new HashMap<>();
            result.put("data", pageMap);
            return result;
        }
    }

    @Override
    public List<Map<String, Object>> getResult(String taskId) {
        SysJobLog jobLog = jobLogMapper.selectById(taskId);
        String result = jobLog.getResult();

        return null;
    }

    @Override
    public Boolean delJobLogs(String jobId, String startTime, String endTime) {

        return jobLogMapper.delete(Wrappers.<SysJobLog>lambdaQuery()
                .eq(SysJobLog::getTaskId, jobId)
                .between(SysJobLog::getCreateTime, startTime, endTime)) != -1;
    }
}
