package mh.cloud.module.system.controller.admin.ares.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 资源管理新增/修改 Request VO")
@Data
public class AResSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21013")
    private String id;

    @Schema(description = "父ID", example = "6956")
    private String parentID;

    @Schema(description = "全ID", example = "12267")
    private String fullID;

    @Schema(description = "排序索引")
    private Integer sortIndex;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "luohang",defaultValue = "test")
    @NotEmpty(message = "名称不能为空")
    private String name;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "类型", example = "1")
    private String type;

    @Schema(description = "图标")
    private String iconCls;

    @Schema(description = "Url", example = "https://www.iocoder.cn")
    private String url;

    @Schema(description = "控制类型", example = "1")
    private String ctrlType;

    @Schema(description = "权限，可以为页面控件ID，数据的查询条件")
    private String auth;

    @Schema(description = "描述", example = "你猜")
    private String description;

    @Schema(description = "系统编码")
    private String systemCode;

    @Schema(description = "创建人")
    private String createUser;

    @Schema(description = "创建人id", example = "8813")
    private String createUserID;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改人")
    private String modifyUser;

    @Schema(description = "修改人id", example = "1259")
    private String modifyUserID;

    @Schema(description = "修改时间")
    private LocalDateTime modifyTime;

    @Schema(description = "发布状态", example = "1")
    private String publishStatus;

    @Schema(description = "上次发布状态", example = "1")
    private String prePublishStatus;

    @Schema(description = "逻辑删除")
    private String isDeleted;

    @Schema(description = "入口的执行类型（弹出页面，再右侧添加子tab，执行一个js函数）", example = "2")
    private String execType;

    @Schema(description = "js脚本内容（预留）")
    private String scriptContent;

    @Schema(description = "关联配置项的ID", example = "19478")
    private String releateConfigID;

    @Schema(description = "第二次打开时是否刷新页面")
    private String isRefreshPage;

    @Schema(description = "首页的图标样式")
    private String bootstrapCls;

    @Schema(description = "需求部门")
    private String requireDept;

    @Schema(description = "使用范围")
    private String useScope;

    @Schema(description = "关键词")
    private String keyWord;

    @Schema(description = "特殊用户")
    private String specialUser;

    @Schema(description = "是否是子系统")
    private String isSubsystem;

    @Schema(description = "业务目录")
    private String businessCategory;

    @Schema(description = "文件")
    private String files;

    @Schema(description = "Isenterprise")
    private String isenterprise;

    @Schema(description = "Abbreviation")
    private String abbreviation;

    @Schema(description = "Servicereamrk")
    private String servicereamrk;

    @Schema(description = "关联制度")
    private String institutions;

    @Schema(description = "关联业务")
    private String business;

    @Schema(description = "服务指南")
    private String serviceDirectory;

    @Schema(description = "输入数据")
    private String inRes;

    @Schema(description = "输入数据", example = "luohang")
    private String inResName;

    @Schema(description = "输出数据")
    private String outRes;

    @Schema(description = "输出数据", example = "李四")
    private String outResName;

    @Schema(description = "门户显示")
    private String isProtal;

    @Schema(description = "相关文件")
    private String otherFile;

    @Schema(description = "菜单图片")
    private String menuImage;

    @Schema(description = "是否是标题")
    private String isTitle;

    @Schema(description = "sqrole")
    private String sqrole;

    @Schema(description = "sqorg")
    private String sqorg;

    @Schema(description = "squser")
    private String squser;

    @Schema(description = "sqroleId", example = "25636")
    private String sqroleId;

    @Schema(description = "sqorgId", example = "16648")
    private String sqorgId;

    @Schema(description = "squserId", example = "18493")
    private String squserId;

    @Schema(description = "KeyWordId", example = "2501")
    private String keyWordId;

    @Schema(description = "glywId", example = "23839")
    private String glywId;

    @Schema(description = "glyw")
    private String glyw;

    @Schema(description = "glzdId", example = "26201")
    private String glzdId;

    @Schema(description = "glzd")
    private String glzd;

    @Schema(description = "SqRoleCount", example = "25621")
    private Integer sqRoleCount;

    @Schema(description = "SqUserCount", example = "24557")
    private Integer sqUserCount;

    @Schema(description = "SxYewu")
    private String sxYewu;

    @Schema(description = "SxYewuName", example = "luohang")
    private String sxYewuName;

    @Schema(description = "XxYewu")
    private String xxYewu;

    @Schema(description = "XxYewuName", example = "张三")
    private String xxYewuName;

    @Schema(description = "dgRelatedFiles")
    private String dgRelatedFiles;

    @Schema(description = "AuthUserID", example = "28285")
    private String authUserID;

    @Schema(description = "AuthUserName", example = "赵六")
    private String authUserName;

    @Schema(description = "AuthRoleID", example = "3291")
    private String authRoleID;

    @Schema(description = "AuthRoleName", example = "王五")
    private String authRoleName;

    @Schema(description = "DataAuth")
    private String dataAuth;

    @Schema(description = "CtrAuth")
    private String ctrAuth;

    @Schema(description = "一致显示", requiredMode = Schema.RequiredMode.REQUIRED,defaultValue = "1")
//    @NotNull(message = "一致显示不能为空")
    private Boolean alwaysShow;

    @Schema(description = "权限")
    private String permission;

    @Schema(description = "路径")
    private String path;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "组件")
    private String component;

    @Schema(description = "组件名称", example = "张三")
    private String componentName;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1",defaultValue = "1")
//    @NotNull(message = "状态不能为空")
    private Short status;

    @Schema(description = "是否可见", requiredMode = Schema.RequiredMode.REQUIRED,defaultValue = "1")
//    @NotNull(message = "是否可见不能为空")
    private Boolean visible;

    @Schema(description = "存活时间", requiredMode = Schema.RequiredMode.REQUIRED,defaultValue = "1000")
//    @NotNull(message = "存活时间不能为空")
    private Boolean keepAlive;

}