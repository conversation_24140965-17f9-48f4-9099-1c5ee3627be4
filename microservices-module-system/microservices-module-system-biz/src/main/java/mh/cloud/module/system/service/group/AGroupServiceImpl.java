package mh.cloud.module.system.service.group;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import mh.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import mh.cloud.module.system.dal.dataobject.ares.AResDO;
import mh.cloud.module.system.dal.dataobject.ares.TreeNode;
import mh.cloud.module.system.dal.dataobject.grouprelation.GrouprelationDO;
import mh.cloud.module.system.dal.dataobject.groupres.GroupresDO;
import mh.cloud.module.system.dal.mysql.ares.AResMapper;
import mh.cloud.module.system.dal.mysql.grouprelation.GrouprelationMapper;
import mh.cloud.module.system.dal.mysql.groupres.GroupresMapper;
import mh.cloud.module.system.service.grouprelation.GrouprelationService;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import mh.cloud.module.system.controller.admin.group.vo.*;
import mh.cloud.module.system.dal.dataobject.group.AGroupDO;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.pojo.PageParam;
import mh.cloud.framework.common.util.object.BeanUtils;

import mh.cloud.module.system.dal.mysql.group.AGroupMapper;

import static mh.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static mh.cloud.module.system.enums.ErrorCodeConstants.*;

/**
 * 系统角色管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AGroupServiceImpl implements AGroupService {

    @Resource
    private AGroupMapper aGroupMapper;
    @Resource
    private GroupresMapper groupresMapper;
    @Resource
    private AResMapper resMapper;

    @Override
    public String createAGroup(AGroupSaveReqVO createReqVO) {
        // 校验父节点ID的有效性
        validateParentAGroup(null, createReqVO.getParentID());
        // 校验名称的唯一性
        validateAGroupNameUnique(null, createReqVO.getParentID(), createReqVO.getName());

        // 插入
        AGroupDO aGroup = BeanUtils.toBean(createReqVO, AGroupDO.class);
        aGroup.setIsDeleted("0");
        aGroupMapper.insert(aGroup);
        AGroupDO aGroupDO = aGroupMapper.selectById(aGroup.getParentID());
        String fullId = null;
        if (aGroupDO != null) {
            fullId = aGroupDO.getFullID() + "." + aGroup.getId();
        } else {
            fullId = aGroup.getId();
        }
        aGroupMapper.update(new LambdaUpdateWrapper<AGroupDO>()
                .eq(AGroupDO::getId, aGroup.getId())
                .set(AGroupDO::getFullID, fullId));
        // 返回
        return aGroup.getId();
    }

    @Override
    public void updateAGroup(AGroupSaveReqVO updateReqVO) {
        // 校验存在
        validateAGroupExists(updateReqVO.getId());
        // 校验父节点ID的有效性
//        validateParentAGroup(updateReqVO.getId(), updateReqVO.getParentID());
        // 校验名称的唯一性
        validateAGroupNameUnique(updateReqVO.getId(), updateReqVO.getParentID(), updateReqVO.getName());

        // 更新
        AGroupDO updateObj = BeanUtils.toBean(updateReqVO, AGroupDO.class);
        aGroupMapper.updateById(updateObj);
    }

    @Override
    public void deleteAGroup(String id) {
        // 校验存在
        validateAGroupExists(id);
        // 校验是否有子系统角色管理
        if (aGroupMapper.selectCountByParentID(id) > 0) {
            throw exception(A_GROUP_EXITS_CHILDREN);
        }
        // 删除
        aGroupMapper.deleteById(id);
    }

    private void validateAGroupExists(String id) {
        if (aGroupMapper.selectById(id) == null) {
            throw exception(A_GROUP_NOT_EXISTS);
        }
    }

    private void validateParentAGroup(String id, String parentID) {
        if (parentID == null || AGroupDO.PARENT_ID_ROOT.equals(parentID)) {
            return;
        }
        // 1. 不能设置自己为父系统角色管理
        if (Objects.equals(id, parentID)) {
            throw exception(A_GROUP_PARENT_ERROR);
        }
        // 2. 父系统角色管理不存在
        AGroupDO parentAGroup = aGroupMapper.selectById(parentID);
//        if (parentAGroup == null) {
//            throw exception(A_GROUP_PARENT_NOT_EXITS);
//        }
        // 3. 递归校验父系统角色管理，如果父系统角色管理是自己的子系统角色管理，则报错，避免形成环路
        if (id == null) { // id 为空，说明新增，不需要考虑环路
            return;
        }
        for (int i = 0; i < Short.MAX_VALUE; i++) {
            // 3.1 校验环路
            parentID = parentAGroup.getParentID();
            if (Objects.equals(id, parentID)) {
                throw exception(A_GROUP_PARENT_IS_CHILD);
            }
            // 3.2 继续递归下一级父系统角色管理
            if (parentID == null || AGroupDO.PARENT_ID_ROOT.equals(parentID)) {
                break;
            }
            parentAGroup = aGroupMapper.selectById(parentID);
            if (parentAGroup == null) {
                break;
            }
        }
    }

    private void validateAGroupNameUnique(String id, String parentID, String name) {
        AGroupDO aGroup = aGroupMapper.selectByParentIDAndName(parentID, name);
        if (aGroup == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的系统角色管理
        if (id == null) {
            throw exception(A_GROUP_NAME_DUPLICATE);
        }
        if (!Objects.equals(aGroup.getId(), id)) {
            throw exception(A_GROUP_NAME_DUPLICATE);
        }
    }

    @Override
    public AGroupDO getAGroup(String id) {
        return aGroupMapper.selectById(id);
    }

    @Override
    public List<AGroupDO> getAGroupList(AGroupListReqVO listReqVO) {
        return aGroupMapper.selectList(listReqVO);
    }

    @Override
    public List<String> getChildrenDepartIds(String deptID) {
        List<String> ids = new ArrayList<>();
        ids.add(deptID);
        addChildIds(deptID, ids);
        return ids;
    }

    @Override
    public PageResult<AGroupDO> getAGroupPage(AGroupPageReqVO pageReqVO) {
        return aGroupMapper.selectPage(pageReqVO);
    }

    private void addChildIds(String deptID, List<String> ids) {
        List<AGroupDO> aGroupDOS = aGroupMapper.selectList(AGroupDO::getParentID, deptID);
        if(IterUtil.isNotEmpty(aGroupDOS)){
            for(AGroupDO a:aGroupDOS){
                if(StrUtil.isNotEmpty(a.getId())){
                    ids.add(a.getId());
                    addChildIds(a.getId(), ids);
                }
            }
        }
    }

    @Override
    public PageResult<AGroupDO> getAuthRole(AGroupRespVO groupRespVO ) {
        List<GroupresDO> groupresDOS = groupresMapper.selectList(new LambdaQueryWrapperX<GroupresDO>().eq(GroupresDO::getResID, groupRespVO.getId()));
        List<String> collect = groupresDOS.stream().map(GroupresDO::getGroupID).collect(Collectors.toList());

        return aGroupMapper.getAuthRoleOrDept(groupRespVO,collect);
    }

    @Override
    public void deleteGroupRes(String groupId,String resId) {
        //寻找下级关联的资源
        AResDO aRes = resMapper.selectById(resId);
        List<AResDO> aResDOS = resMapper.selectList(new LambdaQueryWrapperX<AResDO>().like(AResDO::getFullID, aRes.getFullID()));
        List<String> resIds = aResDOS.stream().map(AResDO::getId).collect(Collectors.toList());

        groupresMapper.delete(new LambdaQueryWrapperX<GroupresDO>()
                .eq(GroupresDO::getGroupID, groupId)
                .in(GroupresDO::getResID, resIds)
        );
    }
    @Override
    public List<TreeNode> getGroupTree(String id, String groupType) {
        //查询角色
        LambdaQueryWrapperX<AGroupDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eqIfPresent(AGroupDO::getGroupType, groupType)
                .eqIfPresent(AGroupDO::getParentID, id);
        List<AGroupDO> groupList = aGroupMapper.selectList(wrapperX);
        List<TreeNode> list = new ArrayList<>();
        groupList.stream().forEach(item -> {
            TreeNode treeNode = new TreeNode();
            if (item.getParentID() == null || item.getParentID().equals("-1")) {
                treeNode.setId(item.getId());
                treeNode.setName(item.getName());
            } else {
                treeNode.setId(item.getId());
                treeNode.setName(item.getName());
                treeNode.setParentId(item.getParentID());
            }
            list.add(treeNode);
        });

        return TreeNode.buildTree(list);
    }

    @Override
    public List<AGroupDO> getAGroupDataList(String groupType, String type, String id) {
        LambdaQueryWrapperX<AGroupDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eqIfPresent(AGroupDO::getGroupType, groupType);
        wrapperX.likeIfPresent(AGroupDO::getFullID, id);
        if (!type.equals("AllRole")){
            wrapperX.eqIfPresent(AGroupDO::getType, type);
        }
        return aGroupMapper.selectList(wrapperX);
    }

    @Override
    public List<String> getResIdsByGroupId(String id) {
        List<GroupresDO> groupresDOS = groupresMapper.selectList(new LambdaQueryWrapperX<GroupresDO>().eq(GroupresDO::getGroupID, id));
        List<String> collect = groupresDOS.stream().map(a -> a.getResID()).collect(Collectors.toList());
        return collect;
    }

    @Override
    public void saveGroupRes(SaveGroupResReqVO saveGroupResReqVO) {


        String groupId = saveGroupResReqVO.getGroupId();
        //根据groupId删除全部数据
        groupresMapper.delete(new LambdaQueryWrapperX<GroupresDO>().eq(GroupresDO::getGroupID, groupId));
        //删除后批量保存
        String[] resIds = saveGroupResReqVO.getResIds();
        List<GroupresDO> list = new ArrayList<>();
        for (String resId : resIds) {
            GroupresDO groupresDO = new GroupresDO();
            groupresDO.setGroupID(groupId);
            groupresDO.setResID(resId);
            list.add(groupresDO);
        }
        groupresMapper.insertBatch(list);
    }

    @Override
    public List<String> getAllGroupIdsByDeptId(String deptID) {
        return groupresMapper.getAllGroupIdsByDeptId(deptID);
    }

    @Override
    public List<AGroupDO> getAuthRole(String resId) {
        List<String> groupresDOS = groupresMapper.selectList(new LambdaQueryWrapperX<GroupresDO>()
                .eq(GroupresDO::getResID, resId))
                .stream().map(GroupresDO::getGroupID).collect(Collectors.toList());

        return aGroupMapper.selectList(new LambdaQueryWrapperX<AGroupDO>()
                .in(AGroupDO::getId, groupresDOS));
    }

    @Override
    public String saveAGroupRes(String resId, String groupIds, Boolean isAuthLower) {
        //判断是否对下级节点进行授权
        List<String> strings = Arrays.asList(groupIds.split(","));
        List<GroupresDO> list = new ArrayList<>();
        if (isAuthLower){
            List<AResDO> aResList = resMapper.selectList(new LambdaQueryWrapperX<AResDO>().like(AResDO::getFullID, resId));
            aResList.stream().forEach(item -> {
                strings.stream().forEach(group -> {
                    GroupresDO groupresDO = new GroupresDO();
                    groupresDO.setGroupID(group);
                    groupresDO.setResID(item.getId());
                    groupresDO.setCreateTime(LocalDateTime.now());
                    list.add(groupresDO);
                });
            });
        }else {
            strings.stream().forEach(item -> {
                GroupresDO groupresDO = new GroupresDO();
                groupresDO.setGroupID(item);
                groupresDO.setResID(resId);
                groupresDO.setCreateTime(LocalDateTime.now());
                list.add(groupresDO);
            });
        }

        return  groupresMapper.insertOrUpdateBatch(list) ? "success" : "fail";
    }
}