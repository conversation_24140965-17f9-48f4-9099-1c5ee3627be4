package mh.cloud.module.system.service.groupres;

import java.util.*;
import jakarta.validation.*;
import mh.cloud.module.system.controller.admin.groupres.vo.*;
import mh.cloud.module.system.dal.dataobject.groupres.GroupresDO;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.pojo.PageParam;

/**
 * 资源-角色关联 Service 接口
 *
 * <AUTHOR>
 */
public interface GroupresService {

    /**
     * 创建资源-角色关联
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createGroupres(@Valid GroupresSaveReqVO createReqVO);

    /**
     * 更新资源-角色关联
     *
     * @param updateReqVO 更新信息
     */
    void updateGroupres(@Valid GroupresSaveReqVO updateReqVO);

    /**
     * 删除资源-角色关联
     *
     * @param id 编号
     */
    void deleteGroupres(String id);

    /**
     * 获得资源-角色关联
     *
     * @param id 编号
     * @return 资源-角色关联
     */
    GroupresDO getGroupres(String id);

    /**
     * 获得资源-角色关联分页
     *
     * @param pageReqVO 分页查询
     * @return 资源-角色关联分页
     */
    PageResult<GroupresDO> getGroupresPage(GroupresPageReqVO pageReqVO);

}