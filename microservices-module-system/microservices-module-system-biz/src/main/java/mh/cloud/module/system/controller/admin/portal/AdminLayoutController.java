package mh.cloud.module.system.controller.admin.portal;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import mh.cloud.framework.apilog.core.annotation.ApiAccessLog;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.module.system.controller.admin.portal.V0.CopyLayoutVo;
import mh.cloud.module.system.controller.admin.portal.V0.SysLayoutGroupVo;
import mh.cloud.module.system.service.portal.LayoutGroupService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "布局模块管理配置")
@RestController
@RequestMapping("/system/admin-layout")
@Validated
public class AdminLayoutController {

    @Resource
    private LayoutGroupService layoutGroupService;


    @GetMapping("/list")
    @Operation(summary = "管理获取用户布局")
    @ApiAccessLog
    public CommonResult<Object> getLayoutData(
            @RequestParam(value = "category", required = false) String category,
            @RequestParam(value = "type", required = false) String type,
            @RequestParam(value = "userId", required = false) String userId,
            @RequestParam(value = "group", required = false) String group
    ) {

        return CommonResult.success(layoutGroupService.getLayoutData(category, type, userId, group));
    }

    @PostMapping("/saveOrUpdate")
    @Operation(summary = "保存或修改用户布局")
    public CommonResult<Object> saveAndUpdateLayoutGroup(@RequestBody List<SysLayoutGroupVo> sysLayoutGroupList,Boolean copy) {

        return CommonResult.success(layoutGroupService.saveAndUpdateLayoutGroup(sysLayoutGroupList,copy));
    }

    @PostMapping("/copyLayout")
    @Operation(summary = "复制布局")
    public CommonResult<Object> copyLayout(@RequestBody List<CopyLayoutVo> copyLayouts) {

        return CommonResult.success(layoutGroupService.copyLayout(copyLayouts));
    }
}
