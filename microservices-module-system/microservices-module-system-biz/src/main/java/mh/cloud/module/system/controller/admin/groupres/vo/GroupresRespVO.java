package mh.cloud.module.system.controller.admin.groupres.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 资源-角色关联 Response VO")
@Data
@ExcelIgnoreUnannotated
public class GroupresRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "13678")
    @ExcelProperty("id")
    private String id;

    @Schema(description = "groupId", requiredMode = Schema.RequiredMode.REQUIRED, example = "8484")
    @ExcelProperty("groupId")
    private String groupID;

    @Schema(description = "ResID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10944")
    @ExcelProperty("ResID")
    private String resID;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}