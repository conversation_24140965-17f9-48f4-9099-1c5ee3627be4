package mh.cloud.module.system.controller.admin.portal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.module.system.controller.admin.portal.V0.*;
import mh.cloud.module.system.controller.admin.portal.service.InfoManageService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

import static mh.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "消息接口")
@RestController
@RequestMapping("/system/InfoManage/Msg")
@Validated
public class InfoManageController {

    @Resource
    private InfoManageService infoManageService;

    @PostMapping("/GetReceiveList")
    @Operation(summary = "获取未读消息")
    public CommonResult<Page<Map<String, Object>>> GetReceiveList(MsgReq msgReq) {
        return CommonResult.success(infoManageService.getReceiveList(msgReq));
    }
    @PostMapping("/SetMsgRed")
    @Operation(summary = "未读消息标记为已读")
    public CommonResult<Boolean> SetMsgRed(String ids) {
        infoManageService.SetMsgRed(ids);
        return success(true);
    }
    @PostMapping("/DeleteMsg")
    @Operation(summary = "删除消息")
    public CommonResult<Boolean> DeleteMsg(String ids) {
        infoManageService.DeleteMsg(ids);
        return success(true);
    }

    @PostMapping("/GetNormalLinkManTree")
    @Operation(summary = "常用联系人")
    public CommonResult<List<Map<String, Object>>> getNormalLinkManTree(OrgMsgReq orgMsgReq) {
        return CommonResult.success(infoManageService.getNormalLinkManTree(orgMsgReq));
    }

    @PostMapping("/GetOrgTree")
    @Operation(summary = "组织结构+联系人")
    public CommonResult<List<OrgTree>> getOrgTree(OrgMsgReq orgMsgReq) {
        return CommonResult.success( infoManageService.getOrgTree(orgMsgReq));
    }
    @PostMapping("/Save")
    @Operation(summary = "发送消息，回复消息，保存新消息")
    public CommonResult<Object> save(MsgSaveReq msgSaveReq) {
        return CommonResult.success( infoManageService.saveMsg(msgSaveReq));
    }

    @PostMapping("/GetReceiveReadList")
    @Operation(summary = "已读人列表")
    public CommonResult<List<Map<String, Object>>> getReceiveReadList(String id) {
        return CommonResult.success(infoManageService.getReceiveReadList(id));
    }
    @PostMapping("/GetReceiveModel")
    @Operation(summary = "消息详情")
    public CommonResult<Map<String, Object>> getReceiveModel(ReceiveModelReq req) {
        return CommonResult.success(infoManageService.GetReceiveModel(req));
    }

}
