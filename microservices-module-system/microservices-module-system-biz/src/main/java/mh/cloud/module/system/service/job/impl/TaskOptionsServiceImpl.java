package mh.cloud.module.system.service.job.impl;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import mh.cloud.framework.common.exception.BaseException;
import mh.cloud.framework.common.exception.ServiceException;
import mh.cloud.module.system.dal.dataobject.job.NewTimerTask;
import mh.cloud.module.system.dal.dataobject.job.TaskApiSQL;
import mh.cloud.module.system.dal.dataobject.job.TaskApiUrl;
import mh.cloud.module.system.dal.dataobject.job.TaskOptions;
import mh.cloud.module.system.dal.mysql.job.TaskApiSQLMapper;
import mh.cloud.module.system.dal.mysql.job.TaskApiUrlMapper;
import mh.cloud.module.system.dal.mysql.job.TaskOptionsMapper;
import mh.cloud.module.system.job.core.handler.SchedulerManager;
import mh.cloud.module.system.service.job.TaskOptionsService;
import org.quartz.SchedulerException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class TaskOptionsServiceImpl extends ServiceImpl<TaskOptionsMapper, TaskOptions> implements TaskOptionsService {

    @Resource
    private TaskApiUrlMapper taskApiUrlMapper;
    @Resource
    private TaskApiSQLMapper taskApiSQLMapper;
    @Resource
    private TaskOptionsMapper taskOptionsMapper;

    @Resource
    private SchedulerManager scheduler;


    /**
     * 获取调度任务列表
     *
     * @return list
     */
    @Override
    public Object getTaskList(String taskGroup, Integer pageNo, Integer pageSize, String taskName, String taskState) {
        //执行远程调用
        IPage<TaskOptions> page = new Page<>(pageNo, pageSize);
        taskOptionsMapper.selectPage(page, Wrappers.<TaskOptions>lambdaQuery()
                .eq(ObjUtil.isNotEmpty(taskGroup), TaskOptions::getTaskGroup, taskGroup)
                .eq(ObjUtil.isNotEmpty(taskState), TaskOptions::getTaskState, taskState)
                .like(ObjUtil.isNotEmpty(taskName), TaskOptions::getTaskName, taskName)
                .and(wrapper -> wrapper.ne(TaskOptions::getIsDeleted, "1").or().isNull(TaskOptions::getIsDeleted)));

        //判断任务是否已经加入调度
        List<TaskOptions> options = page.getRecords().stream().peek(item -> {
            String jobId = item.getID();
            String group = item.getTaskGroup();
            try {
                if (scheduler.checkExists(jobId, group)) {
                    item.setInit(1);
                } else {
                    item.setInit(0);
                }

            } catch (SchedulerException e) {
                log.error("任务调度校验异常,异常信息：{}", e.getMessage());
                item.setInit(-1);
            }
        }).toList();
        page.setRecords(options);
        return page;
    }

    @Override
    public TaskOptions getTask(String id) {
        if (ObjUtil.isEmpty(id)) {
            throw new BaseException("定时任务数据id不存在");
        }
        NewTimerTask taskOptions = taskOptionsMapper.selectDetailById(id);
        try {
            if (scheduler.checkExists(taskOptions.getID(), taskOptions.getTaskGroup())) {
                taskOptions.setInit(1);
            } else {
                taskOptions.setInit(0);
            }
        } catch (SchedulerException e) {
            throw new RuntimeException(e);
        }
        List<TaskApiUrl> taskApiUrls = taskApiUrlMapper.selectList(Wrappers.<TaskApiUrl>lambdaQuery().eq(TaskApiUrl::getNewTimerTaskID, id));
        List<TaskApiSQL> taskApiSQLS = taskApiSQLMapper.selectList(Wrappers.<TaskApiSQL>lambdaQuery().eq(TaskApiSQL::getNewTimerTaskID, id));
        if (!taskApiUrls.isEmpty()) {
            List<TaskApiUrl> list = taskApiUrls.stream().sorted(Comparator.comparing(TaskApiUrl::getSortIndex)).toList();
            taskOptions.setTaskApiUrlList(list);
        }
        if (!taskApiSQLS.isEmpty()) {
            List<TaskApiSQL> list = taskApiSQLS.stream().sorted(Comparator.comparing(TaskApiSQL::getSortIndex)).toList();
            taskOptions.setTaskApiSQLList(list);
        }
        return taskOptions;
    }

    @Override
    public Boolean initTask(List<String> jobIds) {
        if (jobIds.size() > 1000) {
            throw new BaseException("数据处理超过上线：{}" + jobIds.size());
        }
        List<TaskOptions> taskOptions = taskOptionsMapper.selectList(Wrappers.<TaskOptions>
                lambdaQuery().in(TaskOptions::getID, jobIds));
        if (ObjUtil.isNotEmpty(taskOptions)) {
            for (TaskOptions taskOption : taskOptions) {
                try {
                    handleTaskState(taskOption);
                } catch (SchedulerException e) {
                    throw new BaseException("数据初始化异常，异常信息：{}" + e.getMessage());
                }
            }
        }
        return true;
    }

    /**
     * 任务状态处理
     *
     * @return
     */
    private void handleTaskState(TaskOptions task) throws SchedulerException {
        Integer taskState = task.getTaskState();
        Boolean exists = scheduler.checkExists(task.getID(), task.getTaskGroup());
        String jobId = task.getID();
        String taskGroup = task.getTaskGroup();

        if (!exists && taskState != 2) {
            scheduler.addJob(jobId, taskGroup, task.getCron(), false);
        }

        switch (taskState) {
            case 0: {
                //判断是否在运行，没有启动任务
                if (!scheduler.checkIsRun(jobId, taskGroup)) {
                    scheduler.resumeJob(jobId, taskGroup);
                }
                break;
            }
            case 1, 6, 7, 5, 3: {
                //新增,开启，停止,立即执行 ,修改 暂时不做处理
                break;
            }
            case 2: {
                //删除 ,这里指删除调度数据
                if (exists) {
                    scheduler.deleteJob(jobId, taskGroup);
                }
                break;
            }
            case 4: {
                //暂停
                if (scheduler.checkIsRun(jobId, taskGroup)) {
                    scheduler.pauseJob(jobId, taskGroup);
                }
                break;
            }
            default:
                throw new BaseException("定时任务状态异常");
        }
    }

    /**
     * 添加定时任务
     *
     * @param taskId 任务数据
     */
    @Override
    public Boolean addTask(String taskId) {
        TaskOptions options = this.getById(taskId);
        if (ObjUtil.isEmpty(options)) {
            throw new BaseException("定时任务信息不存在");
        }
        try {
            if (!scheduler.checkExists(options.getID(), options.getTaskGroup())) {
                scheduler.addJob(options.getID(), options.getTaskGroup(), options.getCron(), true);
            }
        } catch (SchedulerException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    /**
     * 修改定时任务
     *
     * @param taskId 任务Id
     */
    @Override
    public Boolean updateTask(String taskId) {

        TaskOptions options = this.getById(taskId);
        if (ObjUtil.isEmpty(options)) {
            throw new BaseException("定时任务信息不存在");
        }
        try {
            if (!scheduler.checkExists(options.getID(), options.getTaskGroup())) {
                throw new BaseException("请先加载定时任务");
            }
            scheduler.updateJob(options.getID(), options.getTaskGroup(), options.getCron(), options.getTaskState());
            return true;
        } catch (SchedulerException e) {
            throw new RuntimeException(e);
        }
    }

    private Boolean updateApi(List<TaskApiUrl> taskApiUrlList, List<TaskApiSQL> taskApiSQLList, String taskId) {
        taskApiSQLMapper.delete(Wrappers.<TaskApiSQL>lambdaQuery().eq(TaskApiSQL::getNewTimerTaskID, taskId));
        taskApiUrlMapper.delete(Wrappers.<TaskApiUrl>lambdaQuery().eq(TaskApiUrl::getNewTimerTaskID, taskId));

        //一共没有几条数据，不统一处理了
        if (!taskApiSQLList.isEmpty()) {
            for (TaskApiSQL taskApiSQL : taskApiSQLList) {
                taskApiSQLMapper.insert(taskApiSQL);
            }
        }
        if (!taskApiUrlList.isEmpty()) {
            for (TaskApiUrl taskApiUrl : taskApiUrlList) {
                taskApiUrlMapper.insert(taskApiUrl);
            }
        }
        return true;
    }

    /**
     * 运行恢复定时任务
     *
     * @param jobId 任务id
     */
    @Override
    public Boolean resumeTask(String jobId) {
        log.info("恢复定时任务开始，jobId: {}", jobId);
        TaskOptions taskOptions = taskOptionsMapper.selectById(jobId);
        if (ObjUtil.isEmpty(taskOptions)) {
            log.error("恢复定时任务失败，任务信息不存在，jobId: {}", jobId);
            throw new ServiceException("任务信息不存在");
        }
        try {
            if (!scheduler.checkExists(jobId, taskOptions.getTaskGroup())) {
                //不存在直接添加运行
                log.info("任务不存在于调度器中，直接添加并运行，jobId: {}", jobId);
                scheduler.addJob(jobId, taskOptions.getTaskGroup(), taskOptions.getCron(), true);
            } else {
                log.info("任务存在于调度器中，恢复运行，jobId: {}", jobId);
                scheduler.resumeJob(jobId, taskOptions.getTaskGroup());
            }
            //修改job状态为运行状态
            updateTaskState(jobId, 0);
            log.info("恢复定时任务成功，jobId: {}，状态已更新为运行", jobId);
        } catch (SchedulerException e) {
            log.error("开启定时任务失败，jobId: {}，错误信息: {}", jobId, e.getMessage(), e);
            throw new ServiceException("开启定时任务失败," + e.getMessage());
        }
        return true;
    }

    /**
     * 开启停止的定时任务
     *
     * @param jobId
     */
    @Override
    public Boolean startJob(String jobId) {
        TaskOptions taskOptions = taskOptionsMapper.selectById(jobId);
        if (ObjUtil.isEmpty(taskOptions)) {
            throw new ServiceException("任务信息不存在");
        }
        try {
            if (!scheduler.checkExists(jobId, taskOptions.getTaskGroup())) {
                throw new ServiceException("请先初始化任务");
            }
            scheduler.startJob(jobId, taskOptions.getTaskGroup(), taskOptions.getCron());

            //修改job状态
            updateTaskState(jobId, 4);

        } catch (SchedulerException e) {
            log.error("立即执行定时任务失败" + e.getMessage());
            throw new ServiceException("开启定时任务失败," + e.getMessage());
        }
        return true;
    }

    /**
     * 立即执行一次
     *
     * @param jobId 任务id
     */
    @Override
    public Boolean trigger(String jobId) {
        TaskOptions taskOptions = taskOptionsMapper.selectById(jobId);
        if (ObjUtil.isEmpty(taskOptions)) {
            throw new ServiceException("任务信息不存在");
        }
        try {
            if (!scheduler.checkExists(jobId, taskOptions.getTaskGroup())) {
                throw new BaseException("请先加载项目");
            }
            scheduler.triggerJob(jobId, taskOptions.getTaskGroup());
        } catch (SchedulerException e) {
            log.error("立即执行定时任务失败" + e.getMessage());
            throw new ServiceException("开启定时任务失败," + e.getMessage());
        }
        return true;
    }

    /**
     * 暂停定时任务
     *
     * @param jobId 任务id
     */
    @Override
    public Boolean pauseTask(String jobId) {
        TaskOptions taskOptions = taskOptionsMapper.selectById(jobId);
        if (ObjUtil.isEmpty(taskOptions)) {
            throw new ServiceException("任务信息不存在");
        }
        try {
            if (!scheduler.checkExists(jobId, taskOptions.getTaskGroup())) {
                throw new ServiceException("请先初始化数据");
            }
            scheduler.pauseJob(jobId, taskOptions.getTaskGroup());

            //修改job状态
            updateTaskState(jobId, 4);

        } catch (SchedulerException e) {
            log.error("停止定时任务失败" + e.getMessage());
            throw new ServiceException("停止定时任务失败," + e.getMessage());
        }
        return true;
    }

    /**
     * 停止定时任务
     *
     * @param jobId 任务id
     */
    @Override
    public Boolean stopTask(String jobId) {
        TaskOptions taskOptions = taskOptionsMapper.selectById(jobId);
        if (ObjUtil.isEmpty(taskOptions)) {
            throw new ServiceException("任务信息不存在");
        }
        try {
            if (!scheduler.checkExists(jobId, taskOptions.getTaskGroup())) {
                throw new ServiceException("请先初始化数据");
            }
            scheduler.stopJob(jobId, taskOptions.getTaskGroup());

            //修改job状态
            updateTaskState(jobId, 5);

        } catch (SchedulerException e) {
            log.error("停止定时任务失败" + e.getMessage());
            throw new ServiceException("停止定时任务失败," + e.getMessage());
        }
        return true;
    }

    /**
     * 删除定时任务
     *
     * @param taskId 任务id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteTask(String taskId) {
        TaskOptions taskOption = taskOptionsMapper.selectById(taskId);
        if (ObjUtil.isEmpty(taskOption)) {
            throw new ServiceException("任务信息不存在");
        }
        String jobId = taskOption.getID();
        try {
            if (scheduler.checkExists(jobId, taskOption.getTaskGroup())) {
                scheduler.deleteJob(jobId, taskOption.getTaskGroup());
            }
            //修改job状态
            updateTaskState(jobId, 2);
        } catch (SchedulerException e) {
            log.error("停止定时任务失败" + e.getMessage());
            throw new ServiceException("停止定时任务失败," + e.getMessage());
        }

        return true;
    }

    @Override
    public Date getTaskNextTimes(String jobId, String jobGroup) {
        return scheduler.getNextFireTime(jobId, jobGroup);
    }

    private void updateTaskState(String jobId, Integer state) {
        if (ObjUtil.isEmpty(jobId)) {
            throw new ServiceException("任务id参数不存在，修改失败");
        }
        taskOptionsMapper.update(Wrappers.<TaskOptions>lambdaUpdate()
                .set(ObjUtil.isNotEmpty(state), TaskOptions::getTaskState, state)
                .eq(TaskOptions::getID, jobId));
    }

    @Override
    public Boolean startTask(String taskId) {
        TaskOptions taskOptions = taskOptionsMapper.selectById(taskId);
        if (ObjUtil.isEmpty(taskOptions)) {
            throw new ServiceException("任务信息不存在");
        }
        try {
            if (scheduler.checkExists(taskId, taskOptions.getTaskGroup())) {
                return true;
            }
            scheduler.addJob(taskOptions.getID(), taskOptions.getTaskGroup(), taskOptions.getCron(), false);
            //修改状态为暂停
            updateTaskState(taskId, 4);
        } catch (SchedulerException e) {
            log.error("重启恢复定时任务失败" + e.getMessage());
            throw new ServiceException("重启恢复定时任务失败," + e.getMessage());
        }
        return true;
    }
}
