package mh.cloud.module.system.controller.admin.portal.V0;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

@TableName("system_layout")
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysLayoutVo {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 数据id
     */
    @TableField(value = "data_id")
    private String dataId;

    /**
     * 布局标志
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 布局坐标
     */
    @TableField(value = "layout")
    private String layout;

    /**
     * 添加使用
     */
    @TableField(value = "start_use")
    private Integer startUse;


    @TableField(value = "tenant_id")
    private String tenantId;

    @TableField(value = "create_time")
    private LocalDateTime createTime;


    @TableField(value = "update_time")
    private LocalDateTime updateTime;

}
