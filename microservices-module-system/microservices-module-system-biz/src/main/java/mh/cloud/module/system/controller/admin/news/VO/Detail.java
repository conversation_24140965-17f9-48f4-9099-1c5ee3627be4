package mh.cloud.module.system.controller.admin.news.VO;

import lombok.Data;

@Data
public class Detail {
    private String ID;
    private String Title;
    private String Content;
    private String CreateTime;
    private String UnitName;
    private String SenderName;
    private String ReadCount;

    // 新的附件上传使用该字段存储的，中间使用逗号分隔
    private String Attachments;

    // 如果值为“中心网”，则需要去老的附件库查询，根据新闻ID
    private String BigFl;

    // 老的附件存储字段
    private String OldAttachments;

    // 落款
    private String LK;

    // 文件日期
    private String WJDate;

}
