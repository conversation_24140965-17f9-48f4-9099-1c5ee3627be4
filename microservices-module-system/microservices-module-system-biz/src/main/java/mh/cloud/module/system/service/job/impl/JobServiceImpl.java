package mh.cloud.module.system.service.job.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import mh.cloud.framework.common.exception.BaseException;
import mh.cloud.framework.common.exception.ServiceException;
import mh.cloud.module.system.dal.dataobject.job.JobApi;
import mh.cloud.module.system.dal.dataobject.job.JobTaskType;
import mh.cloud.module.system.dal.dataobject.job.SysJob;
import mh.cloud.module.system.dal.dataobject.job.SysJobVo;
import mh.cloud.module.system.dal.mysql.job.JobMapper;
import mh.cloud.module.system.job.core.handler.SchedulerManager;
import mh.cloud.module.system.service.job.IJobApiService;
import mh.cloud.module.system.service.job.IJobService;
import org.quartz.SchedulerException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static mh.cloud.framework.common.exception.enums.GlobalErrorCodeConstants.BAD_REQUEST;
import static mh.cloud.framework.common.exception.enums.GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR;

@Service
@RequiredArgsConstructor
public class JobServiceImpl extends ServiceImpl<JobMapper, SysJob> implements IJobService {


    private final SchedulerManager scheduler;

    private final IJobApiService jobApiService;

    /**
     * 新增定时任务
     *
     * @param jobVo 任务信息
     * @return 处理数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertJob(SysJobVo jobVo) {
        SysJob job = BeanUtil.toBean(jobVo, SysJob.class);
        if (!ObjUtil.isAllNotEmpty(job.getStatus(), job.getCronExpression())) {
            throw new ServiceException(BAD_REQUEST.getCode(), BAD_REQUEST.getMsg());
        }

        boolean isStart = job.getStatus().equals("1");
        LocalDateTime now = LocalDateTime.now();
        job.setCreateTime(now);
        job.setUpdateTime(now);
        if (!this.save(job)) {
            throw new BaseException("定时任务，添加失败");
        }
        //  处理封装api列表数据
        List<JobApi> apiSQLList = jobVo.getApiSQLList();
        List<JobApi> apiUrlList = jobVo.getApiUrlList();
        jobApiService.saveAllApi(apiSQLList, apiUrlList, job.getJobId());

        try {
            scheduler.addJob(job.getJobId(), job.getJobHandlerName(), job.getCronExpression(), isStart);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return true;
    }

    /**
     * 更新任务
     *
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateJob(SysJobVo jobVo) throws SchedulerException {
        SysJob job = BeanUtil.toBean(jobVo, SysJob.class);
        SysJob sysJob = this.getById(job.getJobId());
        if (ObjUtil.isEmpty(sysJob)) {
            throw new BaseException("定时任务信息不存在");
        }
        boolean rows = this.updateById(job);
        //删除历史任务，重新添加
        jobApiService.remove(Wrappers.<JobApi>lambdaQuery().eq(JobApi::getJobId, job.getJobId()));
        jobApiService.saveAllApi(jobVo.getApiSQLList(), jobVo.getApiUrlList(), job.getJobId());

        if (rows) {
            updateSchedulerJob(job);
        }
        return rows;
    }

    /**
     * 更新任务
     *
     * @param job 任务对象
     */
    public void updateSchedulerJob(SysJob job) throws SchedulerException {
        scheduler.updateJob(job.getJobId(), job.getJobHandlerName(), job.getCronExpression(), Integer.valueOf(job.getStatus()));
    }

    /**
     * 暂停任务
     *
     * @param jobId 调度信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean pauseJob(String jobId) {
        SysJob job = this.getById(jobId);
        if (ObjUtil.isEmpty(job)) {
            throw new BaseException("任务信息不存在");
        }
        boolean update = lambdaUpdate().set(SysJob::getStatus, "0")
                .set(SysJob::getCreateTime, LocalDateTime.now()).eq(SysJob::getJobId, jobId).update();

        try {
            scheduler.pauseJob(job.getJobId(), job.getJobHandlerName());
        } catch (SchedulerException e) {
            throw new RuntimeException(e);
        }

        return update;
    }

    /**
     * 恢复任务
     *
     * @param jobId 调度信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resumeJob(String jobId) {
        SysJob job = this.getById(jobId);
        if (ObjUtil.isEmpty(job)) {
            throw new BaseException("任务信息不存在");
        }
        boolean update = lambdaUpdate().set(SysJob::getStatus, "1")
                .set(SysJob::getCreateTime, LocalDateTime.now()).eq(SysJob::getJobId, jobId).update();
        try {
            scheduler.resumeJob(job.getJobId(), job.getJobHandlerName());
        } catch (SchedulerException e) {
            throw new RuntimeException(e);
        }
        return update;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteJob(String jobId) {
        SysJob job = this.getById(jobId);

        boolean remove = this.removeById(jobId);
        jobApiService.remove(Wrappers.<JobApi>lambdaQuery().eq(JobApi::getJobId, job.getJobId()));
        try {
            scheduler.deleteJob(job.getJobId(), job.getJobHandlerName());
        } catch (SchedulerException e) {
            throw new RuntimeException(e);
        }
        return remove;
    }

    /**
     * 获取下次执行时间
     */
    @Override
    public Date getNextFireTime(String jobId, String jobName) {
        return scheduler.getNextFireTime(jobName, jobId);
    }

    /**
     * 立即执行一次
     */
    @Override
    public Boolean trigger(String jobId) {
        SysJob job = this.getById(jobId);
        if (ObjUtil.isEmpty(job)) {
            throw new ServiceException(INTERNAL_SERVER_ERROR.getCode(), "任务信息不存在");
        }
        try {
            scheduler.triggerJob(job.getJobId(), job.getJobHandlerName());
            return true;
        } catch (SchedulerException e) {
            throw new ServiceException(INTERNAL_SERVER_ERROR.getCode(), "job执行异常");
        }
    }

    /**
     * 获取任务详情
     */
    @Override
    public SysJobVo getJob(String jobId) {
        SysJob sysJob = this.getById(jobId);
        SysJobVo jobVo = BeanUtil.toBean(sysJob, SysJobVo.class);
        Map<String, List<JobApi>> apiMap = jobApiService.getApiMap(jobId);
        if (apiMap.containsKey(JobTaskType.API_URL)) {
            List<JobApi> jobApis = apiMap.get(JobTaskType.API_URL);
            jobVo.setApiUrlList(jobApis);
        }
        if (apiMap.containsKey(JobTaskType.API_SQL)) {
            List<JobApi> jobApis = apiMap.get(JobTaskType.API_SQL);
            jobVo.setApiSQLList(jobApis);
        }
        return jobVo;
    }
}
