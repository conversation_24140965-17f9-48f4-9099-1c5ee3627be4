package mh.cloud.module.system.controller.admin.portal.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import mh.cloud.module.system.controller.admin.portal.V0.*;

import java.util.List;
import java.util.Map;

public interface InfoManageService {

    /**
     * 获取未读消息
     * @param msgReq
     * @return
     */
    Page<Map<String, Object>> getReceiveList(MsgReq msgReq);
    /**
     * 消息标记为已读
     * @param ids 消息id集合
     */
    void SetMsgRed(String ids);

    /**
     * 删除勾选消息
     * @param ids 消息id集合
     */
    void DeleteMsg(String ids);

    /**
     * 常用联系人
     * @param orgMsgReq
     * @return
     */
    List<Map<String, Object>> getNormalLinkManTree(OrgMsgReq orgMsgReq);
    /**
     * 组织结构+联系人树
     * @param orgMsgReq
     * @return
     */
    List<OrgTree> getOrgTree(OrgMsgReq orgMsgReq);

    /**
     * 保存消息
     * @param msgSaveReq
     * @return
     */
    Object saveMsg(MsgSaveReq msgSaveReq);

    /**
     * 获取已读消息
     * @param id
     * @return
     */
    List<Map<String, Object>> getReceiveReadList(String id);

    /**
     * 获取消息详情
     * @param req
     * @return
     */
    Map<String, Object> GetReceiveModel(ReceiveModelReq req);
}
