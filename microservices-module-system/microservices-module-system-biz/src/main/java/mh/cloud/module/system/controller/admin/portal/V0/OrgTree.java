package mh.cloud.module.system.controller.admin.portal.V0;

import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class OrgTree {
    public OrgTree(){
        super();
    }
    public OrgTree(String ID, String Code, String FullID, String Name, String ParentID, List<Map<String, Object>> UserFields) {
        this.ID = ID;
        this.Code = Code;
        this.FullID = FullID;
        this.Name = Name;
        this.ParentID = ParentID;
        this.UserFields = UserFields;
    }

    private String ID;
    private String Code;
    private String FullID;
    private String Name;
    private String ParentID;
    private List<Map<String, Object>> UserFields;
    private List<OrgTree> children = new ArrayList<>();

    public void addChild(OrgTree child) {
        child.setParentID(this.ID);
        children.add(child);
    }

    public static List<OrgTree> buildTree(List<OrgTree> nodes) {
        Map<String, OrgTree> nodeMap = new HashMap<>();

        for (OrgTree node : nodes) {
            nodeMap.put(node.getID(), node);
        }
        List<OrgTree> roots = new ArrayList<>();

        for (OrgTree node : nodes) {
            if (node.getParentId() == null || node.getParentId().equals("")) {
                roots.add(node);
            } else {
                OrgTree parentNode = nodeMap.get(node.getParentId());
                if (parentNode != null) {
                    parentNode.addChild(node);
                }
            }
        }

        return roots;
    }

    public String getParentId() {
        return this.ParentID;
    }
}