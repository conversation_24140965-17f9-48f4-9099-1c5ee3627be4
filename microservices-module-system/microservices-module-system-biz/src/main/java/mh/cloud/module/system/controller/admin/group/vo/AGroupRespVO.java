package mh.cloud.module.system.controller.admin.group.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import com.alibaba.excel.annotation.*;
import mh.cloud.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 系统角色管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AGroupRespVO extends PageParam {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "17234")
    @ExcelProperty("id")
    private String id;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "菜单名字")
    @ExcelProperty("名称")
    private String name;

    @Schema(description = "简称", example = "赵六")
    @ExcelProperty("简称")
    private String shortName;

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("编号")
    private String code;

    @Schema(description = "父节点ID", example = "25940")
    @ExcelProperty("父节点ID")
    private String parentID;

    @Schema(description = "节点全路径", example = "27943")
    @ExcelProperty("节点全路径")
    private String fullID;

    @Schema(description = "分组类型（组织或角色）", example = "1")
    @ExcelProperty("分组类型（组织或角色）")
    private String groupType;

    @Schema(description = "类型", example = "1")
    @ExcelProperty("类型")
    private String type;

}