package mh.cloud.module.system.service.logger;

import lombok.extern.slf4j.Slf4j;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.util.object.BeanUtils;
import mh.cloud.module.system.api.logger.dto.LoginLogCreateReqDTO;
import mh.cloud.module.system.controller.admin.logger.vo.loginlog.LoginLogPageReqVO;
import mh.cloud.module.system.dal.dataobject.logger.LoginLogDO;
import mh.cloud.module.system.dal.mysql.logger.LoginLogMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;

/**
 * 登录日志 Service 实现
 */
@Service
@Validated
@Slf4j
public class LoginLogServiceImpl implements LoginLogService {

    @Resource
    private LoginLogMapper loginLogMapper;

    @Override
    public PageResult<LoginLogDO> getLoginLogPage(LoginLogPageReqVO pageReqVO) {
        return loginLogMapper.selectPage(pageReqVO);
    }

    @Override
    public void createLoginLog(LoginLogCreateReqDTO reqDTO) {
        LoginLogDO loginLog = BeanUtils.toBean(reqDTO, LoginLogDO.class);
        try {
            loginLogMapper.insert(loginLog);
        } catch (Exception e) {
           //记录日志失败不影响数据操作
            log.error("登录日志 Service 异常：日志信息：{}",loginLog);
            log.error("异常信息：{}",e.getMessage());
        }
    }

}
