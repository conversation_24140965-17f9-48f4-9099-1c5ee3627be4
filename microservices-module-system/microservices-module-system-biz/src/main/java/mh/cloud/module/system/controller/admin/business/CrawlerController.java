package mh.cloud.module.system.controller.admin.business;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import lombok.Data;
import mh.cloud.module.system.dal.dataobject.IPublicInformation.IPublicInformationDo;
import mh.cloud.module.system.service.crawler.CrawlerToolService;
import mh.cloud.module.system.util.ConfigurationHelper;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 爬取数据处理")
@RestController
@RequestMapping("/business/crawler")
@Validated
@Data
public class CrawlerController {

    @Resource
    private ConfigurationHelper configurationHelper;

    @Resource
    private CrawlerToolService crawlerToolService;


    @PostMapping("/getjtyw")
    @Operation(summary = "爬取集团要闻")
    @PermitAll
    public Object CrawlerGetjtyw(String url) {
        IPublicInformationDo obj = new IPublicInformationDo();
        obj.setCatalogId("ad590096-adf5-4bc2-97aa-6d88422d26bc");
        obj.setCataLogName("集团要闻");
        String djGroupNews;
        if(url == null || url.isEmpty()){
            djGroupNews = configurationHelper.GetSettingValue("djGroupNews");
        }else{
            djGroupNews = url;
        }
        Integer num = crawlerToolService.getNews(0, djGroupNews);
        return num;
    }

    @PostMapping("/getnews")
    @Operation(summary = "爬取时政新闻")
    @PermitAll
    public Object CrawlerGetnews(String url) {
        IPublicInformationDo obj = new IPublicInformationDo();
        obj.setCatalogId("ad590097-319b-4c5f-88d1-9a709337ca9e");
        String djCurPolitNews;
        if(url == null || url.isEmpty()){
            djCurPolitNews = configurationHelper.GetSettingValue("djCurPolitNews");}
        else{
            djCurPolitNews = url;
        }
        Integer num = crawlerToolService.getNews(1, djCurPolitNews);
        return num;
    }
}
