package mh.cloud.module.system.controller.admin.groupres.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 资源-角色关联新增/修改 Request VO")
@Data
public class GroupresSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "13678")
    private String id;

    @Schema(description = "groupId", requiredMode = Schema.RequiredMode.REQUIRED, example = "8484")
    @NotEmpty(message = "groupId不能为空")
    private String groupID;

    @Schema(description = "ResID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10944")
    @NotEmpty(message = "ResID不能为空")
    private String resID;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "创建时间不能为空")
    private LocalDateTime createTime;

}