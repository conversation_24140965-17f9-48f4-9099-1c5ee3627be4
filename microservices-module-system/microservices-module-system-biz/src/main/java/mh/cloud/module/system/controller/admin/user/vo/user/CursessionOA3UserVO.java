package mh.cloud.module.system.controller.admin.user.vo.user;

import lombok.*;
import mh.cloud.module.system.dal.dataobject.auser.AUserDO;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CursessionOA3UserVO {
    private String ID;
    private String Name;
    private String WorkNo;
    private String DeptID;
    private String DeptName;
    private String DeptFullID;
    private String MobilePhone;
    private String Duties;
    private String ParttimeDeptID;
    private String ParttimeDeptName;
    private String Office;
    private String OfficeId;
    private String OfficeName;
    private String LastLoginIP;
    private String Token;

    public CursessionOA3UserVO(AUserDO user) {
        this.ID = user.getId();
        this.Name = user.getName();
        this.WorkNo = user.getWorkNo();
        this.DeptID = user.getDeptID();
        this.DeptName = user.getDeptName();
        this.DeptFullID = user.getDeptFullID();
        this.MobilePhone = user.getMobilePhone();
        this.Duties = user.getDuties();
        this.ParttimeDeptID = user.getParttimeDeptID();
        this.ParttimeDeptName = user.getParttimeDeptName();
        this.Office = user.getOfficeId();
        this.OfficeId = user.getOfficeId();
        this.OfficeName = user.getOfficeName();
        this.LastLoginIP = user.getLastLoginIP();
    }
}
