package mh.cloud.module.system.service.configmanage;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import mh.cloud.module.system.controller.admin.configmanage.vo.*;
import mh.cloud.module.system.dal.dataobject.configmanage.ConfigmanageDO;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.pojo.PageParam;
import mh.cloud.framework.common.util.object.BeanUtils;

import mh.cloud.module.system.dal.mysql.configmanage.ConfigmanageMapper;

import static mh.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static mh.cloud.module.system.enums.ErrorCodeConstants.*;

/**
 * 系统配置结构树 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ConfigmanageServiceImpl implements ConfigmanageService {

    @Resource
    private ConfigmanageMapper configmanageMapper;

    @Override
    public String createConfigmanage(ConfigmanageSaveReqVO createReqVO) {
        // 插入
        ConfigmanageDO configmanage = BeanUtils.toBean(createReqVO, ConfigmanageDO.class);
        configmanageMapper.insert(configmanage);
        // 返回
        return configmanage.getId();
    }

    @Override
    public void updateConfigmanage(ConfigmanageSaveReqVO updateReqVO) {
        // 校验存在
        validateConfigmanageExists(updateReqVO.getId());
        // 更新
        ConfigmanageDO updateObj = BeanUtils.toBean(updateReqVO, ConfigmanageDO.class);
        configmanageMapper.updateById(updateObj);
    }

    @Override
    public void deleteConfigmanage(String id) {
        // 校验存在
        validateConfigmanageExists(id);
        // 删除
        configmanageMapper.deleteById(id);
    }

    private void validateConfigmanageExists(String id) {
        if (configmanageMapper.selectById(id) == null) {
            throw exception(CONFIGMANAGE_NOT_EXISTS);
        }
    }

    @Override
    public ConfigmanageDO getConfigmanage(String id) {
        return configmanageMapper.selectById(id);
    }

    @Override
    public PageResult<ConfigmanageDO> getConfigmanagePage(ConfigmanagePageReqVO pageReqVO) {
        return configmanageMapper.selectPage(pageReqVO);
    }

}