package mh.cloud.module.system.controller.admin.uiList.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 列表配置新增/修改 Request VO")
@Data
public class ListSaveReqVO {

    @Schema(description = "", requiredMode = Schema.RequiredMode.REQUIRED, example = "15795")
    private String id;

    @Schema(description = "编号")
    private String code;

    @Schema(description = "名称", example = "王五")
    private String name;

    @Schema(description = "数据库连接", example = "张三")
    private String connName;

    @Schema(description = "列表分类", example = "23833")
    private String categoryID;

    @Schema(description = "列表分类名称")
    private String category;

    @Schema(description = "sql语句")
    private String sql;

    @Schema(description = "表名称")
    private String tableNames;

    @Schema(description = "js脚本")
    private String script;

    @Schema(description = "js脚本文本格式")
    private String scriptText;

    @Schema(description = "启用序号")
    private String hasRowNumber;

    @Schema(description = "列表定义", example = "29649")
    private String layoutGrid;

    @Schema(description = "列表字段定义")
    private String layoutField;

    @Schema(description = "查询条件定义")
    private String layoutSearch;

    @Schema(description = "列表按钮定义")
    private String layoutButton;

    @Schema(description = "配置用到的枚举")
    private String enumKeys;

    @Schema(description = "列表属性定义")
    private String settings;

    @Schema(description = "系统生成html")
    private String systemHTML;

    @Schema(description = "", example = "7687")
    private String createUserID;

    @Schema(description = "")
    private String createUser;

    @Schema(description = "")
    private LocalDateTime createTime;

    @Schema(description = "", example = "11211")
    private String modifyUserID;

    @Schema(description = "")
    private String modifyUser;

    @Schema(description = "")
    private LocalDateTime modifyTime;

    @Schema(description = "发布状态")
    private String released;

    @Schema(description = "禁止删除流程")
    private String denyDeleteFlow;

    @Schema(description = "子系统编号")
    private String systemCode;

    @Schema(description = "初始化sql")
    private String initSQL;

    @Schema(description = "", example = "随便")
    private String description;

    @Schema(description = "自定义html")
    private String userHTML;

    @Schema(description = "")
    private String isDeleted;

    @Schema(description = "列表数据权限", example = "29641")
    private String authUserID;

    @Schema(description = "列表数据权限")
    private String authUser;

    @Schema(description = "页面查询前SQL")
    private String beforeSelect;

    @Schema(description = "查询完成后SQL")
    private String afterSelect;

    @Schema(description = "")
    private String preInitScript;

    @Schema(description = "")
    private String listLoadedScript;

    @Schema(description = "")
    private Boolean filterByCurDept;

    @Schema(description = "")
    private String authRole;

    @Schema(description = "", example = "29382")
    private String authRoleID;

    @Schema(description = "设计器列表对象记录ID", example = "7506")
    private String designerID;

}