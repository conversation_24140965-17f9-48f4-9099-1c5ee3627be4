package mh.cloud.module.system.service.publicinformation;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import mh.cloud.module.system.controller.admin.publicinformation.vo.*;
import mh.cloud.module.system.dal.dataobject.publicinformation.PublicinformationDO;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.pojo.PageParam;
import mh.cloud.framework.common.util.object.BeanUtils;

import mh.cloud.module.system.dal.mysql.publicinformation.PublicinformationMapper;

import static mh.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static mh.cloud.module.system.enums.ErrorCodeConstants.*;

/**
 * 院内信息发布 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PublicinformationServiceImpl implements PublicinformationService {

    @Resource
    private PublicinformationMapper publicinformationMapper;

    @Override
    public String createPublicinformation(PublicinformationSaveReqVO createReqVO) {
        // 插入
        PublicinformationDO publicinformation = BeanUtils.toBean(createReqVO, PublicinformationDO.class);
        publicinformationMapper.insert(publicinformation);
        // 返回
        return publicinformation.getId();
    }

    @Override
    public void updatePublicinformation(PublicinformationSaveReqVO updateReqVO) {
        // 校验存在
        validatePublicinformationExists(updateReqVO.getId());
        // 更新
        PublicinformationDO updateObj = BeanUtils.toBean(updateReqVO, PublicinformationDO.class);
        publicinformationMapper.updateById(updateObj);
    }

    @Override
    public void deletePublicinformation(String id) {
        // 校验存在
        validatePublicinformationExists(id);
        // 删除
        publicinformationMapper.deleteById(id);
    }

    private void validatePublicinformationExists(String id) {
        if (publicinformationMapper.selectById(id) == null) {
            throw exception(PUBLICINFORMATION_NOT_EXISTS);
        }
    }

    @Override
    public PublicinformationDO getPublicinformation(String id) {
        return publicinformationMapper.selectById(id);
    }

    @Override
    public PageResult<PublicinformationDO> getPublicinformationPage(PublicinformationPageReqVO pageReqVO) {
        return publicinformationMapper.selectPage(pageReqVO);
    }

}