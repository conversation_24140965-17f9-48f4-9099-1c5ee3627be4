package mh.cloud.module.system.service.group;

import java.util.*;
import jakarta.validation.*;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.module.system.controller.admin.group.vo.*;
import mh.cloud.module.system.dal.dataobject.ares.TreeNode;
import mh.cloud.module.system.dal.dataobject.group.AGroupDO;

/**
 * 系统角色管理 Service 接口
 *
 * <AUTHOR>
 */
public interface AGroupService {

    /**
     * 创建系统角色管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createAGroup(@Valid AGroupSaveReqVO createReqVO);

    /**
     * 更新系统角色管理
     *
     * @param updateReqVO 更新信息
     */
    void updateAGroup(@Valid AGroupSaveReqVO updateReqVO);

    /**
     * 删除系统角色管理
     *
     * @param id 编号
     */
    void deleteAGroup(String id);

    /**
     * 获得系统角色管理
     *
     * @param id 编号
     * @return 系统角色管理
     */
    AGroupDO getAGroup(String id);

    /**
     * 获得系统角色管理列表
     *
     * @param listReqVO 查询条件
     * @return 系统角色管理列表
     */
    List<AGroupDO> getAGroupList(AGroupListReqVO listReqVO);

    List<String> getChildrenDepartIds(String deptID);

    PageResult<AGroupDO> getAGroupPage(AGroupPageReqVO pageReqVO);

    /**
     * 获得已关联到资源的系统角色管理列表
     * @param groupRespVO
     * @return
     */
    PageResult<AGroupDO> getAuthRole(AGroupRespVO groupRespVO);

    /**
     * 删除资源与角色组织关联关系
     *
     * @param groupId 资源ID
     * @param resId 组织角色ID
     */
    void deleteGroupRes(String groupId,String resId);

    /**
     * 获得角色管理树
     *
     * @param id 根节点ID
     * @param groupType 类型，组织或者角色
     * @return 资源管理列表
     */
    List<TreeNode> getGroupTree(String id, String groupType);

    /**
     * 获得角色管理树
     *
     * @param id 角色id
     * @param groupType 类型，组织或者角色
     * @param groupType 角色类型
     * @return 资源管理列表
     */
    List<AGroupDO> getAGroupDataList(String groupType, String type, String id);

    /**
     * 获得已关联到资源的系统角色管理列表
     * @param resId
     * @return
     */
    List<AGroupDO> getAuthRole(String resId);
    /**
     * 保存资源与角色组织关联关系
     * @param resId 资源ID
     * @param groupIds 组织角色ID
     * @param isAuthLower 是否对下级节点授权
     */
    String saveAGroupRes(String resId, String groupIds, Boolean isAuthLower);

    List<String> getResIdsByGroupId(String id);

    void saveGroupRes(SaveGroupResReqVO saveGroupResReqVO);

    List<String> getAllGroupIdsByDeptId(String deptID);
}