package mh.cloud.module.system.controller.admin.portal.V0;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

@TableName("system_layout_group")
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysLayoutGroupVo {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    @TableField(value = "data_id")
    private String dataId;

    private String groupCode;

    private String defaultLayout;

    @TableField(exist = false)
    private String startUse;

}
