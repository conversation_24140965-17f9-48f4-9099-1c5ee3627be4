package mh.cloud.module.system.controller.admin.ares.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import com.alibaba.excel.annotation.*;
import mh.cloud.framework.excel.core.annotations.DictFormat;
import mh.cloud.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 资源管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AResRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21013")
    @ExcelProperty("ID")
    private String id;

    @Schema(description = "父ID", example = "6956")
    @ExcelProperty("父ID")
    private String parentID;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "luohang")
    @ExcelProperty("名称")
    private String name;

    @Schema(description = "编码")
    @ExcelProperty("编码")
    private String code;

    @Schema(description = "一致显示", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("一致显示")
    private Boolean alwaysShow;

    @Schema(description = "权限")
    @ExcelProperty("权限")
    private String permission;

    @Schema(description = "路径")
    @ExcelProperty("路径")
    private String path;

    @Schema(description = "图标")
    @ExcelProperty("图标")
    private String icon;

    @Schema(description = "组件")
    @ExcelProperty("组件")
    private String component;

    @Schema(description = "组件名称", example = "张三")
    @ExcelProperty("组件名称")
    private String componentName;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("状态")
    private Short status;

    @Schema(description = "是否可见", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否可见")
    private Boolean visible;

    @Schema(description = "存活时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("存活时间")
    private Boolean keepAlive;

}