package mh.cloud.module.system.controller.admin.logger;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.module.system.controller.admin.logger.vo.searchLog.SearchLogCreateReqVO;
import mh.cloud.module.system.service.logger.SearchLogRecordService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import static mh.cloud.framework.common.pojo.CommonResult.success;

/**
 * @Author: ytq
 * @Date: 2025/3/14 14:30
 * @Description: TOOD 描述
 */
@Tag(name = "管理后台 - 搜索日志")
@RestController
@RequestMapping("/system/search-log")
@Validated
public class SearchLogRecordController {

    @Resource
    private SearchLogRecordService searchLogService;
    // 添加搜索日志

    @PostMapping("/addSearchLog")
    @Operation(summary = "添加搜索日志 Excel")
    @PreAuthorize("@ss.hasPermission('system:search-log:addSearchLog')")
    public CommonResult<Boolean> createSearchLog(Map<String, Object> searchLog) {
        searchLogService.createSearchLog(searchLog);
        return success(true);
    }
}
