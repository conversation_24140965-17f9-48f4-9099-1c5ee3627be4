package mh.cloud.module.system.controller.admin.otherjobrelation.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 兼职关系新增/修改 Request VO")
@Data
public class OtherjobRelationSaveReqVO {

    @Schema(description = "groupid", example = "29745")
    private String parentGroupID;

    @Schema(description = "用户id", example = "23989")
    private String childGroupID;

    @Schema(description = "全路径id", example = "15065")
    private String deptFullID;

    @Schema(description = "关系类型", example = "1")
    private String relationType;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31559")
    private String id;

}