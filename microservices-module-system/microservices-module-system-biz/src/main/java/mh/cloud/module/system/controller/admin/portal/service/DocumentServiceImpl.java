package mh.cloud.module.system.controller.admin.portal.service;


import mh.cloud.framework.security.core.util.SecurityFrameworkUtils;
import mh.cloud.module.system.controller.admin.portal.V0.SysDocument;
import mh.cloud.module.system.service.db.SQLHelper;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.UUID;

@Service
public class DocumentServiceImpl implements DocumentService {
    @Override
    public Map<String, Object> selectDocList(String routeName) {
        SQLHelper sqlHelper = null;
        try {
            sqlHelper = SQLHelper.createSqlHelper("Core");
            String sql = "SELECT doc_id,doc_name,doc_content FROM system_document where route_name =?";
            return sqlHelper.selectFirstRow(sql, routeName);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (sqlHelper != null) {
                sqlHelper.close();
            }
        }
        return null;
    }

    @Override
    public Integer updateDoc(SysDocument sysDocument) {
        SQLHelper sqlHelper = null;
        try {
            sqlHelper = SQLHelper.createSqlHelper("Core");
            String selectSql = "select route_name from system_document where route_name =?";
            Map<String, Object> map = sqlHelper.selectFirstRow(selectSql, sysDocument.getRouteName());
            int row;
            String nickname = SecurityFrameworkUtils.getLoginUserNickname();
            if (map == null) {
                //新增
                String id = UUID.randomUUID().toString();
                String insertSql = "INSERT INTO system_document(doc_id,route_name,doc_name,doc_content,creator) VALUES(?,?,?,?,?)";
                row = sqlHelper.executeNonQuery(insertSql, id, sysDocument.getRouteName(), sysDocument.getDocName(), sysDocument.getDocContent(), nickname);
            } else {
                //更新
                String updateSql = "UPDATE system_document SET doc_content =?,updater=? WHERE route_name =?";
                row = sqlHelper.executeNonQuery(updateSql, sysDocument.getDocContent(), nickname, sysDocument.getRouteName());
            }
            return row;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (sqlHelper != null) {
                sqlHelper.close();
            }
        }
        return null;
    }
}
