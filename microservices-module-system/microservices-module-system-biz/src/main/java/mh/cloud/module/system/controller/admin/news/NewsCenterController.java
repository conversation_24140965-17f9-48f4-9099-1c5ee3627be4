package mh.cloud.module.system.controller.admin.news;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import mh.cloud.framework.security.core.util.SecurityFrameworkUtils;
import mh.cloud.module.system.controller.admin.news.VO.Detail;
import mh.cloud.module.system.controller.admin.news.service.NewsService;
import mh.cloud.module.system.dal.dataobject.auser.AUserDO;
import mh.cloud.module.system.enums.SqlType;
import mh.cloud.module.system.service.auser.AUserService;
import mh.cloud.module.system.service.db.SQLHelper;
import mh.cloud.module.system.util.PatternMatchUtil;
import org.springframework.scheduling.annotation.Async;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static mh.cloud.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "资讯中心列表")
@RestController
@RequestMapping("/Portal")
@Validated
@Slf4j
public class NewsCenterController {
    @Resource
    private NewsService newsService;
    @Resource
    private AUserService aUserService;

    @Resource
    private PatternMatchUtil patternMatchUtil;


    @PostMapping("/WorkCenter/NewsDetail")
    @Operation(summary = "资讯中心详情页")
    public Map<String, Object> NewsDetail(@RequestParam("id") String id,
                                          @RequestParam(name="Code",required = false) String code,
                                          @RequestParam(name="code",required = false) String code2 ,
                                          @RequestParam("navigation") String navigation,
                                          @RequestParam(name = "pageIndex", defaultValue = "1") int pageIndex,
                                          @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                          HttpServletRequest request) {

        String authorization = request.getHeader("Authorization");
        String token = authorization.replace("Bearer", "").trim();

        if(code == null && code2 == null){
            return null;
        }
        if(code==null){
            code = code2;
        }
        SQLHelper core = SQLHelper.CreateSqlHelper("Core");
        try {


            HashMap<String, Object> ret = new HashMap<>();
            //源代码翻译
            String scriptSql = newsService.newsCenterTypeSql(code, id, null, SqlType.DetailSql);
            log.info("资讯中心详情页sql：{}", scriptSql);
            scriptSql = "select ID,Title,Attachments,BigFl,Content,ReleaseDate AS CreateTime,(select  DeptName   from  KMYZH_SystemDataBase.dbo.a_user where ID = CAST(EnteredBy as varchar(8000))) as UnitName,\n" +
                    "(select  Name  from  KMYZH_SystemDataBase.dbo.a_user where ID = CAST(EnteredBy as varchar(8000))) as SenderName,ReadCount,Isdelete,LK,WJDate\n" +
                    "from   KMYZH_SystemDataBase.dbo.I_PublicInformation\n" +
                    "where   ID='" + id + "' and  (Isdelete IS NULL OR Isdelete <> '1') ";
            List<Map<String, Object>> maps = core.executeReader(scriptSql);
            Detail detail = new Detail();
            if (maps != null && !maps.isEmpty()) {
                Map<String, Object> map = maps.get(0);
                // 使用三目运算符进行非空判断
                detail.setID(map.get("ID") != null ? map.get("ID").toString() : "");
                detail.setTitle(map.get("Title") != null ? map.get("Title").toString() : "");
                detail.setContent(map.get("Content") != null ? map.get("Content").toString() : "");
                detail.setCreateTime(map.get("CreateTime") != null ? map.get("CreateTime").toString() : "");
                detail.setUnitName(map.get("UnitName") != null ? map.get("UnitName").toString() : "");
                detail.setSenderName(map.get("SenderName") != null ? map.get("SenderName").toString() : "");
                detail.setReadCount(map.get("ReadCount") != null ? map.get("ReadCount").toString() : "");
                detail.setAttachments(map.get("Attachments") != null ? map.get("Attachments").toString() : "");
                detail.setBigFl(map.get("BigFl") != null ? map.get("BigFl").toString() : "");

                String lks = map.get("LK") != null ? map.get("LK").toString() : "";
                if (map.get("LK") != null) {
                    String[] lkss = lks.split("\n", -1);
                    StringBuffer sb=new StringBuffer();
                    for (String lk : lkss) {
                        sb.append("<li>" + lk + "</li>");
                    }
                    detail.setLK(sb.toString());
                }
                detail.setWJDate(String.valueOf(map.get("WJDate")) == null ? "" : String.valueOf(map.get("WJDate")));
                //处理附件
                setAttachments(detail);
                //内容路径处理
                String content = detail.getContent().replaceAll("&nbsp;", "");
//                String hrefPattern = "<a\\b[^<>]*?\\bhref[\\s\\t\\r\\n]*=[\\s\\t\\r\\n]*['\"]?[\\s\\t\\r\\n]*(?<imgUrl>[^\\s\\t\\r\\n'\"]*?)[^<>]*?/?[\\s\\t\\r\\n]*>";
                String hrefPattern = "<a\\b[^<>]*?\\bhref\\s*=?\\s*['\"]?\\s*(?<imgUrl>[^\\s'\"><>]*)[^<>]*?/?\\s*>";
                content = patternMatchUtil.replaceHref(content, hrefPattern,token);
                content = patternMatchUtil.updateImageUrls(content);
                content = content.replace("`","");
                detail.setContent(content);

                //上一篇下一篇
                String listSql = newsService.newsCenterTypeSql(code, null, null, SqlType.ListSql);
                String proviousSql = " SELECT t.* FROM ( " + listSql + " ) t WHERE RID = (SELECT t.RID-1 FROM ( " + listSql + " ) t where id='" + id + "')";
                List<Map<String, Object>> proviousDt = core.selectRows(proviousSql);
                ret.put("ProviousLocation", IterUtil.isNotEmpty(proviousDt) ? proviousDt.size() : "");

                String nextSql = "SELECT t.* FROM (" + listSql + ") t WHERE RID = (SELECT t.RID+1 FROM (" + listSql + ") t where id='" + id + "')";
                List<Map<String, Object>> nextDt = core.selectRows(nextSql);
                ret.put("NextLocation", IterUtil.isNotEmpty(nextDt) ? nextDt.size() : "");

                for (Map<String, Object> dt : proviousDt) {
                    String newsId = dt.get("ID") != null ? dt.get("ID").toString() : "";
                    String newsTitle = dt.get("Title") != null ? dt.get("Title").toString() : "";
                    String previous = "<a href='NewsDetail?ID=" + newsId + "&Code=" + code + "&navigation=" + navigation + "' style = 'font-size:16px;font-family:宋体;'>上一篇：" + newsTitle + "</a>";
                    ret.put("Previous", previous);
                }
                for (Map<String, Object> dt : nextDt) {

                    String newsId = dt.get("ID") != null ? dt.get("ID").toString() : "";
                    String newsTitle = dt.get("Title") != null ? dt.get("Title").toString() : "";
                    String next = "<a href='NewsDetail?ID=" + newsId + "&Code=" + code + "&navigation=" + navigation + "' style = 'font-size:16px;font-family:宋体;'>下一篇：" + newsTitle + "</a>";
                    ret.put("Next", next);
                }
                //获取当前用户
                AUserDO aUser = aUserService.getAUser(getLoginUserId());
                updateViewCountAndRecord(code, id, aUser, navigation);
            }
            ret.put("detail", detail);
            ret.put("Lable", navigation);
            ret.put("Code", code);
            return ret;
        } finally {
            core.close();
        }
    }


    private static void setAttachments(Detail detail) {
        try {
            SQLHelper centerNet = SQLHelper.CreateSqlHelper("CenterNet");
            if (detail.getBigFl().equals("中心网") && !detail.getContent().contains("/PageOffice/Viewpage.aspx")) {
                String attachmentsSql = "SELECT [ID] ,[AttachmentName] ,[AttachmentUrl] ,[MainMsgID] FROM [XxzxWork].[dbo].[XxzxMainMsg_Attachments] WHERE MainMsgID='" + detail.getID() + "'";
                List<Map<String, Object>> attachments = centerNet.selectRows(attachmentsSql);
                StringBuilder attachmentsStringBuilder = new StringBuilder();// attachmentsString = null;
                if (attachments != null && attachments.size() > 0) {
                    StringBuilder sb = new StringBuilder();
                    for (Map<String, Object> attachment : attachments) {
                        //三目运算加非空判断
                        String attach = attachment.get("AttachmentUrl") != null ? attachment.get("AttachmentUrl").toString() : "";
                        attachmentsStringBuilder.append(attach);
                        if (attach != null && !attach.trim().isEmpty()) {
                            attachmentsStringBuilder.append(",");
                        }
                    }
                    detail.setAttachments(attachmentsStringBuilder.toString());
                }
            }
            centerNet.close();
        } catch (Exception e) {
            log.error("获取附件失败", e);
        }
    }

    @Async
    protected void updateViewCountAndRecord(String code, String id, AUserDO user, String navigation) {

        try (SQLHelper core = SQLHelper.CreateSqlHelper("Core"); SQLHelper oa = SQLHelper.CreateSqlHelper("OA")) {


            String viewCountSql = newsService.newsCenterTypeSql(code, id, null, SqlType.ViewCountSql);
            core.executeNonQuery(viewCountSql);
            String viewSql = "INSERT INTO [dbo].[T_Page_NewsCenterViewRecord] " +
                    "([ID],[DeptID] ,[DeptName] ,[WorkNo] ,[Name] ,[Duties] ,[CategoryName] ,[Code] ,[NewsID],[CreateTime]) " +
                    "VALUES(?,?,?,?,?,?,?,?,?,?)";
            // 执行viewSql
            oa.executeNonQuery(viewSql,
                    UUID.randomUUID().toString(),
                    user.getDeptID(),
                    user.getDeptName(),
                    user.getWorkNo(),
                    user.getName(),
                    user.getDuties(),
                    navigation,
                    code,
                    id,
                    new Timestamp(System.currentTimeMillis()));
        }
    }

    @PostMapping("/WorkCenter/NewsList")
    @Operation(summary = "资讯中心 - 首页文章列表")
    public Object NewsList(String code, int pageSize) {
        Page<Map<String, Object>> mapPage = newsService.exceNewsSql("SELECT ListSql FROM T_Page_NewsCenterMenu WHERE code='" + code + "'", pageSize, 1);
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();
        String token = SecurityFrameworkUtils.obtainAuthorization(request, "Authorization", "token");
        mapPage.getRecords().forEach(item -> {
            if (item.get("PicFile") != null) {
                String content = item.get("PicFile").toString()+"&token="+token;
                item.put("PicFile", content);
            }
        });
        return mapPage;

    }

    @PostMapping("/WorkCenter/Links")
    @Operation(summary = "快速入口&友情链接")
    public Object Links(String type, int page, int pageSize) {
        String sql = "select  ROW_NUMBER() OVER(ORDER BY Cast(Nv200_4 as int) DESC) AS RID,ID,Nv200_1 as Name,Nv200_2 as  Url  from T_New_URLInfo where (IsDeleted<>'1' or IsDeleted is null) and  Nv200_3 = '" + type + "'";
        SQLHelper loghelper = SQLHelper.CreateSqlHelper("Log");
        var dt = loghelper.ExecuteDataTable(sql, page, pageSize);
        return dt;

    }

    @PostMapping("/WorkCenter/NewsListChildren")
    @Operation(summary = "资讯中心 - 首页文章列表 - 子表内容列表")
    public Object Links(String code, int pageSize) {
        Page<Map<String, Object>> mapPage = newsService.exceNewsSql("SELECT ListSql FROM T_Page_NewsCenterMenu_NewsCenterMenuSub WHERE code='" + code + "'", pageSize, 1);
        return mapPage;

    }

    @PostMapping("/NewsCenter/TechnologicalInnovation")
    @Operation(summary = "技术创新中心")
    public Object TechnologicalInnovation(int pageSize) {
        try (
                SQLHelper oa = SQLHelper.createSqlHelper("OA")
        ) {
            String strsql = "select CatalogName,ListSql,Code,ID from T_Page_NewsCenterMenu_NewsCenterMenuSub where T_Page_NewsCenterMenuID = 'ad5000ac-2812-4b7a-a675-335c29b8e712'";
            List<Map<String, Object>> maps = oa.selectRows(strsql);
            for (Map<String, Object> map : maps) {
                String listSql = map.get("ListSql") == null ? "" : map.get("ListSql").toString();
                if (StrUtil.isNotEmpty(listSql)) {
                    Page<Map<String, Object>> page = oa.page(listSql, 1, pageSize);
                    map.put("list", page.getRecords());
                }
            }
            return maps;
        }
    }

    //NewsCenter/GetZXLeftMenu
    @PostMapping("/NewsCenter/GetZXLeftMenu")
    @Operation(summary = "资讯中心左侧菜单")
    public Object getZXLeftMenu() {
        //var user = UserService.CurrentUser;
        //code = HttpUtility.UrlDecode(code);
        try (SQLHelper oa = SQLHelper.CreateSqlHelper("OA")) {
            var sql = "SELECT * FROM (SELECT  'Parent' AS [Type] ,[ID] ,'ParentID' AS ParentID ,CatalogName ,SortIndex ,[IsEnabled],[Code] FROM [T_Page_NewsCenterMenu] WHERE (IsDeleted <> '1' or IsDeleted is null) and IsEnabled = 1\n" +
                    "        UNION ALL\n" +
                    "        SELECT 'child' AS[Type] ,[ID] ,[T_Page_NewsCenterMenuID]AS ParentID, CatalogName, SortIndex,[IsEnabled],[Code]\n" +
                    "        FROM[T_Page_NewsCenterMenu_NewsCenterMenuSub] WHERE IsEnabled = 1\n" +
                    "\t\t\t\t\t)T\n" +
                    "        ORDER BY CAST(SortIndex AS int)ASC ";

            List<Map<String, Object>> maps = oa.selectRows(sql);
            return maps;
        }

    }

    @PostMapping("/NewsCenter/GetPublicInformData")
    @Operation(summary = "资讯中心新闻列表")
    public Object getPublicInformData(String code, int page, int pageSize, @RequestParam(name = "searchContent", required = false) String searchContent) {

//        code = HttpUtility.UrlDecode(code);
//        searchContent = HttpUtility.UrlDecode(searchContent);
        try (SQLHelper core = SQLHelper.createSqlHelper("Core")) {
            String s = newsService.newsCenterTypeSql(code, null, searchContent, SqlType.ListSql);
//            page(String sql, int startRowNum, int maxRowNum, Object... params)
            Page<Map<String, Object>> pages = core.page(s, page, pageSize, null);
            return pages;
        }
    }
   @PostMapping("/NewsCenter/GetPublicInfoCount")
    @Operation(summary = "数量统计")
    public Object GetPublicInfoCount(String code, @RequestParam(name = "searchContent", required = false) String searchContent) {

//        code = HttpUtility.UrlDecode(code);
//        searchContent = HttpUtility.UrlDecode(searchContent);
        try (SQLHelper core = SQLHelper.createSqlHelper("Core")) {
            String s = newsService.newsCenterTypeSql(code, null, searchContent, SqlType.ListSql);
//            page(String sql, int startRowNum, int maxRowNum, Object... params)
            Page<Map<String, Object>> pages = core.page(s, 1, 10, null);
            return pages.getTotal();
        }
    }


}
