package mh.cloud.module.system.controller.admin.portal.V0;

import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
@Data
public class ProjectTree {
    public ProjectTree(){
        super();
    }
    public ProjectTree(Integer auth, String FullId, String Icon, String Id, String Name, String ParentId, String ResId, String ResName, String SortIndex, String Url) {
        this.FullId = FullId;
        this.Icon = Icon;
        this.Id = Id;
        this.Name = Name;
        this.ParentId = ParentId;
        this.ResId = ResId;
        this.ResName = ResName;
        this.SortIndex = SortIndex;
        this.Url = Url;
        this.auth = auth;
    }

    private String FullId;
    private String Icon;
    private String Id;
    private String Name;
    private String ParentId;
    private String ResId;
    private String ResName;
    private String SortIndex;
    private String Url;
    private Integer auth;
    private List<ProjectTree> children = new ArrayList<>();

    public void addChild(ProjectTree child) {
        child.setParentId(this.Id);
        children.add(child);
    }

    public static List<ProjectTree> buildTree(List<ProjectTree> nodes) {
        Map<String, ProjectTree> nodeMap = new HashMap<>();

        for (ProjectTree node : nodes) {
            nodeMap.put(node.getId(), node);
        }
        List<ProjectTree> roots = new ArrayList<>();

        for (ProjectTree node : nodes) {
            if (node.getParentId() == null) {
                roots.add(node);
            } else {
                ProjectTree parentNode = nodeMap.get(node.getParentId());
                if (parentNode != null) {
                    parentNode.addChild(node);
                }
            }
        }

        return roots;
    }

    public String getParentId() {
        return this.ParentId;
    }
}
