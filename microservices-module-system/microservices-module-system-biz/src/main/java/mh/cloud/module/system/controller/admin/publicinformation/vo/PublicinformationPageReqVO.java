package mh.cloud.module.system.controller.admin.publicinformation.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import mh.cloud.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static mh.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 院内信息发布分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public  class PublicinformationPageReqVO extends PageParam {

    @Schema(description = "类别", example = "27068")
    private String catalogId;

    @Schema(description = "新闻主题")
    private String title;

    @Schema(description = "")
    private String content;

    @Schema(description = "")
    private String contentText;

    @Schema(description = "相关附件")
    private String attachments;

    @Schema(description = "", example = "2629")
    private String receiveDeptId;

    @Schema(description = "", example = "张三")
    private String receiveDeptName;

    @Schema(description = "", example = "10085")
    private String receiveUserId;

    @Schema(description = "", example = "张三")
    private String receiveUserName;

    @Schema(description = "", example = "2355")
    private String deptDoorId;

    @Schema(description = "", example = "luohang")
    private String deptDoorName;

    @Schema(description = "")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] expiresTime;

    @Schema(description = "", example = "13745")
    private Integer readCount;

    @Schema(description = "重要度 1重要，0一般")
    private String important;

    @Schema(description = "紧急度 1重要，0一般")
    private String urgency;

    @Schema(description = "置顶排序")
    private String isTop;

    @Schema(description = "CreateTime")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "CreateUser")
    private String createUser;

    @Schema(description = "CreateUserID", example = "10085")
    private String createUserID;

    @Schema(description = "ModifyUser")
    private String modifyUser;

    @Schema(description = "ModifyUserID", example = "20848")
    private String modifyUserID;

    @Schema(description = "ModifyTime")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] modifyTime;

    @Schema(description = "子系统编号")
    private String systemCode;

    @Schema(description = "IsSendMobile")
    private String isSendMobile;

    @Schema(description = "PicFile")
    private String picFile;

    @Schema(description = "流程状态")
    private String flowPhase;

    @Schema(description = "流程环节")
    private String flowStep;

    @Schema(description = "流程结束日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] flowCompleteTime;

    @Schema(description = "流程执行人")
    private String flowHandler;

    @Schema(description = "流程执行人ID", example = "31991")
    private String flowHandlerID;

    @Schema(description = "信息类型", example = "张三")
    private String catalogName;

    @Schema(description = "发布部门ID")
    private String publishDept;

    @Schema(description = "发布部门Name", example = "赵六")
    private String publishDeptName;

    @Schema(description = "发布状态")
    private String publishState;

    @Schema(description = "副标题")
    private String deputyTitle;

    @Schema(description = "新闻摘要")
    private String abStract;

    @Schema(description = "信息大类", example = "2")
    private String newsType;

    @Schema(description = "IsExtranet")
    private String isExtranet;

    @Schema(description = "EditorCharge")
    private String editorCharge;

    @Schema(description = "IsImage")
    private String isImage;

    @Schema(description = "ReleaseScope")
    private String releaseScope;

    @Schema(description = "WordsAuthor")
    private String wordsAuthor;

    @Schema(description = "图片作者")
    private String imageAuthor;

    @Schema(description = "发布时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] releaseDate;

    @Schema(description = "Source")
    private String source;

    @Schema(description = "关键字")
    private String keyWords;

    @Schema(description = "ReleaseScopeName", example = "王五")
    private String releaseScopeName;

    @Schema(description = "IsPic")
    private String isPic;

    @Schema(description = "ExtranetType", example = "2")
    private String extranetType;

    @Schema(description = "所在部门")
    private String releaseCompany;

    @Schema(description = "报送人")
    private String enteredBy;

    @Schema(description = "EnteredBy")
    private String videFile;

    @Schema(description = "所在部门名称", example = "李四")
    private String releaseCompanyName;

    @Schema(description = "报送人名称", example = "张三")
    private String enteredByName;

    @Schema(description = "CatalogIDName", example = "luohang")
    private String catalogIDName;

    @Schema(description = "MGCCX")
    private String mgccx;

    @Schema(description = "PicFileMINI")
    private String picFileMINI;

    @Schema(description = "TopNum")
    private Integer topNum;

    @Schema(description = "第二作者")
    private String wordsAuthor2;

    @Schema(description = "第三作者")
    private String wordsAuthor3;

    @Schema(description = "PublishUser")
    private String publishUser;

    @Schema(description = "PublishUserName", example = "张三")
    private String publishUserName;

    @Schema(description = "PublishUnit")
    private String publishUnit;

    @Schema(description = "PublishUnitName", example = "赵六")
    private String publishUnitName;

    @Schema(description = "Isdelete")
    private String isdelete;

    @Schema(description = "")
    private LocalDateTime cjrq;

    @Schema(description = "")
    private String otherWebAddres;

    @Schema(description = "第一作者名称", example = "luohang")
    private String authorName;

    @Schema(description = "发布时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] sendTime;

    @Schema(description = "信息类型")
    private String xxlx;

    @Schema(description = "首页图片", example = "https://www.iocoder.cn")
    private String firstImageUrl;

    @Schema(description = "第一作者")
    private String author;

    @Schema(description = "第二作者名称", example = "王五")
    private String wordsAuthor2Name;

    @Schema(description = "第三作者名称", example = "赵六")
    private String wordsAuthor3Name;

    @Schema(description = "图片作者名称", example = "李四")
    private String imageAuthorName;

    @Schema(description = "第一作者名称", example = "赵六")
    private String authorNameName;

    @Schema(description = "")
    private String bigFl;

    @Schema(description = "置顶结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] isTopEndTime;

    @Schema(description = "外键值")
    private String outKey;

    @Schema(description = "外键类型值", example = "1")
    private String outType;

    @Schema(description = "文件底稿")
    private String firstFile;

    @Schema(description = "文件终稿")
    private String endFile;

    @Schema(description = "落款")
    private String lk;

    @Schema(description = "正文内容")
    private String contentZW;

    @Schema(description = "文件日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] wJDate;

    @Schema(description = "是否行政")
    private String isXZ;

}