package mh.cloud.module.system.controller.admin.portal;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import mh.cloud.module.system.controller.admin.portal.service.MsgService;
import mh.cloud.module.system.dal.dataobject.auser.AUserDO;
import mh.cloud.module.system.dal.db.QueryModel;
import mh.cloud.module.system.service.auser.AUserService;
import mh.cloud.module.system.service.db.SQLHelper;
import mh.cloud.module.system.service.db.VO.GridData;
import org.dromara.hutool.core.text.StrUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import static java.time.LocalDate.now;
import static mh.cloud.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

@Tag(name = "消息接口")
@RestController
@RequestMapping("/BasicApplication/InfoManage/Msg")
@Validated
@Slf4j
public class MsgController {
    @Resource
    private MsgService msgService;
    @Resource
    private AUserService aUserService;

    @PostMapping("/GetReceiveReadList")
    @Operation(summary = "获取已读消息详情")
    public GridData GetReceiveReadList(QueryModel qb) {
        String id = qb.getID();
        if (StrUtil.isNotEmpty(id))
        {
            String sql = "select ID, MsgBodyID, UserID, UserName, FirstViewTime, ReplyTime, IsDeleted, DeleteTime, AlreadyRead, AlreadyReply from V_MsgReceiveReadList where MsgBodyID='{0}'";
            sql = SQLHelper.format(sql, id);
            try(SQLHelper core = SQLHelper.CreateSqlHelper("Core")){
                List<Map<String, Object>> maps = core.selectRows(sql);
                GridData gridData = new GridData(maps.size(), maps);
                return gridData;
            }
        }
        return null;
    }
    @PostMapping("/GetReceiveModel")
    @Operation(summary = "获取消息详情GetReceiveModel")
    public Map<String, Object> GetReceiveModel(QueryModel qb) {
        try(SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core");){
            String id = qb.getID();
            String forwardID = qb.getForwardID();
            String replyID = qb.getReplyID();
            AUserDO aUser = aUserService.getAUser(getLoginUserId());
            String orgUser=StrUtil.isEmptyIfStr(aUser.getDeptName())?aUser.getName():aUser.getDeptName()+"&nbsp;&nbsp;"+aUser.getName();
            if(StringUtil.isNotEmpty(forwardID)){
                Map<String, Object> msg = sqlHelper.selectFirstRow("select * from  I_MsgBody where ID = ?", forwardID);
                msg.put("ID", "");
                msg.put("ParentID", forwardID);
                msg.put("Title", "转发：" + msg.get("Title"));
                msg.put("Content", "<p>&nbsp;</p><p></p><p></p><p></p><p>" + orgUser + "</p><hr />" + msg.get("Content"));
                msg.put("IsReadReceipt", "0");
                msg.put("ReceiverIDs", "");
                msg.put("ReceiverNames", "");
                return msg;
            }else if(StringUtil.isNotEmpty(replyID)){
                Map<String, Object> msg = sqlHelper.selectFirstRow("select * from  I_MsgBody where ID = ?", replyID);
                msg.put("ID", "");
                msg.put("ParentID", replyID);
                msg.put("ReceiverIDs", msg.get("SenderID"));
                msg.put("ReceiverNames", msg.get("SenderName"));
                msg.put("Title", "回复：" + msg.get("Title"));
                msg.put("Content", "<p>&nbsp;</p><p></p><p></p><p></p><p>" + orgUser + "</p><hr />" + msg.get("Content"));
                msg.put("IsReadReceipt", "0");
                return msg;
            }else{
                String userID = aUser.getId();
                Map<String, Object> receiver = sqlHelper.selectFirstRow("select * from \"I_MsgReceiver\" imr  where MsgBodyID=? and UserID=? and FirstViewTime is null", id, userID);

                if (receiver != null) {
                    receiver.put("FirstViewTime", now());
                    Map<String, Object> msg = sqlHelper.selectFirstRow("select * from  I_MsgBody where ID = ?", id);
                    if(!msg.isEmpty()){
                        if ("1".equals(msg.get("IsReadReceipt") != null ? msg.get("IsReadReceipt").toString() : "0")){
                            String content = "<p style=\"text-align:left;text-indent:-72pt;margin-left:72pt;\" align=\"left\"><span style=\"font-family:宋体;\">消息</span></p>"
                                    + "<p style=\"text-align:left;text-indent:-72pt;margin-left:72pt;\" align=\"left\"><span style=\"font-family:宋体;\"></span></p>"
                                    + "<p style=\"text-align:left;text-indent:-72pt;margin-left:72pt;\" align=\"left\"><span style=\"font-family:宋体;\"><span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>接收人:<span> </span>{0}</span></p>"
                                    + "<p style=\"text-align:left;text-indent:-72pt;margin-left:72pt;\" align=\"left\"><span style=\"font-family:宋体;\"><span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>标题:<span>&nbsp;&nbsp; </span>{1}</span></p>"
                                    + "<p style=\"text-align:left;text-indent:-72pt;margin-left:72pt;\" align=\"left\"><span style=\"font-family:宋体;\"><span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>发送时间:<span> </span>{2}</span></p>"
                                    + "<p style=\"text-align:left;\" align=\"left\"><span style=\"font-family:宋体;\"></span></p>"
                                    + "<p style=\"text-align:left;\" align=\"left\"><span style=\"font-family:宋体;\">阅读时间为 {3}</span></p>";
                            String format = SQLHelper.format(content, receiver.get("UserName"), receiver.get("Title"), receiver.get("SendTime"), receiver.get("SendTime"));
//                            msgService.sendMsg(receiver.get("SenderID").toString(), receiver.get("SenderName").toString(), receiver.get("UserID").toString(), receiver.get("UserName").toString(), receiver.get("Title").toString(), format, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0");
                        }
                    }


                }
            }
        }



        return null;
    }
}
