package mh.cloud.module.system.controller.admin.portal.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fhs.common.utils.StringUtil;
import jakarta.annotation.Resource;
import mh.cloud.framework.common.exception.ServiceException;
import mh.cloud.module.system.controller.admin.portal.V0.*;
import mh.cloud.module.system.dal.dataobject.auser.AUserDO;
import mh.cloud.module.system.service.auser.AUserService;
import mh.cloud.module.system.service.db.SQLHelper;
import org.springframework.stereotype.Service;

import javax.print.DocFlavor;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static mh.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static mh.cloud.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static mh.cloud.module.system.enums.ErrorCodeConstants.AUTH_LOGIN_CAPTCHA_CODE_ERROR;
import static mh.cloud.module.system.enums.ErrorCodeConstants.NOTIFY_SEND_TEMPLATE_PARAM_LENGTH;


@Service
public class InfoManageServiceImpl implements InfoManageService {
    @Resource
    private AUserService userService;
    @Resource
    private MsgService msgService;

    @Override
    public Page<Map<String, Object>> getReceiveList(MsgReq msgReq) {
        String userId = getLoginUserId();
        SQLHelper sqlHelper = null;
        Page<Map<String, Object>> maps = null;
        try {
            sqlHelper = SQLHelper.createSqlHelper("Core");
            String msgCountSql = "select \n" +
                    "ID,ReceiverID,ParentID,Type,Title,ContentText,AttachFileIDs,LinkUrl,IsSystemMsg,SendTime,SenderID,SenderName,ReceiverIDs,ReceiverNames,AlreadyRead,Importance,UserID,FirstViewTime,IsDeleted,IsRemind,Content\n" +
                    "from V_MsgReceiveList \n" +
                    "where \n" +
                    "UserID= '{0}'\n" +
                    "AND FirstViewTime is null \n" +
                    "and AttachFileIDs is not null ORDER BY SendTime DESC";
            maps = sqlHelper.ExecuteDataTable(SQLHelper.format(msgCountSql, userId), msgReq.getPageNo(), msgReq.getPageSize());
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            sqlHelper.close();
        }
        return maps;
    }

    @Override
    public void SetMsgRed(String ids) {
        SQLHelper sqlHelper = null;
        try {
            //获取当前时间
            LocalDateTime now = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
            String nowDate = now.format(formatter);
            String stringIds = Arrays.stream(ids.split(","))
                    .map(id -> quote(id))
                    .collect(Collectors.joining(","));
            sqlHelper = SQLHelper.createSqlHelper("Core");
            String sql = "UPDATE I_MsgReceiver SET FirstViewTime = {0} WHERE ID IN({1})";
            sqlHelper.executeNonQuery(SQLHelper.format(sql, quote(nowDate),stringIds));
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            sqlHelper.close();
        }
    }
    private String quote(Object value) {
        return "'" + value.toString() + "'";
    }

    @Override
    public void DeleteMsg(String ids) {
        SQLHelper sqlHelper = null;
        try {
            //获取当前时间
            LocalDateTime now = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
            String nowDate = now.format(formatter);
            String stringIds = Arrays.stream(ids.split(","))
                    .map(id -> quote(id))
                    .collect(Collectors.joining(","));
            sqlHelper = SQLHelper.createSqlHelper("Core");
            String sql = "UPDATE I_MsgReceiver SET IsDeleted = 1, DeleteTime = {0}  WHERE ID IN({1})";
            sqlHelper.executeNonQuery(SQLHelper.format(sql, quote(nowDate),stringIds));
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            sqlHelper.close();
        }
    }

    @Override
    public List<Map<String, Object>> getNormalLinkManTree(OrgMsgReq orgMsgReq) {
        SQLHelper sqlHelper = null;
        List<Map<String, Object>> maps= null;
        String userId = getLoginUserId();
        try {
            sqlHelper = SQLHelper.createSqlHelper("Core");
            String sql = "select A_User.ID,A_User.Name,A_User.Name as UserName,A_User.DeptName,A_User.DeptID,A_User.WorkNo,case when GroupID='' or GroupID is null then 'Root' else GroupID end as ParentID,A_UserLinkMan.SortIndex,'User' as NodeType from A_UserLinkMan Join A_User on A_UserLinkMan.LinkManID=A_User.ID where A_UserLinkMan.UserID='{0}' Order By A_UserLinkMan.SortIndex ASC";
            maps = sqlHelper.selectRows(SQLHelper.format(sql,userId));
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            sqlHelper.close();
        }
        return maps;
    }

    @Override
    public List<OrgTree> getOrgTree(OrgMsgReq orgMsgReq) {
        SQLHelper sqlHelper = null;
        List<Map<String, Object>> orgMaps= null;
        List<Map<String, Object>> userMaps= null;
        String userId = getLoginUserId();
        try {
            sqlHelper = SQLHelper.createSqlHelper("Core");
            String orgSql = "select ID,ParentID,FullID,Code,Name,'' as UserFields from A_Group ag WHERE GroupType = 'Org'";
            String userSql = "select * from V_UserAndConnection {0}";
             orgMaps = sqlHelper.selectRows(SQLHelper.format(orgSql));
             userMaps = sqlHelper.selectRows(SQLHelper.format(userSql,userId));

        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            sqlHelper.close();
        }
        return buildTree(orgMaps,userMaps);
    }
    public List<OrgTree> buildTree(List<Map<String, Object>> orgMap, List<Map<String, Object>> userMap){
        List<OrgTree> list = new ArrayList<>();
        orgMap.stream().forEach(item -> {
            OrgTree orgTree = new OrgTree();
            orgTree.setID(item.get("ID")!=null? item.get("ID").toString() : "");
            orgTree.setCode(item.get("Code")!=null ? item.get("Code").toString() : "");
            orgTree.setFullID(item.get("FullID")!=null? item.get("FullID").toString() : "");
            orgTree.setName(item.get("Name")!=null? item.get("Name").toString() : "");
            orgTree.setParentID(item.get("ParentID")!=null? item.get("ParentID").toString() : "");
            List<Map<String, Object>> collect = userMap.stream().filter(temp -> temp.get("DeptID").equals(item.get("ID"))).collect(Collectors.toList());
            orgTree.setUserFields(collect.isEmpty()? null : collect);
            //item.put("UserFields",userMap.stream().filter(temp -> temp.get("DeptID").equals(item.get("ID"))));
            list.add(orgTree);
        });
        List<OrgTree> orgTrees = OrgTree.buildTree(list);
        return orgTrees;
    }

    @Override
    public List<Map<String, Object>> getReceiveReadList(String id) {
        SQLHelper sqlHelper = null;
        List<Map<String, Object>> maps= null;
        try {
            sqlHelper = SQLHelper.createSqlHelper("Core");
            String sql = "select ID, MsgBodyID, UserID, UserName, FirstViewTime, ReplyTime, IsDeleted, DeleteTime, AlreadyRead, AlreadyReply from V_MsgReceiveReadList where MsgBodyID='{0}'";
            maps = sqlHelper.selectRows(SQLHelper.format(sql,id));
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            sqlHelper.close();
        }
        return maps;
    }

    @Override
    public Map<String, Object> GetReceiveModel(ReceiveModelReq req) {
        //获取当前登陆人
        AUserDO aUser = userService.getAUser(getLoginUserId());
        //组装格式
        String orgUser = StringUtil.isEmpty(aUser.getDeptName()) ? aUser.getName() : aUser.getDeptName() + "&nbsp;&nbsp;" + aUser.getName();
        //转发消息
        SQLHelper sqlHelper = null;
        Map<String, Object> map = null;
        try {
            sqlHelper = SQLHelper.createSqlHelper("Core");
                if (req.getForwardID() != null && !req.getForwardID().equals("")) {
                    String sql = "select * from I_MsgBody where ID = '{0}'";
                    map = sqlHelper.selectFirstRow(SQLHelper.format(sql, req.getForwardID()));
                    map.put("ID", "");
                    map.put("ParentID", req.getForwardID());
                    map.put("ID", "");
                    map.put("ReceiverIDs", "");
                    map.put("ReceiverNames", "");
                    map.put("Title", "转发：" + map.get("Title"));
                    map.put("Content", "<p>&nbsp;</p><p></p><p></p><p></p><p>" + orgUser + "</p><hr />" + map.get("Content"));
                    map.put("IsReadReceipt", "0");
                    return map;
                }else if (req.getReplyID() != null && !req.getReplyID().equals("")) { //回复消息
                    String sql = "select * from I_MsgBody where ID = '{0}'";
                    map = sqlHelper.selectFirstRow(SQLHelper.format(sql, req.getReplyID()));
                    map.put("ID", "");
                    map.put("ParentID", req.getReplyID());
                    map.put("ReceiverIDs", map.get("SenderID"));
                    map.put("ReceiverNames", map.get("SenderName"));
                    map.put("Title", "回复：" + map.get("Title"));
                    map.put("Content", "<p>&nbsp;</p><p></p><p></p><p></p><p>" + orgUser + "</p><hr />" + map.get("Content"));
                    map.put("IsReadReceipt", "0");
                    return map;
                } else { //消息详情
                    String sql = "select * from I_MsgReceiver where MsgBodyID = '{0}' AND UserID = '{1}'";
                    Map<String, Object> receiver = sqlHelper.selectFirstRow(SQLHelper.format(sql, req.getID(), aUser.getId()));
                    if (receiver != null) {
                        receiver.put("FirstViewTime", LocalDateTime.now());
                        String sql1 = "select * from I_MsgBody where ID = '{0}'";
                        Map<String, Object> msg = sqlHelper.selectFirstRow(SQLHelper.format(sql1, req.getID()));
                        if (msg != null) {
                            if (msg.get("IsReadReceipt") == "1"){
                                String content = "<p style=\"text-align:left;text-indent:-72pt;margin-left:72pt;\" align=\"left\"><span style=\"font-family:宋体;\">消息</span></p>"
                                        + "<p style=\"text-align:left;text-indent:-72pt;margin-left:72pt;\" align=\"left\"><span style=\"font-family:宋体;\"></span></p>"
                                        + "<p style=\"text-align:left;text-indent:-72pt;margin-left:72pt;\" align=\"left\"><span style=\"font-family:宋体;\"><span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>接收人:<span> </span>{0}</span></p>"
                                        + "<p style=\"text-align:left;text-indent:-72pt;margin-left:72pt;\" align=\"left\"><span style=\"font-family:宋体;\"><span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>标题:<span>&nbsp;&nbsp; </span>{1}</span></p>"
                                        + "<p style=\"text-align:left;text-indent:-72pt;margin-left:72pt;\" align=\"left\"><span style=\"font-family:宋体;\"><span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>发送时间:<span> </span>{2}</span></p>"
                                        + "<p style=\"text-align:left;\" align=\"left\"><span style=\"font-family:宋体;\"></span></p>"
                                        + "<p style=\"text-align:left;\" align=\"left\"><span style=\"font-family:宋体;\">阅读时间为 {3}</span></p>";
                                content = SQLHelper.format(content, receiver.get("UserName"), msg.get("Title"), msg.get("SendTime"), receiver.get("FirstViewTime"));
                                msgService.SendMsg("已读：" + msg.get("Title"), content, "", "", msg.get("SenderID").toString(), msg.get("SenderName").toString(), null,null, null, null, null, null, null, null, null, null , null, null, null, null, null);
                            }
                        }
                    }
                    String sql1 = "select * from I_MsgBody where ID = '{0}'";
                    map = sqlHelper.selectFirstRow(SQLHelper.format(sql1, req.getID()));
                    return map;
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                sqlHelper.close();
            }
        return map;
    }

    @Override
    public Object saveMsg(MsgSaveReq msgSaveReq) {
        //判断内容长度
        if (msgSaveReq.getContent().length() > 4000){
            throw exception(NOTIFY_SEND_TEMPLATE_PARAM_LENGTH);
        }
        SQLHelper sqlHelper = null;
        try {
            sqlHelper = SQLHelper.createSqlHelper("Core");
            //判断是否有接收人
            if (!StringUtil.isEmpty(msgSaveReq.getReceiverIDs()) && !StringUtil.isEmpty(msgSaveReq.getReceiverNames())){
                List<String> receiverIDs = Arrays.asList(msgSaveReq.getReceiverIDs().split(","));
                List<String> receiverNames = Arrays.asList(msgSaveReq.getReceiverDeptNames().split(","));
                for (int i = 0; i < receiverIDs.size(); i++) {
                    String sql = "insert into I_MsgBody (ID,MsgBodyID,UserID,UserName) values ('{0}', '{1}', '{2}', '{3}')";
                    sqlHelper.executeNonQuery(SQLHelper.format(sql, SQLHelper.generateUUID(), msgSaveReq.getID(), receiverIDs.get(i), receiverNames.get(i)));
                }
            }
            //判断是否有部门
            if (!StringUtil.isEmpty(msgSaveReq.getReceiverDeptIDs())){
                String deptIds = Arrays.stream(msgSaveReq.getReceiverDeptIDs().split(","))
                        .map(id -> quote(id))
                        .collect(Collectors.joining(","));
                String sql = "select * from V_GroupUser where ParentGroupID in ('{0}')";
                List<Map<String, Object>> maps = sqlHelper.selectRows(SQLHelper.format(sql, deptIds));
                for (int i = 0; i < maps.size(); i++) {
                    //判断是否有接收人
                    String sql1 = "insert into I_MsgReceiver (ID,MsgBodyID,UserID,UserName) values ('{0}', '{1}', '{2}', '{3}')";
                    sqlHelper.executeNonQuery(SQLHelper.format(sql1, SQLHelper.generateUUID(), msgSaveReq.getID(), maps.get(i).get("UserID"), maps.get(i).get("UserName")));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            sqlHelper.close();
        }
        return null;
    }


}
