package mh.cloud.module.system.controller.admin.notify;

import com.alibaba.druid.support.json.JSONUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletResponse;
import mh.cloud.module.system.controller.admin.portal.constant.QRCodeConstant;
import mh.cloud.module.system.service.auth.AdminAuthService;
import org.apache.http.entity.ContentType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户登录二维码回调接口
 */
@Tag(name = "管理后台 - 二维码登录回调地址")
@RestController
@RequestMapping("/callback")
public class QRCodeNotifyController {

    @Resource
    private AdminAuthService authService;

    @PostMapping("/QRCode")
    @Operation(summary = "二维码登录回调接口")
    public void handleQRCallBack(Map<String, Object> map, HttpServletResponse response) throws Exception {
        //1. 获取到请求信息，可能涉及啊到验签

        //2.验签成功后修改，二维码状态,返回数据中包含key信息
        String ket = (String) map.get("key");

        String status = (String) QRCodeConstant.getQRCodeStatus(ket).get("status");
        if (status.equals(QRCodeConstant.QR_IN_HAND)) {
            //3.判断回调状态，回调成功修改状态成功
            if (true) {
                //回调参数中应该会包含用户信息
                if (!authService.handleQRCallBack("key", "USER2A7Y")) {
                    responseERROR(response);
                    return;
                }
                responseSUCCESS(response);
            } else {
                responseERROR(response);
            }
        } else {
            //其余状态没必要再次发起回调
            responseSUCCESS(response);
        }
    }


    /**
     * 成功响应
     */
    private void responseSUCCESS(HttpServletResponse response) throws Exception {
        response.setStatus(200);
        HashMap<Object, Object> map = new HashMap<>();
        map.put("code", "SUCCESS");
        map.put("message", "SUCCESS");
        response.setHeader("Content-type", ContentType.APPLICATION_JSON.toString());
        response.getOutputStream().write(JSONUtils.toJSONString(map).getBytes(StandardCharsets.UTF_8));
        response.flushBuffer();
    }

    /**
     * 失败响应
     */
    private void responseERROR(HttpServletResponse response) throws Exception {
        response.setStatus(200);
        HashMap<Object, Object> map = new HashMap<>();
        map.put("code", "ERROR");
        map.put("message", "ERROR");
        response.setHeader("Content-type", ContentType.APPLICATION_JSON.toString());
        response.getOutputStream().write(JSONUtils.toJSONString(map).getBytes(StandardCharsets.UTF_8));
        response.flushBuffer();
    }
}
