package mh.cloud.module.system.controller.admin.portal.emnu;

public enum RoleType {
    /**
     * 角色类型
     */
    SystemRole("SystemRole","系统角色"),
    OrgRole("OrgRole","组织角色"),
    VariableRole("VariableRole","动态角色"),
    AutoRole("AutoRole", "自动更新角色");

    private final String name;
    private final String code;
    RoleType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getName() {
        return name;
    }
    public String getCode() {
        return code;
    }
}
