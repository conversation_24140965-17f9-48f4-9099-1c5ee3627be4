package mh.cloud.module.system.framework.rpc.config;

import mh.cloud.module.infra.api.file.FileApi;
import mh.cloud.module.infra.api.websocket.WebSocketSenderApi;
import mh.cloud.module.system.api.portal.SystemApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {FileApi.class, SystemApi.class, WebSocketSenderApi.class})
public class RpcConfiguration {
}
