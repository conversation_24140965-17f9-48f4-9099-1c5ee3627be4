package mh.cloud.module.system.controller.admin.uiList.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 列表配置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ListRespVO {

    @Schema(description = "", requiredMode = Schema.RequiredMode.REQUIRED, example = "15795")
    @ExcelProperty("")
    private String id;

    @Schema(description = "编号")
    @ExcelProperty("编号")
    private String code;

    @Schema(description = "名称", example = "王五")
    @ExcelProperty("名称")
    private String name;

    @Schema(description = "数据库连接", example = "张三")
    @ExcelProperty("数据库连接")
    private String connName;

    @Schema(description = "列表分类", example = "23833")
    @ExcelProperty("列表分类")
    private String categoryID;

    @Schema(description = "列表分类名称")
    @ExcelProperty("列表分类名称")
    private String category;

    @Schema(description = "sql语句")
    @ExcelProperty("sql语句")
    private String sql;

    @Schema(description = "表名称")
    @ExcelProperty("表名称")
    private String tableNames;

    @Schema(description = "js脚本")
    @ExcelProperty("js脚本")
    private String script;

    @Schema(description = "js脚本文本格式")
    @ExcelProperty("js脚本文本格式")
    private String scriptText;

    @Schema(description = "启用序号")
    @ExcelProperty("启用序号")
    private String hasRowNumber;

    @Schema(description = "列表定义", example = "29649")
    @ExcelProperty("列表定义")
    private String layoutGrid;

    @Schema(description = "列表字段定义")
    @ExcelProperty("列表字段定义")
    private String layoutField;

    @Schema(description = "查询条件定义")
    @ExcelProperty("查询条件定义")
    private String layoutSearch;

    @Schema(description = "列表按钮定义")
    @ExcelProperty("列表按钮定义")
    private String layoutButton;

    @Schema(description = "配置用到的枚举")
    @ExcelProperty("配置用到的枚举")
    private String enumKeys;

    @Schema(description = "列表属性定义")
    @ExcelProperty("列表属性定义")
    private String settings;

    @Schema(description = "系统生成html")
    @ExcelProperty("系统生成html")
    private String systemHTML;

    @Schema(description = "", example = "7687")
    @ExcelProperty("")
    private String createUserID;

    @Schema(description = "")
    @ExcelProperty("")
    private String createUser;

    @Schema(description = "")
    @ExcelProperty("")
    private LocalDateTime createTime;

    @Schema(description = "", example = "11211")
    @ExcelProperty("")
    private String modifyUserID;

    @Schema(description = "")
    @ExcelProperty("")
    private String modifyUser;

    @Schema(description = "")
    @ExcelProperty("")
    private LocalDateTime modifyTime;

    @Schema(description = "发布状态")
    @ExcelProperty("发布状态")
    private String released;

    @Schema(description = "禁止删除流程")
    @ExcelProperty("禁止删除流程")
    private String denyDeleteFlow;

    @Schema(description = "子系统编号")
    @ExcelProperty("子系统编号")
    private String systemCode;

    @Schema(description = "初始化sql")
    @ExcelProperty("初始化sql")
    private String initSQL;

    @Schema(description = "", example = "随便")
    @ExcelProperty("")
    private String description;

    @Schema(description = "自定义html")
    @ExcelProperty("自定义html")
    private String userHTML;

    @Schema(description = "")
    @ExcelProperty("")
    private String isDeleted;

    @Schema(description = "列表数据权限", example = "29641")
    @ExcelProperty("列表数据权限")
    private String authUserID;

    @Schema(description = "列表数据权限")
    @ExcelProperty("列表数据权限")
    private String authUser;

    @Schema(description = "页面查询前SQL")
    @ExcelProperty("页面查询前SQL")
    private String beforeSelect;

    @Schema(description = "查询完成后SQL")
    @ExcelProperty("查询完成后SQL")
    private String afterSelect;

    @Schema(description = "")
    @ExcelProperty("")
    private String preInitScript;

    @Schema(description = "")
    @ExcelProperty("")
    private String listLoadedScript;

    @Schema(description = "")
    @ExcelProperty("")
    private Boolean filterByCurDept;

    @Schema(description = "")
    @ExcelProperty("")
    private String authRole;

    @Schema(description = "", example = "29382")
    @ExcelProperty("")
    private String authRoleID;

    @Schema(description = "设计器列表对象记录ID", example = "7506")
    @ExcelProperty("设计器列表对象记录ID")
    private String designerID;

}