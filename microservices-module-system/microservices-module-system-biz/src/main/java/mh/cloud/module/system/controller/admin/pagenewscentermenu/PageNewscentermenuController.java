package mh.cloud.module.system.controller.admin.pagenewscentermenu;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import mh.cloud.framework.common.pojo.PageParam;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.framework.common.util.object.BeanUtils;
import static mh.cloud.framework.common.pojo.CommonResult.success;

import mh.cloud.framework.excel.core.util.ExcelUtils;

import mh.cloud.framework.apilog.core.annotation.ApiAccessLog;
import static mh.cloud.framework.apilog.core.enums.OperateTypeEnum.*;

import mh.cloud.module.system.controller.admin.pagenewscentermenu.vo.*;
import mh.cloud.module.system.dal.dataobject.pagenewscentermenu.PageNewscentermenuDO;
import mh.cloud.module.system.service.pagenewscentermenu.PageNewscentermenuService;

@Tag(name = "管理后台 - 菜单管理")
@RestController
@RequestMapping("/system/page-newscentermenu")
@Validated
public class PageNewscentermenuController {

    @Resource
    private PageNewscentermenuService pageNewscentermenuService;

    @PostMapping("/create")
    @Operation(summary = "创建菜单管理")
    @PreAuthorize("@ss.hasPermission('system:page-newscentermenu:create')")
    public CommonResult<String> createPageNewscentermenu(@Valid @RequestBody PageNewscentermenuSaveReqVO createReqVO) {
        return success(pageNewscentermenuService.createPageNewscentermenu(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新菜单管理")
    @PreAuthorize("@ss.hasPermission('system:page-newscentermenu:update')")
    public CommonResult<Boolean> updatePageNewscentermenu(@Valid @RequestBody PageNewscentermenuSaveReqVO updateReqVO) {
        pageNewscentermenuService.updatePageNewscentermenu(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除菜单管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:page-newscentermenu:delete')")
    public CommonResult<Boolean> deletePageNewscentermenu(@RequestParam("id") String id) {
        pageNewscentermenuService.deletePageNewscentermenu(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得菜单管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:page-newscentermenu:query')")
    public CommonResult<PageNewscentermenuRespVO> getPageNewscentermenu(@RequestParam("id") String id) {
        PageNewscentermenuDO pageNewscentermenu = pageNewscentermenuService.getPageNewscentermenu(id);
        return success(BeanUtils.toBean(pageNewscentermenu, PageNewscentermenuRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得菜单管理分页")
    @PreAuthorize("@ss.hasPermission('system:page-newscentermenu:query')")
    public CommonResult<PageResult<PageNewscentermenuRespVO>> getPageNewscentermenuPage(@Valid PageNewscentermenuPageReqVO pageReqVO) {
        PageResult<PageNewscentermenuDO> pageResult = pageNewscentermenuService.getPageNewscentermenuPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PageNewscentermenuRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出菜单管理 Excel")
    @PreAuthorize("@ss.hasPermission('system:page-newscentermenu:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportPageNewscentermenuExcel(@Valid PageNewscentermenuPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<PageNewscentermenuDO> list = pageNewscentermenuService.getPageNewscentermenuPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "菜单管理.xls", "数据", PageNewscentermenuRespVO.class,
                        BeanUtils.toBean(list, PageNewscentermenuRespVO.class));
    }

}
