package mh.cloud.module.system.service.db;

import com.jfinal.plugin.activerecord.ActiveRecordPlugin;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.plugin.druid.DruidPlugin;
import mh.cloud.module.system.service.db.VO.DbConfig;
import org.jetbrains.annotations.NotNull;

import java.util.List;

public class JfinalSqlHelper {

    private static DruidPlugin startDruidPlugin(DbConfig dbConfig) {
        DruidPlugin druidPlugin = createDruidPlugin(dbConfig);
        druidPlugin.start();
        return druidPlugin;
    }

    private static ActiveRecordPlugin startActiveRecordPlugin(DruidPlugin druidPlugin) {
        ActiveRecordPlugin arp = new ActiveRecordPlugin(druidPlugin);
        arp.setShowSql(true);
        arp.setDevMode(true);
        arp.start();
        return arp;
    }

    private static void stopPlugins(DruidPlugin druidPlugin, ActiveRecordPlugin arp) {
        if (arp != null) {
            arp.stop();
        }
        if (druidPlugin != null) {
            druidPlugin.stop();
        }
    }

    public static DruidPlugin createDruidPlugin(DbConfig config) {
        return new DruidPlugin(config.getJdbcUrl(), config.getUsername(), config.getPassword(), config.getDriverClassName());
    }

    public static List<Record> find(DbConfig dbConfig, String sql) {
        Result result = getResult(dbConfig);
        List<Record> records = Db.find(sql);
        stopPlugins(result.druidPlugin(), result.arp());
        return records;
    }
    public static List<Record> find(DbConfig dbConfig, String sql,Object... paras) {
        Result result = getResult(dbConfig);
        List<Record> records = Db.find(sql,paras);
        stopPlugins(result.druidPlugin(), result.arp());
        return records;
    }

    public static int update(DbConfig dbConfig, String sql) {
        Result result = getResult(dbConfig);
        int r = Db.update(sql);
        stopPlugins(result.druidPlugin(), result.arp());
        return r;
    }

    public static int delete(DbConfig dbConfig, String sql) {


        Result result = getResult(dbConfig);
        int r = Db.delete(sql);
        stopPlugins(result.druidPlugin(), result.arp());
        return r;

    }

    public static Page<Record> paginateByFullSql(DbConfig dbConfig, int pageNumber, int pageSize, String totalRowSql, String findSql, Object... paras) {
        Result result = getResult(dbConfig);
        Page<Record> recordPage = Db.paginateByFullSql(pageNumber, pageSize, totalRowSql, findSql, paras);
        stopPlugins(result.druidPlugin(), result.arp());
        return recordPage;
    }

    public static Page<Record> paginate(DbConfig dbConfig, int pageNumber, int pageSize, String select, String sqlExceptSelect, Object... paras) {
        Result result = getResult(dbConfig);

        Page<Record> recordPage = Db.paginate(pageNumber, pageSize, select, sqlExceptSelect, paras);
        stopPlugins(result.druidPlugin(), result.arp());
        return recordPage;
    }

    public static Page<Record> paginate(DbConfig dbConfig, int pageNumber, int pageSize, String select, String sqlExceptSelect) {
        Result result = getResult(dbConfig);
        Page<Record> recordPage = Db.paginate(pageNumber, pageSize, select, sqlExceptSelect);
        stopPlugins(result.druidPlugin(), result.arp());
        return recordPage;
    }

    private static @NotNull Result getResult(DbConfig dbConfig) {
        DruidPlugin druidPlugin = startDruidPlugin(dbConfig);
        ActiveRecordPlugin arp = startActiveRecordPlugin(druidPlugin);
        return new Result(druidPlugin, arp);
    }

    private record Result(DruidPlugin druidPlugin, ActiveRecordPlugin arp) {
    }

}
