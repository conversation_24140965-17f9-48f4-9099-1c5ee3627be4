package mh.cloud.module.system.controller.admin.configmanage.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 系统配置结构树新增/修改 Request VO")
@Data
public class ConfigmanageSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "4372")
    private String id;

    @Schema(description = "父ID", example = "24317")
    private String parentID;

    @Schema(description = "全ID", example = "14198")
    private String fullID;

    @Schema(description = "排序索引")
    private Integer sortIndex;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "名称不能为空")
    private String name;

    @Schema(description = "code")
    private String code;

    @Schema(description = "节点类型", example = "2")
    private String type;

    @Schema(description = "iconcls")
    private String iconCls;

    @Schema(description = "主界面地址", example = "https://www.iocoder.cn")
    private String url;

    @Schema(description = "控制类型", example = "2")
    private String ctrlType;

    @Schema(description = "权限，可以为页面控件ID，数据的查询条件")
    private String auth;

    @Schema(description = "描述", example = "你猜")
    private String description;

    @Schema(description = "子系统编号")
    private String systemCode;

    @Schema(description = "创建人")
    private String createUser;

    @Schema(description = "创建用户id", example = "7867")
    private String createUserID;

    @Schema(description = "修改用户")
    private String modifyUser;

    @Schema(description = "修改用户id", example = "31093")
    private String modifyUserID;

    @Schema(description = "修改时间")
    private LocalDateTime modifyTime;

    @Schema(description = "节点状态（未发布，已发布）", example = "2")
    private String status;

    @Schema(description = "节点编辑权限")
    private String editAuth;

    @Schema(description = "节点编辑权限")
    private String editAuthUser;

    @Schema(description = "节点的连接页面", example = "https://www.iocoder.cn")
    private String configUrl;

    @Schema(description = "主数据库连接")
    private String mainDBConn;

    @Schema(description = "relateId", example = "2096")
    private String relateID;

    @Schema(description = "关联表")
    private String relateTable;

    @Schema(description = "是否主界面", example = "https://www.iocoder.cn")
    private String isMainUrl;

    @Schema(description = "是否删除")
    private String isDeleted;

    @Schema(description = "isStandard")
    private String isStandard;

}