package mh.cloud.module.system.controller.admin.group.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 系统角色管理新增/修改 Request VO")
@Data
public class AGroupSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "17234")
    private String id;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "菜单名字")
    @NotEmpty(message = "名称不能为空")
    private String name;

    @Schema(description = "简称", example = "赵六")
    private String shortName;

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "编号不能为空")
    private String code;

    @Schema(description = "父节点ID", example = "25940")
    private String parentID;

    @Schema(description = "节点全路径", example = "27943")
    private String fullID;

    @Schema(description = "分组类型（组织或角色）", example = "1")
    private String groupType;

    @Schema(description = "类型", example = "1")
    private String type;

    @Schema(description = "排序")
    private Integer sortIndex;

    @Schema(description = "删除状态")
    private String isDeleted;

    @Schema(description = "删除日期")
    private LocalDateTime deleteTime;

    @Schema(description = "组织等级")
    private Integer orgLevel;

    @Schema(description = "描述", example = "你说的对")
    private String description;

    @Schema(description = "所在地")
    private String location;

    @Schema(description = "子系统编号")
    private String systemCode;

    @Schema(description = "外部数据Key")
    private String outKey;

    @Schema(description = "数据库", example = "赵六")
    private String connName;

    @Schema(description = "查询SQL")
    private String userSQL;

    @Schema(description = "部门全路径名称", example = "luohang")
    private String fullName;

    @Schema(description = "组织角色")
    private String orgRole;

    @Schema(description = "所属分类", example = "14555")
    private String categoryID;

    @Schema(description = "所属分类名称", example = "luohang")
    private String categoryName;

    @Schema(description = "使用分类")
    private String useCategory;

    @Schema(description = "属性分类")
    private String propCategory;

    @Schema(description = "是否是领导")
    private Integer isleader;

    @Schema(description = "creditCode")
    private String creditCode;

    @Schema(description = "DeptUnit")
    private String deptUnit;

    @Schema(description = "DeptUnitName", example = "李四")
    private String deptUnitName;

    @Schema(description = "Tlevel")
    private Integer tlevel;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime modifyTime;

    @Schema(description = "角色用户")
    private String jsyh;

    @Schema(description = "角色用户名称", example = "张三")
    private String jSYHName;

    @Schema(description = "平台标识")
    private String platformTag;

    @Schema(description = "所属分类名称", example = "赵六")
    private String categoryIDName;

    @Schema(description = "是否开发人员维护")
    private String isEditable;

    @Schema(description = "是否开发人员使用")
    private String sfkfrysy;

    @Schema(description = "GlYwName", example = "luohang")
    private String glYwName;

    @Schema(description = "GlYwId", example = "3202")
    private String glYwId;

    @Schema(description = "UserCountInRole")
    private Integer userCountInRole;

    @Schema(description = "MenuCountInRole")
    private Integer menuCountInRole;

    @Schema(description = "Cl_JSYHName", example = "luohang")
    private String clJsyhname;

    @Schema(description = "颜色")
    private String color;

}