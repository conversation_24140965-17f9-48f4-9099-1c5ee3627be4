package mh.cloud.module.system.service.ares;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import mh.cloud.module.system.dal.dataobject.ares.TreeNode;
import mh.cloud.module.system.dal.dataobject.ares.TreeNode2;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import mh.cloud.module.system.controller.admin.ares.vo.*;
import mh.cloud.module.system.dal.dataobject.ares.AResDO;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.pojo.PageParam;
import mh.cloud.framework.common.util.object.BeanUtils;

import mh.cloud.module.system.dal.mysql.ares.AResMapper;

import static mh.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static mh.cloud.module.system.enums.ErrorCodeConstants.*;

/**
 * 资源管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AResServiceImpl implements AResService {

    @Resource
    private AResMapper aResMapper;

    @Override
    public String createARes(AResSaveReqVO createReqVO) {
        // 校验父ID的有效性
        validateParentARes(null, createReqVO.getParentID());
        // 校验名称的唯一性
//        validateAResNameUnique(null, createReqVO.getParentID(), createReqVO.getName());

        // 插入
        AResDO aRes = BeanUtils.toBean(createReqVO, AResDO.class);
        aResMapper.insert(aRes);
        //更新fullId
        AResDO aResParent = aResMapper.selectById(aRes.getParentID());
        String fullId = null;
        if (aResParent != null) {
            fullId = aResParent.getFullID() + "." + aRes.getId();
        } else {
            fullId = aRes.getId();
        }
        aResMapper.update(new LambdaUpdateWrapper<AResDO>().eq(AResDO::getId, aRes.getId()).set(AResDO::getFullID, fullId));
        // 返回
        return aRes.getId();
    }

    @Override
    public void updateARes(AResSaveReqVO updateReqVO) {
        // 校验存在
        validateAResExists(updateReqVO.getId());
        // 校验父ID的有效性
        validateParentARes(updateReqVO.getId(), updateReqVO.getParentID());
        // 校验名称的唯一性
//        validateAResNameUnique(updateReqVO.getId(), updateReqVO.getParentID(), updateReqVO.getName());

        // 更新
        AResDO updateObj = BeanUtils.toBean(updateReqVO, AResDO.class);
        aResMapper.updateById(updateObj);
    }

    @Override
    public void deleteARes(String id) {
        // 校验存在
        validateAResExists(id);
        // 校验是否有子资源管理
        if (aResMapper.selectCountByParentID(id) > 0) {
            throw exception(A_RES_EXITS_CHILDREN);
        }
        // 删除
        aResMapper.deleteById(id);
    }

    private void validateAResExists(String id) {
        if (aResMapper.selectById(id) == null) {
            throw exception(A_RES_NOT_EXISTS);
        }
    }

    private void validateParentARes(String id, String parentID) {
        if (parentID == null || AResDO.PARENT_ID_ROOT.equals(parentID)) {
            return;
        }
        // 1. 不能设置自己为父资源管理
        if (Objects.equals(id, parentID)) {
            throw exception(A_RES_PARENT_ERROR);
        }
        // 2. 父资源管理不存在
        AResDO parentARes = aResMapper.selectById(parentID);
        if (parentARes == null) {
            throw exception(A_RES_PARENT_NOT_EXITS);
        }
        // 3. 递归校验父资源管理，如果父资源管理是自己的子资源管理，则报错，避免形成环路
        if (id == null) { // id 为空，说明新增，不需要考虑环路
            return;
        }
        for (int i = 0; i < Short.MAX_VALUE; i++) {
            // 3.1 校验环路
            parentID = parentARes.getParentID();
            if (Objects.equals(id, parentID)) {
                throw exception(A_RES_PARENT_IS_CHILD);
            }
            // 3.2 继续递归下一级父资源管理
            if (parentID == null || AResDO.PARENT_ID_ROOT.equals(parentID)) {
                break;
            }
            parentARes = aResMapper.selectById(parentID);
            if (parentARes == null) {
                break;
            }
        }
    }

    private void validateAResNameUnique(String id, String parentID, String name) {
        AResDO aRes = aResMapper.selectByParentIDAndName(parentID, name);
        if (aRes == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的资源管理
        if (id == null) {
            throw exception(A_RES_NAME_DUPLICATE);
        }
        if (!Objects.equals(aRes.getId(), id)) {
            throw exception(A_RES_NAME_DUPLICATE);
        }
    }

    @Override
    public AResDO getARes(String id) {
        return aResMapper.selectById(id);
    }

    @Override
    public List<AResDO> getAResList(AResListReqVO listReqVO) {
        return aResMapper.selectList(listReqVO);
    }

    @Override
    public List<TreeNode> getResTree(String id, List<String> roleIds, String name) {
        // 查询当前登录用户拥有的角色
        List<AResDO> aResDOS = aResMapper.selectResTree(id, roleIds, name);
        List<TreeNode> list = new ArrayList<>();
        aResDOS.stream().forEach(item -> {
            TreeNode treeNode = new TreeNode();
            if (item.getId().equals(id)) {
                treeNode.setId(item.getId());
                treeNode.setName(item.getName());
                treeNode.setUrl(item.getUrl());
            } else {
                treeNode.setId(item.getId());
                treeNode.setParentId(item.getParentID());
                treeNode.setName(item.getName());
                treeNode.setUrl(item.getUrl());
            }
            list.add(treeNode);
        });
        return TreeNode.buildTree(list);
    }

    @Override
    public List<TreeNode> getResTreeLazy(String id) {
        LambdaQueryWrapper<AResDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AResDO::getParentID, id).or().eq(AResDO::getId, id);
        List<AResDO> aResDOS = aResMapper.selectList(wrapper);
        List<TreeNode> list = new ArrayList<>();
        aResDOS.stream().forEach(item -> {
            TreeNode treeNode = new TreeNode();
            if (item.getId().equals(id)) {
                treeNode.setId(item.getId());
                treeNode.setName(item.getName());
                treeNode.setUrl(item.getUrl());

            } else {
                treeNode.setId(item.getId());
                treeNode.setParentId(item.getParentID());
                treeNode.setName(item.getName());
                treeNode.setUrl(item.getUrl());
            }
            list.add(treeNode);
        });
        return TreeNode.buildTree(list);
    }

//    public List<TreeNode> buildTree(List<TreeNode> collect) {
//        List<String> ids = collect.stream().map(a -> a.getId()).collect(Collectors.toList());
//        //处理父节点
//        ArrayList<TreeNode> pList = new ArrayList<>();
//        //遍历，查找出没有父节点的节点
//        for (TreeNode t : collect) {
//            //检查t的父节点是否在collect中
//            if (!ids.contains(t.getParentId())) {
//                pList.add(t);
//            }
//        }
//        for (TreeNode p : pList) {
//            addChildren(p, collect);
//        }
//        return pList;
//    }
    @Override
    public List<TreeNode2> buildTree(List<TreeNode2> collect) {
        // 使用HashMap存储所有节点，以便快速访问
        Map<String, TreeNode2> nodeMap = new HashMap<>();
        // 用于存储根节点
        List<TreeNode2> roots = new ArrayList<>();
        // 遍历列表，将所有节点存储到HashMap中，并初始化子节点列表
        for (TreeNode2 node : collect) {

            nodeMap.put(node.getId(), node);
        }
        // 遍历列表，建立父子关系
        for (TreeNode2 node : collect) {
            String parentId = node.getParentId();
            if (parentId == null || !nodeMap.containsKey(parentId)) {
                // 如果没有父节点或父节点不在HashMap中，则将该节点视为根节点
                roots.add(node);
            } else {
                // 否则，将该节点添加到其父节点的子节点列表中
                TreeNode2 parent = nodeMap.get(parentId);
                if(parent.getChildren()==null){

                    parent.setChildren(new ArrayList<>());
                }
                parent.getChildren().add(node);
            }
        }
        // 返回根节点列表
        return roots;
    }

    @Override
    public List<AResDO> getAResList2(AResListReqVO listReqVO) {
        return aResMapper.getAResList2(listReqVO);
    }

    private void addChildren(TreeNode p, List<TreeNode> all) {
        p.setChildren(new ArrayList<>());
        all.forEach(a -> {
            if (a.getParentId()!=null&&a.getParentId().equals(p.getId())) {
                p.getChildren().add(a);
            }
        });
        if (!p.getChildren().isEmpty()) {
            p.getChildren().forEach(a -> addChildren(a, all));
        }
    }
}