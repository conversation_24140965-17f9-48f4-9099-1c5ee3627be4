package mh.cloud.module.system.controller.admin.portal.service;

import mh.cloud.module.system.controller.admin.portal.V0.EntryMenu;
import mh.cloud.module.system.controller.admin.portal.V0.OutSystemV0;

import java.util.List;
import java.util.Map;

public interface ServiceCenterService {

    List<EntryMenu> List(String search);

    List<Map<String, Object>> getLeftListCat();

    OutSystemV0 getOutSystemUrl(OutSystemV0 outSystem);

}
