package mh.cloud.module.system.service.groupres;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import mh.cloud.module.system.controller.admin.groupres.vo.*;
import mh.cloud.module.system.dal.dataobject.groupres.GroupresDO;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.pojo.PageParam;
import mh.cloud.framework.common.util.object.BeanUtils;

import mh.cloud.module.system.dal.mysql.groupres.GroupresMapper;

import static mh.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static mh.cloud.module.system.enums.ErrorCodeConstants.*;

/**
 * 资源-角色关联 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class GroupresServiceImpl implements GroupresService {

    @Resource
    private GroupresMapper groupresMapper;

    @Override
    public String createGroupres(GroupresSaveReqVO createReqVO) {
        // 插入
        GroupresDO groupres = BeanUtils.toBean(createReqVO, GroupresDO.class);
        groupresMapper.insert(groupres);
        // 返回
        return groupres.getId();
    }

    @Override
    public void updateGroupres(GroupresSaveReqVO updateReqVO) {
        // 校验存在
        validateGroupresExists(updateReqVO.getId());
        // 更新
        GroupresDO updateObj = BeanUtils.toBean(updateReqVO, GroupresDO.class);
        groupresMapper.updateById(updateObj);
    }

    @Override
    public void deleteGroupres(String id) {
        // 校验存在
        validateGroupresExists(id);
        // 删除
        groupresMapper.deleteById(id);
    }

    private void validateGroupresExists(String id) {
        if (groupresMapper.selectById(id) == null) {
            throw exception(GROUPRES_NOT_EXISTS);
        }
    }

    @Override
    public GroupresDO getGroupres(String id) {
        return groupresMapper.selectById(id);
    }

    @Override
    public PageResult<GroupresDO> getGroupresPage(GroupresPageReqVO pageReqVO) {
        return groupresMapper.selectPage(pageReqVO);
    }

}