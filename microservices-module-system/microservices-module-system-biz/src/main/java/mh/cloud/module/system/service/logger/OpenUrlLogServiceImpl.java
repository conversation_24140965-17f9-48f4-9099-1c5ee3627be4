package mh.cloud.module.system.service.logger;

import lombok.extern.slf4j.Slf4j;
import mh.cloud.framework.security.core.LoginUser;
import mh.cloud.framework.security.core.util.SecurityFrameworkUtils;
import mh.cloud.module.system.service.db.SQLHelper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

import static mh.cloud.module.system.service.db.SQLHelper.generateUUID;

@Service
@Validated
@Slf4j
public class OpenUrlLogServiceImpl implements OpenUrlLogService {
    @Override
    public void create(Map<String, Object> params) {
        // 构建SQL模板
        String sqlTemplate = SQLHelper.format("INSERT INTO T_OpenUrlLog(Id,UserID,UserName,RecordTime,LoginName,ItemID,ItemUrl,ItemFuncType,ItemMenuName,ItemSrc,Remark) " +
                "VALUES('{0}','{1}','{2}','{3}','{4}','{5}','{6}','{7}','{8}','{9}','{10}')");

        // 获取当前登录用户信息
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();

        // 格式化当前时间
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedDateTime = now.format(formatter);

        // 获取参数
        LocalDateTime recordTime = (LocalDateTime) params.get("recordTime");
        String itemId = (String) params.get("itemId");
        String itemUrl = (String) params.get("itemUrl");
        String itemFuncType = (String) params.get("itemFuncType");
        String itemMenuName = (String) params.get("itemMenuName");
        String itemSrc = (String) params.get("itemSrc");
        String remark = (String) params.get("remark");

        // 格式化SQL语句
        String sql = SQLHelper.format(sqlTemplate,
                generateUUID(),
                loginUser.getId(),
                loginUser.getInfo().get("nickname"),
                recordTime != null ? recordTime.format(formatter) : formattedDateTime,
                loginUser.getInfo().get("WorkNo"),
                itemId != null ? itemId : "",
                itemUrl != null ? itemUrl : "",
                itemFuncType != null ? itemFuncType : "",
                itemMenuName != null ? itemMenuName : "",
                itemSrc != null ? itemSrc : "",
                remark != null ? remark : "");

        // 执行SQL
        SQLHelper sqlHelper = SQLHelper.CreateSqlHelper("Log");
        try {
            sqlHelper.executeNonQuery(sql);
        } finally {
            sqlHelper.close();
        }
    }
}


