package mh.cloud.module.system.service.job;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import mh.cloud.module.system.dal.dataobject.job.SysJobLog;

import java.util.List;
import java.util.Map;

public interface IJobLogService {
    Map<String, Object> getJobLogList(String jobId, String beginTime, String endTime, String status, Page<SysJobLog> page);



    Boolean delJobLogs(String jobId, String startTime, String endTime);

    List<Map<String,Object>> getResult(String taskId);
}
