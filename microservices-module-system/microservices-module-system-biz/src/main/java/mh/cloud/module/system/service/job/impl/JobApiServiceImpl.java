package mh.cloud.module.system.service.job.impl;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import mh.cloud.module.system.dal.dataobject.job.JobApi;
import mh.cloud.module.system.dal.dataobject.job.JobTaskType;
import mh.cloud.module.system.dal.mysql.job.JobApiMapper;
import mh.cloud.module.system.service.job.IJobApiService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class JobApiServiceImpl extends ServiceImpl<JobApiMapper, JobApi> implements IJobApiService {

    /**
     * 获取到JobApi执行列为表，并分组
     *
     * @param jobId
     */
    @Override
    public Map<String, List<JobApi>> getApiMap(String jobId) {
        List<JobApi> jobApis = lambdaQuery().eq(JobApi::getJobId, jobId).list();
        return jobApis.stream().collect(Collectors.groupingBy(JobApi::getModelType));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveAllApi(List<JobApi> apiSQLList, List<JobApi> apiUrlList, String jobId) {
        LocalDateTime now = LocalDateTime.now();
        List<JobApi> apiSQL = new ArrayList<>();
        List<JobApi> apiUrl = new ArrayList<>();
        if (!ObjUtil.isEmpty(apiSQLList)) {
            apiSQL = apiSQLList.stream().peek(item -> {
                item.setModelType(JobTaskType.API_SQL);
                item.setCreateTime(now);
                item.setJobId(jobId);
            }).toList();
        }
        if (!ObjUtil.isEmpty(apiUrlList)) {
            apiUrl = apiUrlList.stream().peek(item -> {
                item.setModelType(JobTaskType.API_URL);
                item.setCreateTime(now);
                item.setJobId(jobId);
            }).toList();
        }
        List<JobApi> allApi = new ArrayList<>(apiSQL);
        allApi.addAll(apiUrl);
        return this.saveBatch(allApi);
    }
}
