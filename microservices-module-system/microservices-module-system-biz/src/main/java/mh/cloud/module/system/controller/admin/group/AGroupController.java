package mh.cloud.module.system.controller.admin.group;

import cn.hutool.core.util.StrUtil;
import mh.cloud.module.system.controller.admin.mail.vo.log.MailLogRespVO;
import mh.cloud.module.system.dal.dataobject.ares.TreeNode;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import mh.cloud.framework.common.pojo.PageParam;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.framework.common.util.object.BeanUtils;
import static mh.cloud.framework.common.pojo.CommonResult.success;

import mh.cloud.framework.excel.core.util.ExcelUtils;

import mh.cloud.framework.apilog.core.annotation.ApiAccessLog;
import static mh.cloud.framework.apilog.core.enums.OperateTypeEnum.*;

import mh.cloud.module.system.controller.admin.group.vo.*;
import mh.cloud.module.system.dal.dataobject.group.AGroupDO;
import mh.cloud.module.system.service.group.AGroupService;

@Tag(name = "管理后台 - 系统角色管理")
@RestController
@RequestMapping("/system/A-group")
@Validated
public class AGroupController {

    @Resource
    private AGroupService aGroupService;

    @GetMapping("/page")
    @Operation(summary = "获得组织管理分页")
    @PreAuthorize("@ss.hasPermission('system:A-group:query')")
    public CommonResult<PageResult<AGroupRespVO>> getAGroupPage(@Valid AGroupPageReqVO pageReqVO) {
        PageResult<AGroupDO> pageResult = aGroupService.getAGroupPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AGroupRespVO.class));
    }


    @PostMapping("/create")
    @Operation(summary = "创建系统角色管理")
    @PreAuthorize("@ss.hasPermission('system:A-group:create')")
    public CommonResult<String> createAGroup(@Valid @RequestBody AGroupSaveReqVO createReqVO) {
        return success(aGroupService.createAGroup(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新系统角色管理")
    @PreAuthorize("@ss.hasPermission('system:A-group:update')")
    public CommonResult<Boolean> updateAGroup(@Valid @RequestBody AGroupSaveReqVO updateReqVO) {
        aGroupService.updateAGroup(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除系统角色管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:A-group:delete')")
    public CommonResult<Boolean> deleteAGroup(@RequestParam("id") String id) {
        aGroupService.deleteAGroup(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得系统角色管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:A-group:query')")
    public CommonResult<AGroupDO> getAGroup(@RequestParam("id") String id) {
        AGroupDO aGroup = aGroupService.getAGroup(id);
        return success(aGroup);
    }

    @GetMapping("/getById")
    @Operation(summary = "获得系统角色管理(详细信息)")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")

    public CommonResult<AGroupDO> getAGroupById(@RequestParam("id") String id) {
        AGroupDO aGroup = aGroupService.getAGroup(id);
        return success(aGroup);
    }


    @GetMapping("/list")
    @Operation(summary = "获得系统角色管理列表")
    @PreAuthorize("@ss.hasPermission('system:A-group:query')")
    public CommonResult<List<AGroupRespVO>> getAGroupList(@Valid AGroupListReqVO listReqVO) {
        if(StrUtil.isEmptyIfStr(listReqVO.getGroupType())){
            listReqVO.setGroupType("Role");
        }
        List<AGroupDO> list = aGroupService.getAGroupList(listReqVO);
        return success(BeanUtils.toBean(list, AGroupRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出系统角色管理 Excel")
    @PreAuthorize("@ss.hasPermission('system:A-group:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAGroupExcel(@Valid AGroupListReqVO listReqVO,
              HttpServletResponse response) throws IOException {
        List<AGroupDO> list = aGroupService.getAGroupList(listReqVO);
        // 导出 Excel
        ExcelUtils.write(response, "系统角色管理.xls", "数据", AGroupRespVO.class,
                        BeanUtils.toBean(list, AGroupRespVO.class));
    }

    @GetMapping("/getAuthRole")
    @Operation(summary = "获得已关联到资源的系统角色列表")
    @Parameter(name = "id", description = "资源id", required = true)
    public CommonResult<PageResult<AGroupRespVO>> getAuthRole(@Valid AGroupRespVO groupRespVO) {
        PageResult<AGroupDO> list = aGroupService.getAuthRole(groupRespVO);
        return success(BeanUtils.toBean(list, AGroupRespVO.class));
    }

    @PostMapping("/deleteGroupRes")
    @Operation(summary = "删除资源与角色组织关联关系")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:A-group:delete')")
    public CommonResult<Boolean> deleteGroupRes(@RequestParam("groupId") String groupId, @RequestParam("resId") String resId) {
        aGroupService.deleteGroupRes(groupId, resId);
        return success(true);
    }

    @GetMapping("/getGroupTree")
    @Operation(summary = "获得角色管理树")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @Parameter(name = "id", description = "groupType", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:A-group:query')")
    public CommonResult<List<TreeNode>> getGroupTree(@RequestParam("id") String id, @RequestParam("groupType") String groupType) {
        List<TreeNode> resTree = aGroupService.getGroupTree(id, groupType);
        return success(resTree);
    }

    @GetMapping("/getAGroupDataList")
    @Operation(summary = "获得系统角色管理列表")
    @PreAuthorize("@ss.hasPermission('system:A-group:query')")
    public CommonResult<List<AGroupRespVO>> getAGroupDataList(@RequestParam("groupType") String groupType, @RequestParam("type") String type, @RequestParam("id") String id) {
        List<AGroupDO> list = aGroupService.getAGroupDataList(groupType,type, id);
        return success(BeanUtils.toBean(list, AGroupRespVO.class));
    }

    @GetMapping("/getAuthRoleByResId")
    @Operation(summary = "获得已关联到资源的系统角色列表")
    @Parameter(name = "id", description = "资源id", required = true)
    public CommonResult<List<AGroupRespVO>> getAuthRoleByResId(@RequestParam("resId") String resId) {
        List<AGroupDO>  list = aGroupService.getAuthRole(resId);
        return success(BeanUtils.toBean(list, AGroupRespVO.class));
    }

    @GetMapping("/saveGroupRes")
    @Operation(summary = "创建系统角色管理")
    @PreAuthorize("@ss.hasPermission('system:A-group:create')")
    public CommonResult<String> saveGroupRes(@RequestParam("resId") String resId, @RequestParam("groupIds") String groupIds, @RequestParam("isAuthLower") Boolean isAuthLower) {
        return success(aGroupService.saveAGroupRes(resId,groupIds,isAuthLower));
    }

    @GetMapping("/getResIdsByGroupId")
    @Operation(summary = "获得系统角色管理(详细信息)")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")

    public CommonResult<String[]> getResIdsByGroupId(@RequestParam("id") String id) {
        //根据groupId查询resIds
        List<String> resIds = aGroupService.getResIdsByGroupId(id);
        //将resIds使用stream转换为String[]
        String[] resIdArr = resIds.toArray(new String[resIds.size()]);
        return success(resIdArr);
    }

    @PostMapping("/saveGroupRes")
    @Operation(summary = "保存group和res的关系数据")
    public CommonResult<Boolean> saveGroupRes(@RequestBody SaveGroupResReqVO saveGroupResReqVO) {
        aGroupService.saveGroupRes(saveGroupResReqVO);
        return success(true);
    }
}
