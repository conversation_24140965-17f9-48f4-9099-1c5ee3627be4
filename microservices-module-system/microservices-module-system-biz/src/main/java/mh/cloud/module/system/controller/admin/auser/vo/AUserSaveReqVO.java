package mh.cloud.module.system.controller.admin.auser.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 个人设置新增/修改 Request VO")
@Data
public class AUserSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "9967")
    private String id;

    @Schema(description = "姓名", example = "李四")
    private String name;

    @Schema(description = "登录名", example = "李四")
    private String loginName;

    @Schema(description = "工号")
    private String workNo;

    @Schema(description = "密码")
    private String password;

    @Schema(description = "排序")
    private Integer sortIndex;

    @Schema(description = "部门ID", example = "30427")
    private String deptID;

    @Schema(description = "部门名称", example = "张三")
    private String deptName;

    @Schema(description = "部门完整ID", example = "9846")
    private String deptFullID;

    @Schema(description = "兼职部门", example = "32557")
    private String parttimeDeptID;

    @Schema(description = "兼职部门", example = "luohang")
    private String parttimeDeptName;

    @Schema(description = "是否授权")
    private String isAuth;

    private String isDeleted;

    @Schema(description = "删除时间")
    private LocalDateTime deleteTime;

    @Schema(description = "最后登录时间")
    private LocalDateTime lastLoginTime;

    @Schema(description = "最后登录IP")
    private String lastLoginIP;

    @Schema(description = "错误次数", example = "23752")
    private Integer errorCount;

    @Schema(description = "错误时间")
    private LocalDateTime errorTime;

    @Schema(description = "修改时间")
    private LocalDateTime modifyTime;

    @Schema(description = "备注", example = "无")
    private String description;

    @Schema(description = "性别")
    private String sex;

    @Schema(description = "入职日期")
    private LocalDateTime inDate;

    @Schema(description = "离职日期")
    private LocalDateTime outDate;

    @Schema(description = "电话")
    private String phone;

    @Schema(description = "手机")
    private String mobilePhone;

    @Schema(description = "Emai")
    private String email;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "职务")
    private String duties;

    @Schema(description = "生日")
    private LocalDateTime birthday;

    @Schema(description = "默认IP")
    private String clientIp;

    @Schema(description = "签名密码")
    private String signPwd;

    @Schema(description = "子系统编号")
    private String systemCode;

    @Schema(description = "SortIndex1")
    private Integer sortIndex1;

    @Schema(description = "SortIndex2")
    private Integer sortIndex2;

    @Schema(description = "SortIndex3")
    private Integer sortIndex3;

    @Schema(description = "SortIndex4")
    private Integer sortIndex4;

    @Schema(description = "SortIndex5")
    private Integer sortIndex5;

    @Schema(description = "是否接收手机短信")
    private String acceptMobileMsg;

    @Schema(description = "状态", example = "2")
    private String status;

    @Schema(description = "身份证照片")
    private byte[] iDCardImg;

    @Schema(description = "用户照片")
    private byte[] userImg;

    @Schema(description = "身份证反面")
    private byte[] iDCardImgF;

    @Schema(description = "Ext1")
    private String ext1;

    @Schema(description = "OfficeId", example = "18634")
    private String officeId;

    @Schema(description = "OfficeName", example = "李四")
    private String officeName;

    @Schema(description = "ucmobile")
    private String ucmobile;

    @Schema(description = "uctitle")
    private String uctitle;

    @Schema(description = "ucshort")
    private String ucshort;

    @Schema(description = "ucmail")
    private String ucmail;

    @Schema(description = "thirdPartMail")
    private String thirdPartMail;

    @Schema(description = "voipNumber")
    private String voipNumber;

    @Schema(description = "OutKey")
    private String outKey;

    @Schema(description = "身份证号")
    private String idCard;

    @Schema(description = "用户类型", example = "2")
    private String userType;

    @Schema(description = "isAuthority")
    private String isAuthority;

    @Schema(description = "民族")
    private String nation;

    @Schema(description = "曾用名", example = "张三")
    private String beforeName;

    @Schema(description = "政治面貌")
    private String politics;

    @Schema(description = "OfficeFullID", example = "22573")
    private String officeFullID;

}