package mh.cloud.module.system.controller.admin.portal.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import mh.cloud.module.system.dal.dataobject.auser.AUserDO;
import mh.cloud.module.system.service.auser.AUserService;
import mh.cloud.module.system.service.db.SQLHelper;
import org.json.JSONArray;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static mh.cloud.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

@Service
@Slf4j
public class CommonServiceImpl implements CommonService {
    private static final String GetSysParameter = "select ParamValue from C_SystemParameter where Code='{0}' and (IsDeleted<>'1' or IsDeleted is null)";

    @Resource
    private AUserService aUserService;
    private String GetSysScript = "select ID, Name, Code, ConnName, SQL, ExecType, SystemCode, Description, CreateUser, CreateUserID,CreateTime, ModifyUser, ModifyUserID, ModifyTime, IsDeleted, CategoryID, CategoryName from C_SystemScript where Code in ('{0}') or Name in ('{1}')";

    @Override
    public List<Map<String, Object>> execSystemScript(String code, String execData) {
        if (StrUtil.isEmptyIfStr(code)) {
            throw new RuntimeException("编号参数不能为空。");
        }
        try (SQLHelper core = SQLHelper.createSqlHelper("Core")) {
           /* Map<String, Object> map = core.selectFirstRow("SELECT ConnName, SQL FROM C_SystemScript WHERE Code = ?", code);
            String connName = map.get("ConnName") == null ? "" : map.get("ConnName").toString();
*/
            String sql = GetSysScript;
//            private String GetSysScript = "select ID, Name, Code, ConnName, SQL, ExecType, SystemCode, Description, CreateUser, CreateUserID,CreateTime, ModifyUser, ModifyUserID, ModifyTime, IsDeleted, CategoryID, CategoryName from C_SystemScript where Code in ('{0}') or Name in ('{1}')";

            sql = SQLHelper.format(sql, code.replace(",", "','"), code.replace(",", "','"));
            Page<Map<String, Object>> dt = core.page(sql, 1, 999);
            if (dt.getRecords().size() < 1) {
                throw new RuntimeException("未找到执行的数据。");
            }

            String getSQL = dt.getRecords().get(0).get("SQL") == null ? "" : dt.getRecords().get(0).get("SQL").toString();
            String connName = dt.getRecords().get(0).get("ConnName") == null ? "" : dt.getRecords().get(0).get("ConnName").toString();
            Map<String, Object> rtnDic = new HashMap<>();

            AUserDO aUser = aUserService.getAUser(getLoginUserId());

            log.info("执行脚本：" + code);

            if (StrUtil.isNotEmpty(execData) && !"[]".equals(execData)) {
                JSONArray objects = new JSONArray(execData);
                log.info(objects.toString());
                for (Object obj : objects) {
                    //获取obj中得所有key和value
                    Map<String, Object> map = JSON.parseObject(obj.toString());
                    //读取map得key和value
                    for (String key : map.keySet()) {
                        getSQL = getSQL.replace("{" + key + "}", map.get(key) == null ? "" : map.get(key).toString());
                    }
                }

            }
            if (StrUtil.isNotEmpty(getSQL) && StrUtil.isNotEmpty(connName)) {
                try (SQLHelper sqlHelper = SQLHelper.CreateSqlHelper(connName);
                ) {
                    List<Map<String, Object>> maps = sqlHelper.executeReader(getSQL);
                    return maps;
                }
            }
//            for (Map<String, Object> dr : dt.getRecords()) {
//                if (dr.get("ConnName") == null) {
//                    throw new RuntimeException("未找到数据库连接名称。");
//                }
//
//                SQLHelper sqlHelper = SQLHelper.CreateSqlHelper(dr.get("ConnName").toString());
//                sql = dr.get("SQL") == null ? "" : dr.get("SQL").toString();
//                if (StrUtil.isEmptyIfStr(sql)) {
//                    continue;
//                }
//                if (dr.get("ExecType") != null && dr.get("ExecType").toString() == "none") {
//                    sql = SQLHelper.formatString(sql, userDic, null);
//                    for (Map<String, Object> execDic : list) {
//                        sql = SQLHelper.formatString(sql, null, execDic);
//                    }
//                    sqlHelper.executeNonQuery(sql);
//                } else if (dr.get("ExecType") != null && dr.get("ExecType").toString() == "round") {
//                    StringBuilder sb = new StringBuilder();
//                    for (Map<String, Object> dic : list) {
//                        sb.append(SQLHelper.formatString(sql, userDic, dic));
//                        sb.append(";");
//                    }
//                    if (sb != null && StrUtil.isEmptyIfStr(sb.toString())) {
//                        sqlHelper.executeNonQuery(sb.toString());
//                    }
//                } else {
//                    sql = SQLHelper.formatString(sql, userDic, null);
//                    for (Map<String, Object> execDic : list) {
//                        sql = SQLHelper.formatString(sql, null, execDic);
//                    }
//                    Page execDt = sqlHelper.page(sql, 1, 999);
//                    //处理专用数据集
//                }
//
//            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return List.of();
    }


    public static List<Map<String, Object>> GetZYSJBySysCode(String sysScriptCode) {
        String sql = SQLHelper.format("select ID   , SQJS, SQJSName, JJJS, JJJSName, JJYH, JJYHName, LinkName, SrcTable, SrcTableName, LinkID, SQYH, SQYHName from T_Page_ZYSJJ where SysScriptCode='{0}' and Isnull(Isdeleted,0)=0", sysScriptCode);
        SQLHelper _zjsjsqlHelper = SQLHelper.CreateSqlHelper("KDSQLServer_1010163_S_DataManager");
        return _zjsjsqlHelper.executeReader(sql);
    }

    public static Boolean setZYSJData(List<Map<String, Object>> zysj) {
        SQLHelper _coreHelper = SQLHelper.CreateSqlHelper("Core");
        String roleIds;
        String rolesql = "select count(1) as ret from A_GroupRelation where ParentGroupID in ('{0}') and ChildGroupID='{1}'";
        if (zysj != null && zysj.size() > 0) {
            String sqjsName = zysj.get(0).get("SQJSName") != null ? zysj.get(0).get("SQJSName").toString() : "";
            String sqyhName = zysj.get(0).get("SQYHName") != null ? zysj.get(0).get("SQYHName").toString() : "";
            String sqyh = zysj.get(0).get("SQYH") != null ? zysj.get(0).get("SQYH").toString() : "";
            String sqjs = zysj.get(0).get("SQJS") != null ? zysj.get(0).get("SQJS").toString() : "";

            String JJJSName = zysj.get(0).get("JJJSName") != null ? zysj.get(0).get("JJJSName").toString() : "";
            String JJYHName = zysj.get(0).get("JJYHName") != null ? zysj.get(0).get("JJYHName").toString() : "";
            String JJYH = zysj.get(0).get("JJYH") != null ? zysj.get(0).get("JJYH").toString() : "";
            String JJJS = zysj.get(0).get("JJJS") != null ? zysj.get(0).get("JJJS").toString() : "";

            if (StrUtil.isNotEmpty(sqjsName) || StrUtil.isNotEmpty(sqyhName)) {
                if (StrUtil.isNotEmpty(sqyhName) && !sqyh.contains(getLoginUserId())) {
                    return true;
                } else {
                    if (StrUtil.isNotEmpty(sqjsName)) {

                        String[] roleIdsArray = sqjs.split(",");

                        StringBuilder sb = new StringBuilder();
                        for (int i = 0; i < roleIdsArray.length; i++) {
                            if (i > 0) {
                                sb.append("','");
                            }
                            sb.append(roleIdsArray[i]);
                        }
                        roleIds = sb.toString();
                        rolesql = SQLHelper.format(rolesql, roleIds, getLoginUserId());
                        Object ret = _coreHelper.selectFirstRow(rolesql).get("ret");

                        if (ret != null && Integer.parseInt(ret.toString()) == 0) {
                            return true;
                        }
                    }
                }
            } else if (StrUtil.isNotEmpty(JJJSName) || StrUtil.isNotEmpty(JJYHName)) {
                if (StrUtil.isNotEmpty(JJYHName) && JJYH.contains(getLoginUserId())) {
                    return true;
                } else if (StrUtil.isNotEmpty(JJJSName)) {
                    String[] roleIdsArray = JJJS.split(",");

                    StringBuilder sb = new StringBuilder();
                    for (int i = 0; i < roleIdsArray.length; i++) {
                        if (i > 0) {
                            sb.append("','");
                        }
                        sb.append(roleIdsArray[i]);
                    }
                    roleIds = sb.toString();
                    rolesql = SQLHelper.format(rolesql, roleIds, getLoginUserId());
                    Object ret = _coreHelper.selectFirstRow(rolesql).get("ret");

                    if (ret != null && Integer.parseInt(ret.toString()) == 0) {
                        return true;
                    }
                }
            }
            return false;
        }
        return null;
    }

    /**
     * 记录外部访问日志
     *
     * @param user
     * @param ip
     */
    @Override
    public void insertLog(AUserDO user, String ip, String logMsg) {
        LocalDateTime now = LocalDateTime.now();
        try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("Log")) {
            String date = DateUtil.format(now, "yyyy-MM-dd HH:mm:ss");
            logMsg = StrUtil.format(logMsg,now);
            String insertSql = SQLHelper.format("INSERT INTO dbo.Log(UserID,UserName,LoginName,RecordTime,LogMsg,UserIP)" +
                    " VALUES('{0}','{1}','{2}','{3}','{4}','{5}')", user.getId(), user.getName(), user.getLoginName(), date, logMsg, ip);
            sqlHelper.executeNonQuery(insertSql);
        }
    }

    public static void main(String[] args) {
        String a = " [{name:\"test\"},{name3:\"test2\"}]";
        JSONArray objects = new JSONArray(a);
        for (Object o : objects) {
            //将string转map
            Map<String, Object> map = JSON.parseObject(o.toString());
            //读取map得key和value

            for (String key : map.keySet()) {
                System.out.println(key);
                System.out.println(map.get(key));
            }
        }


    }
}

