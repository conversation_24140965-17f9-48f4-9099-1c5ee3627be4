package mh.cloud.module.system.service.portal.impl;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import jakarta.annotation.Resource;
import mh.cloud.module.infra.api.file.FileApi;
import mh.cloud.module.system.controller.admin.portal.V0.HeadAndSign;
import mh.cloud.module.system.dal.dataobject.auser.AUserDO;
import mh.cloud.module.system.dal.dataobject.protal.UserExtsDo;
import mh.cloud.module.system.dal.mysql.auser.AUserMapper;
import mh.cloud.module.system.dal.mysql.portal.UserExtsMapper;
import mh.cloud.module.system.service.db.SQLHelper;
import mh.cloud.module.system.service.portal.UserCenterService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import static mh.cloud.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Service
public class UserCenterServiceImpl implements UserCenterService {

    @Resource
    private AUserMapper aUserMapper;

    @Resource
    private UserExtsMapper userExtsMapper;

    @Resource
    private FileApi fileApi;

    /**
     * 查询用户中心，左侧列表
     */
    @Override
    public List<Map<String, Object>> getLeftTopMenu(String templateCode) {

        String sql = "SELECT *\n" +
                "FROM dbo.T_MH_UserSysNav\n" +
                "WHERE EXISTS (\n" +
                "    SELECT 1\n" +
                "    FROM (SELECT [ParentGroupID] FROM KMYZH_SystemDataBase.[dbo].[V_GroupUser] WHERE [ID] = '" + getLoginUserId() + "') AS roles\n" +
                "    WHERE ',' || SQJS || ',' LIKE '%,' || roles.[ParentGroupID] || ',%'\n" +
                ");";
        try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("OA")) {
            List<Map<String, Object>> maps = sqlHelper.selectRows(sql);
//            List<UserLeftMenu> leftMenus = maps.stream().map(map -> BeanUtil.toBean(map, UserLeftMenu.class)).toList();
//            Map<String, List<UserLeftMenu>> listMap = leftMenus.stream().sorted().collect(Collectors.groupingBy(UserLeftMenu::getNavSort));
            return maps;
        }
    }

    @Override
    public AUserDO getUserInfo() {
        String userId = getLoginUserId();
        AUserDO aUserDO = aUserMapper.selectOne(Wrappers.<AUserDO>lambdaQuery()
                .select(AUserDO::getId, AUserDO::getName, AUserDO::getWorkNo,
                        AUserDO::getBirthday, AUserDO::getEmail,
                        AUserDO::getPhone, AUserDO::getAddress,
                        AUserDO::getMobilePhone,
                        AUserDO::getSex).eq(AUserDO::getId, userId));
        //获取签名
        String signImg = aUserMapper.selectSinggImg(userId);
        String headImageStr = aUserMapper.getUserHeadImageStr(userId);
        aUserDO.setAvatar(headImageStr);
        aUserDO.setSignImg(signImg);
        return aUserDO;
    }

    /**
     * 用户中心修改用户
     */
    @Override
    public Boolean updateUserInfo(HeadAndSign headAndSign) {
        String userId = getLoginUserId();
        if (ObjUtil.isEmpty(userId) || !ObjUtil.equals(headAndSign.getUserId(), userId)) {
            throw new RuntimeException("用户信息异常，请重新登录查看");
        }
        headAndSign.setUserId(userId);
        return null;
    }

    /**
     * 存储用户图片文件数据
     *
     * @param name 文件名
     * @param type Sign or Head
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateHeadAndSign(Map<String, Object> params) throws IOException {
        String fileType = params.get("type").equals("Head") ? "Head" : "Sign";
        MultipartFile file = (MultipartFile) params.get("file");
        String userId = getLoginUserId();
        byte[] nameBytes = params.get("name").toString().getBytes();
        byte[] fileBytes = file.getBytes();
        //数据存储到数据库已字节流形式
        userExtsMapper.update(Wrappers.<UserExtsDo>lambdaUpdate()
                .set(fileType.equals("Head"), UserExtsDo::getHeadPortraitFile, nameBytes)
                .set(fileType.equals("Sign"), UserExtsDo::getSignImgFile, nameBytes)
                .eq(UserExtsDo::getUserID, userId));
        //获取文件扩展名
        return params.get("name").toString();
    }
}
