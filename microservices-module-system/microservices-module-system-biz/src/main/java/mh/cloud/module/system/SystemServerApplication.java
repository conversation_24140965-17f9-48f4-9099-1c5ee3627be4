package mh.cloud.module.system;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.EnableAsync;

import java.io.IOException;
import java.util.Arrays;

/**
 * 项目的启动类
 */
@SpringBootApplication
@EnableFeignClients
@EnableAsync
public class SystemServerApplication extends SpringBootServletInitializer {
    // 重写 configure 方法，支持打包成 war 包
    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        return builder.sources(SystemServerApplication.class);
    }

    public static void main(String[] args) throws IOException {
        SpringApplication.run(SystemServerApplication.class, args);
        System.out.println("=== System 启动成功 (^_^) ===");
    }

}
