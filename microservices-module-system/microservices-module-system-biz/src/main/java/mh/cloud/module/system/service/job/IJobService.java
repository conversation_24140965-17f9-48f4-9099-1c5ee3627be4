package mh.cloud.module.system.service.job;

import com.baomidou.mybatisplus.extension.service.IService;
import mh.cloud.module.system.dal.dataobject.job.SysJob;
import mh.cloud.module.system.dal.dataobject.job.SysJobVo;
import org.quartz.SchedulerException;

import java.util.Date;

/**
 * <p>
 * 定时任务调度表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-15
 */
public interface IJobService extends IService<SysJob> {

    /**
     * 新增定时任务
     *
     * @param job 任务信息
     * @return 处理数量
     */
    boolean insertJob(SysJobVo job);


    /**
     * 更新任务
     *
     * @param job 调度信息
     * @return 结果
     */
     boolean updateJob(SysJobVo job) throws SchedulerException;

    /**
     * 暂停任务
     *
     * @param jobId 调度信息
     * @return 结果
     */
    boolean pauseJob(String jobId);

    /**
     * 恢复任务
     *
     * @param jobId 调度信息
     * @return 结果
     */
    boolean resumeJob(String jobId);


    Boolean deleteJob(String jobId);

    /**
     * 获取下次执行时间
     */
    Date getNextFireTime(String jobId, String jobName);

    /**
     * 立即执行一次
     */
    Boolean trigger(String jobId);

    /**
     * 获取任务详情
     */
    SysJobVo getJob(String jobId);
}
