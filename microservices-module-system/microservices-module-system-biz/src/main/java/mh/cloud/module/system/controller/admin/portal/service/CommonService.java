package mh.cloud.module.system.controller.admin.portal.service;

import mh.cloud.module.system.dal.dataobject.auser.AUserDO;
import org.springframework.scheduling.annotation.Async;

import java.util.List;
import java.util.Map;

public interface CommonService {
    List<Map<String, Object>> execSystemScript(String code, String formData);


    /**
     * 记录外部访问日志
     *
     */
    void insertLog(AUserDO user, String ip, String logMsg);
}
