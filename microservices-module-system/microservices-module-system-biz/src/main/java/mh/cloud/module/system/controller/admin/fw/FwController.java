package mh.cloud.module.system.controller.admin.fw;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jfinal.plugin.activerecord.Record;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.module.system.service.db.SQLHelper;
import mh.cloud.module.system.service.db.SqlService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.sql.ResultSet;
import java.util.List;
import java.util.Map;

import static mh.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "sqlHelper-demo")
@RestController
@RequestMapping("/system/fw")
@Validated
public class FwController {
    @Resource
    private SqlService sqlservice;
/*
    @Resource
    private SQLHelper sqlHelper;
*/
    @GetMapping("/page")
    @Operation(summary = "获得列表配置分页")
    public Page<Map<String, Object>> getListPage(@RequestParam("pageNumber") int pageNumber, @RequestParam("pageSize") int pageSize) {

        SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core");
        List<Map<String, Object>> maps = sqlHelper.executeReader("select * from a_user limit 10");
        System.out.println(maps.size());

        SQLHelper sqlHelper2 = SQLHelper.createSqlHelper("OA");
        List<Map<String, Object>> maps2 = sqlHelper2.executeReader("select * from T_Page_NewsCenterMenu limit 10");
        System.out.println(maps2.size());

//        Page<Map<String, Object>> mapPage = sqlHelper1.ExecuteDataTable("select * from a_user", 1, 10);
//        Page<Map<String, Object>> mapPage2 = sqlHelper1.ExecuteDataTable("select * from a_user", 3, 10);
        Page<Map<String, Object>> mapPage = sqlHelper.ExecuteDataTable("select * from \"A_User\"  where name like concat ('%',?,'%') ", 1, 10,"王");
        Page<Map<String, Object>> mapPage2 = sqlHelper.ExecuteDataTable("select * from a_user", 3, 10);
        sqlHelper.close();
        sqlHelper2.close();


        //循环输出maps中的值
        return mapPage;
        /*Page<Record> list = sqlservice.getFw(pageNumber, pageSize);
        return success(list);*/
    }




}
