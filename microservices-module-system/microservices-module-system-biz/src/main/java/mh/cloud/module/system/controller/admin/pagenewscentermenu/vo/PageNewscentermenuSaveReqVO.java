package mh.cloud.module.system.controller.admin.pagenewscentermenu.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 菜单管理新增/修改 Request VO")
@Data
public class PageNewscentermenuSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20270")
    private String id;

    @Schema(description = "创建人")
    private String createUser;

    @Schema(description = "创建人ID", example = "1589")
    private String createUserID;

    @Schema(description = "创建日期")
    private LocalDateTime createTime;

    @Schema(description = "修改人")
    private String modifyUser;

    @Schema(description = "修改人ID", example = "21043")
    private String modifyUserID;

    @Schema(description = "修改日期")
    private LocalDateTime modifyTime;

    @Schema(description = "流程状态")
    private String flowPhase;

    @Schema(description = "流程步骤")
    private String flowStep;

    @Schema(description = "流程结束日期")
    private LocalDateTime flowCompleteTime;

    @Schema(description = "是否删除")
    private String isDeleted;

    @Schema(description = "状态", example = "2")
    private String status;

    @Schema(description = "所有签字、意见")
    private String signTemp;

    @Schema(description = "部门ID", example = "4078")
    private String deptID;

    @Schema(description = "部门", example = "王五")
    private String deptName;

    @Schema(description = "备注", example = "你说的对")
    private String description;

    @Schema(description = "经手人")
    private String flowHandler;

    @Schema(description = "经手人ID", example = "14074")
    private String flowHandlerID;

    @Schema(description = "是否当前版本")
    private String isCurrent;

    @Schema(description = "版本号")
    private Integer versionNumber;

    @Schema(description = "修改记录大字段")
    private String modifyRecordText;

    @Schema(description = "资讯中心菜单名称", example = "王五")
    private String catalogName;

    @Schema(description = "排序号")
    private String sortIndex;

    @Schema(description = "是否启用")
    private String isEnabled;

    @Schema(description = "设置组织")
    private String departmentInfo;

    @Schema(description = "设置组织名称", example = "张三")
    private String departmentInfoName;

    @Schema(description = "设置平台角色")
    private String roleInfo;

    @Schema(description = "设置平台角色名称", example = "luohang")
    private String roleInfoName;

    @Schema(description = "设置新建角色")
    private String selfBuildRoleInfo;

    @Schema(description = "设置新建角色名称", example = "李四")
    private String selfBuildRoleInfoName;

    @Schema(description = "获取列表数据")
    private String listSql;

    @Schema(description = "获取数据详情")
    private String detailSql;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "序号")
    private String orderNumber;

    @Schema(description = "浏览次数")
    private String viewCountSql;

    @Schema(description = "资讯中心菜单名称", example = "luohang")
    private String menuName;

}