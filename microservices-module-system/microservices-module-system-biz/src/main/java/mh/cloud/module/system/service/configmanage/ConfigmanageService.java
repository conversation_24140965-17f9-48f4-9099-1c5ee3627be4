package mh.cloud.module.system.service.configmanage;

import java.util.*;
import jakarta.validation.*;
import mh.cloud.module.system.controller.admin.configmanage.vo.*;
import mh.cloud.module.system.dal.dataobject.configmanage.ConfigmanageDO;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.pojo.PageParam;

/**
 * 系统配置结构树 Service 接口
 *
 * <AUTHOR>
 */
public interface ConfigmanageService {

    /**
     * 创建系统配置结构树
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createConfigmanage(@Valid ConfigmanageSaveReqVO createReqVO);

    /**
     * 更新系统配置结构树
     *
     * @param updateReqVO 更新信息
     */
    void updateConfigmanage(@Valid ConfigmanageSaveReqVO updateReqVO);

    /**
     * 删除系统配置结构树
     *
     * @param id 编号
     */
    void deleteConfigmanage(String id);

    /**
     * 获得系统配置结构树
     *
     * @param id 编号
     * @return 系统配置结构树
     */
    ConfigmanageDO getConfigmanage(String id);

    /**
     * 获得系统配置结构树分页
     *
     * @param pageReqVO 分页查询
     * @return 系统配置结构树分页
     */
    PageResult<ConfigmanageDO> getConfigmanagePage(ConfigmanagePageReqVO pageReqVO);

}