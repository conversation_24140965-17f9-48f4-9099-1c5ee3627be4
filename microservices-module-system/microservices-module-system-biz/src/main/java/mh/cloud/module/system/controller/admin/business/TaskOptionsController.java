package mh.cloud.module.system.controller.admin.business;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import mh.cloud.framework.common.exception.BaseException;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.module.system.dal.dataobject.job.TaskOptions;
import mh.cloud.module.system.job.core.utils.CronUtils;
import mh.cloud.module.system.service.job.TaskOptionsService;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/quartz/task")
@Tag(name = "后台管理-任务调度接口Task")
public class TaskOptionsController {

    @Resource
    private TaskOptionsService taskOptionsService;


    @PostMapping("/ExecTask")
    @Operation(summary = "执行修改定时任务,对接构件化系统定时任务操作按钮")
    public CommonResult<Object> ExecTask(@RequestParam(name = "ID") String id,
                                         @RequestParam(name = "execType") String execType) {

        Boolean res = false;
        switch (execType) {
            case "UpdateTask":
                res = taskOptionsService.updateTask(id);
                break;
            case "AddTask":
                res = taskOptionsService.addTask(id);
            case "PauseTask":
                // 暂停任务
                res = taskOptionsService.pauseTask(id);
                break;
            case "StartTask":
                res = taskOptionsService.resumeTask(id);
                break;
            case "RunTask":
                res = taskOptionsService.trigger(id);
                break;
            case "RemoveTask":
                res = taskOptionsService.deleteTask(id);
                break;
            default:
                throw new BaseException("无效执行类型");
        }
        return CommonResult.success(res, "操作成功");

    }


    @GetMapping("/getTasks")
    @Operation(summary = "获取调度任务列表")
    public CommonResult<Object> getTaskList(
            @RequestParam(name = "TaskGroup", required = false) String taskGroup,
            @RequestParam(defaultValue = "1") Integer pageNo,
            @RequestParam(defaultValue = "20") Integer pageSize,
            @RequestParam(name = "TaskName", required = false) String taskName,
            @RequestParam(name = "TaskState", required = false) String taskState) {

        return CommonResult.success(taskOptionsService.getTaskList(taskGroup, pageNo, pageSize, taskName, taskState));
    }

    @GetMapping("/getTask")
    @Operation(summary = "获取调度任务详情")
    public CommonResult<TaskOptions> getTask(@RequestParam String id) {

        return CommonResult.success(taskOptionsService.getTask(id));
    }

    @PostMapping("/update")
    @Operation(summary = "修改调度任务")
    public CommonResult<Boolean> updateJob(@RequestBody TaskOptions options) {
        if (!CronUtils.isValid(options.getCron())) {
            return CommonResult.error(400, "更新任务'" + options.getTaskCode() + "'失败，Cron表达式不正确");
        }
        return CommonResult.success(taskOptionsService.updateTask(options.getID()));
    }


    @PostMapping("/init")
    @Operation(summary = "初始化")
    public CommonResult<Boolean> initTask(@RequestBody List<String> taskIds) {

        return CommonResult.success(taskOptionsService.initTask(taskIds));
    }


    /**
     * 恢复定时任务
     */
    @PostMapping("/resume")
    @Operation(summary = "运行定时任务")
    public CommonResult<Boolean> resumeTask(String taskId) {

        return CommonResult.success(taskOptionsService.resumeTask(taskId));
    }

//    /**
//     * 开启定时任务
//     */
//    @PostMapping("/start")
//    @Operation(summary = "开启定时任务")
//    public CommonResult<Boolean> startJob(String jobId) {
//
//        return CommonResult.success(taskOptionsService.startJob(jobId));
//    }


    /**
     * 立即执行一次
     */
    @PostMapping("/trigger")
    @Operation(summary = "立即执行一次")
    public CommonResult<Boolean> trigger(String taskId) {

        return CommonResult.success(taskOptionsService.trigger(taskId));
    }


    /**
     * 暂停定时任务
     */
    @PostMapping("/pause")
    @Operation(summary = "暂停定时任务")
    public CommonResult<Boolean> pauseTask(String taskId) {

        return CommonResult.success(taskOptionsService.pauseTask(taskId));
    }

    /**
     * 暂停定时任务
     */
    @PostMapping("/start")
    @Operation(summary = "重新启动定时任务")
    public CommonResult<Boolean> startTask(String taskId) {

        return CommonResult.success(taskOptionsService.startTask(taskId));
    }

//
//    /**
//     * 停止定时任务
//     */
//    @PostMapping("/stop")
//    @Operation(summary = "停止定时任务")
//    public CommonResult<Boolean> stopJob(String jobId) {
//
//        return CommonResult.success(taskOptionsService.stopTask(jobId));
//    }

    /**
     * 删除定时任务
     */
    @PostMapping("/deleteTask")
    @Operation(summary = "删除定时任务")
    public CommonResult<Boolean> deleteTask(@RequestBody List<String> taskIds) {

        return CommonResult.success(taskOptionsService.deleteTask(taskIds.get(0)));
    }

    @GetMapping("/getTaskNextTimes")
    @Operation(summary = "获取下次执行时间")
    public CommonResult<Date> getNextFireTime(String jobId, String jobGroup) {
        return CommonResult.success(taskOptionsService.getTaskNextTimes(jobId, jobGroup));
    }


}
