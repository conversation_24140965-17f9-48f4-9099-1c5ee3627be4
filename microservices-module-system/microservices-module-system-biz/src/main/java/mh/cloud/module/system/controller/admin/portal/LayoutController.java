package mh.cloud.module.system.controller.admin.portal;

import cn.hutool.json.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.module.system.controller.admin.portal.V0.LayoutSaveReq;
import mh.cloud.module.system.controller.admin.portal.V0.SysLayoutConfigVo;
import mh.cloud.module.system.controller.admin.portal.V0.SysLayoutDataReq;
import mh.cloud.module.system.service.portal.LayoutDataService;
import mh.cloud.module.system.service.portal.LayoutService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "布局模块")
@RestController
@RequestMapping("/system/layout")
@Validated
public class LayoutController {

    @Resource
    private LayoutService layoutService;

    @Resource
    private LayoutDataService layoutDataService;


    @GetMapping("/getLayoutData")
    @Operation(summary = "获取用户布局数据")
    public CommonResult<Object> getLayoutData(@RequestParam("category") String category) {

        return CommonResult.success(layoutDataService.getLayoutData(category));
    }

    @PostMapping("/saveLayoutData")
    @Operation(summary = "保存布局数据")
    public CommonResult<Boolean> saveLayoutData(@RequestBody SysLayoutDataReq sysLayoutReq) {

        return CommonResult.success(layoutDataService.saveLayoutData(sysLayoutReq));
    }

    @PostMapping("/updateLayoutData")
    @Operation(summary = "修改布局数据")
    public CommonResult<Boolean> updateLayoutData(@RequestBody SysLayoutDataReq sysLayoutReq) {

        return CommonResult.success(layoutDataService.updateLayoutData(sysLayoutReq));
    }

    @PostMapping("/delLayoutData")
    @Operation(summary = "删除布局数据")
    public CommonResult<Boolean> deleteLayoutData(String id) {

        return CommonResult.success(layoutDataService.deleteLayoutData(id));
    }

    //    ========================================
    @GetMapping("/getLayout")
    @Operation(summary = "获取用户布局")
    public CommonResult<Object> getLayout(@RequestParam("category") String category) {

        return CommonResult.success(layoutService.getLayout(category));
    }

//    @GetMapping("/getDefaultLayout")
//    @Operation(summary = "获取默认布局")
//    public CommonResult<Object> getDefaultLayout(@RequestParam("category") String category) {
//
//        return CommonResult.success(layoutService.getLayout(category, true));
//    }


    @GetMapping("/getLayoutConfig")
    @Operation(summary = "获取用户布局配置")
    public CommonResult<Object> getLayoutConfig() {

        return CommonResult.success(layoutService.getLayoutConfig());
    }

    @PostMapping("/saveLayoutConfig")
    @Operation(summary = "修改用户布局配置")
    public CommonResult<Object> saveLayoutConfig(@RequestBody SysLayoutConfigVo configVo) {

        return CommonResult.success(layoutService.saveLayoutConfig(configVo));
    }


    @PostMapping("/saveLayoutList")
    @Operation(summary = "保存布局")
    public CommonResult<Object> saveLayout(@RequestBody LayoutSaveReq layoutSaveReq) {

        return CommonResult.success(layoutService.saveOrUpdateLayout(layoutSaveReq));
    }


    @PostMapping("/delLayout")
    @Operation(summary = "删除布局数据")
    public CommonResult<Boolean> deleteLayout(@RequestBody List<String> ids) {

        return CommonResult.success(layoutService.deleteLayout(ids));
    }

    @PostMapping("/startDelLayout")
    @Operation(summary = "启用删除布局")
    public CommonResult<Boolean> startDelLayout(@RequestParam String id) {

        return CommonResult.success(layoutService.startDelLayout(id));
    }

}
