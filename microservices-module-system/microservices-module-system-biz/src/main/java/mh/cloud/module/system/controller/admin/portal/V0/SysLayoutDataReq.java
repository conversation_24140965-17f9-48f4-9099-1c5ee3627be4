package mh.cloud.module.system.controller.admin.portal.V0;

import cn.hutool.json.JSONObject;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SysLayoutDataReq {

    private String id;


    private String dataId;

    /**
     * 布局名称
     */

    private String name;

    /**
     * 布局标志
     */

    private String sign;

    /**
     * 数据内容
     */

    private JSONObject layoutJson;


    private JSONObject itemJson;

    /**
     * 创建时间
     */

    private LocalDateTime createTime;

    /**
     * 修改时间
     */

    private LocalDateTime updateTime;


    //组件Icon
    private String icon;

    //组件分类
    private String category;

    //拖拽组件标识
    private String itemId;

    private String defaultLayout;


    /**
     * 租户id
     */

    private String tenantId;

}
