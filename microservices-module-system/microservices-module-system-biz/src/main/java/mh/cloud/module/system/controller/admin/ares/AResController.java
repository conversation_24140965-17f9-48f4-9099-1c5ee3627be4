package mh.cloud.module.system.controller.admin.ares;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.system.SystemUtil;
import mh.cloud.framework.security.core.LoginUser;
import mh.cloud.module.system.dal.dataobject.ares.TreeNode;
import mh.cloud.module.system.dal.dataobject.ares.TreeNode2;
import org.json.JSONObject;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;
import java.util.stream.Collectors;

import mh.cloud.framework.common.pojo.PageParam;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.framework.common.util.object.BeanUtils;
import static mh.cloud.framework.common.pojo.CommonResult.success;

import mh.cloud.framework.excel.core.util.ExcelUtils;

import mh.cloud.framework.apilog.core.annotation.ApiAccessLog;
import static mh.cloud.framework.apilog.core.enums.OperateTypeEnum.*;
import static mh.cloud.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import static mh.cloud.framework.security.core.util.SecurityFrameworkUtils.getLoginUserDeptId;

import mh.cloud.module.system.controller.admin.ares.vo.*;
import mh.cloud.module.system.dal.dataobject.ares.AResDO;
import mh.cloud.module.system.service.ares.AResService;

@Tag(name = "管理后台 - 资源管理")
@RestController
@RequestMapping("/system/A-res")
@Validated
public class AResController {

    @Resource
    private AResService aResService;

    @PostMapping("/create")
    @Operation(summary = "创建资源管理")
    @PreAuthorize("@ss.hasPermission('system:A-res:create')")
    public CommonResult<String> createARes(@Valid @RequestBody AResSaveReqVO createReqVO) {
        return success(aResService.createARes(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新资源管理")
    @PreAuthorize("@ss.hasPermission('system:A-res:update')")
    public CommonResult<Boolean> updateARes(@Valid @RequestBody AResSaveReqVO updateReqVO) {
        aResService.updateARes(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除资源管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:A-res:delete')")
    public CommonResult<Boolean> deleteARes(@RequestParam("id") String id) {
        aResService.deleteARes(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得资源管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:A-res:query')")
    public CommonResult<AResDO> getARes(@RequestParam("id") String id) {
        AResDO aRes = aResService.getARes(id);
        return success(aRes);
    }

    @GetMapping("/list")
    @Operation(summary = "获得资源管理列表")
    @PreAuthorize("@ss.hasPermission('system:A-res:query')")
    public CommonResult<List<AResRespVO>> getAResList(@Valid AResListReqVO listReqVO) {
        List<AResDO> list = aResService.getAResList(listReqVO);
        return success(BeanUtils.toBean(list, AResRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出资源管理 Excel")
    @PreAuthorize("@ss.hasPermission('system:A-res:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAResExcel(@Valid AResListReqVO listReqVO,
              HttpServletResponse response) throws IOException {
        List<AResDO> list = aResService.getAResList(listReqVO);
        // 导出 Excel
        ExcelUtils.write(response, "资源管理.xls", "数据", AResRespVO.class,
                        BeanUtils.toBean(list, AResRespVO.class));
    }

    @GetMapping("/getResTree")
    @Operation(summary = "获得资源管理树")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:A-res:query')")
    public CommonResult<List<TreeNode>> getResTree(@RequestParam("id") String id, @RequestParam(value = "name",required = false) String name) {
        LoginUser loginUser = getLoginUser();
        List<String> list = new ArrayList<>();
        List<TreeNode> resTree = aResService.getResTree(id, list, name);
        return success(resTree);
    }

    @GetMapping("/getResTreeLazy")
    @Operation(summary = "获得资源管理树")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:A-res:query')")
    public CommonResult<List<TreeNode>> getResTreeLazy(@RequestParam("id") String id) {
        List<TreeNode> resTree = aResService.getResTreeLazy(id);
        return success(resTree);
    }


    @GetMapping("/getResTree2")
    @Operation(summary = "获得资源管理树")
    public CommonResult<String> getResTree(@Valid  AResListReqVO listReqVO) {
        long s = System.currentTimeMillis();
        List<AResDO> list = aResService.getAResList2(listReqVO);
        List<TreeNode2> collect = list.stream()
                .map(a -> new TreeNode2(a.getId(), a.getName(),a.getParentID()))
                .collect(Collectors.toList());
        long e = System.currentTimeMillis();
        List<TreeNode2> resTree = aResService.buildTree(collect);
        //移除restree中的parenId及子节点 孙子节点的parentId
        setParentIdNull(resTree);
        long b = System.currentTimeMillis();
//        return success(resTree);
        return success(resTree.toString());
//        return success(JSONUtil.toJsonStr(resTree));


    }

    private static void setParentIdNull(List<TreeNode2> resTree) {
        resTree.stream().forEach(item -> {
            if (item.getParentId() != null) {
                item.setParentId(null);
            }
            if(IterUtil.isNotEmpty(item.getChildren())){
                setParentIdNull(item.getChildren());
            }
        });
    }
}
