package mh.cloud.module.system.controller.admin.tenant;

import cn.hutool.core.util.ObjUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import mh.cloud.framework.apilog.core.annotation.ApiAccessLog;
import mh.cloud.framework.common.exception.ServiceException;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.framework.common.pojo.PageParam;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.util.object.BeanUtils;
import mh.cloud.framework.excel.core.util.ExcelUtils;
import mh.cloud.module.system.controller.admin.tenant.vo.tenant.TenantPageReqVO;
import mh.cloud.module.system.controller.admin.tenant.vo.tenant.TenantRespVO;
import mh.cloud.module.system.controller.admin.tenant.vo.tenant.TenantSaveReqVO;
import mh.cloud.module.system.controller.admin.tenant.vo.tenant.TenantSimpleRespVO;
import mh.cloud.module.system.dal.dataobject.tenant.TenantDO;
import mh.cloud.module.system.service.tenant.TenantService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import static mh.cloud.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static mh.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 租户")
@RestController
@RequestMapping("/system/tenant")
public class TenantController {

    @Resource
    private TenantService tenantService;

    @GetMapping("/get-id-by-name")
    @PermitAll
    @Operation(summary = "使用租户名，获得租户编号", description = "登录界面，根据用户的租户名，获得租户编号")
    @Parameter(name = "name", description = "租户名", required = true, example = "1024")
    public CommonResult<Long> getTenantIdByName(@RequestParam("name") String name) {
        TenantDO tenant = tenantService.getTenantByName(name);
        return success(tenant != null ? tenant.getId() : null);
    }

    @GetMapping("/get-by-website")
    @PermitAll
    @Operation(summary = "使用域名，获得租户信息", description = "登录界面，根据用户的域名，获得租户信息")
    @Parameter(name = "website", description = "域名", required = true, example = "www.iocoder.cn")
    public CommonResult<TenantSimpleRespVO> getTenantByWebsite(@RequestParam("website") String website) {
        TenantDO tenant = tenantService.getTenantByWebsite(website);
        return success(BeanUtils.toBean(tenant, TenantSimpleRespVO.class));
    }

    @PostMapping("/create")
    @Operation(summary = "创建租户")
    @PreAuthorize("@ss.hasPermission('system:tenant:create')")
    public CommonResult<Long> createTenant(@Valid @RequestBody TenantSaveReqVO createReqVO) {
        return success(tenantService.createTenant(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新租户")
    @PreAuthorize("@ss.hasPermission('system:tenant:update')")
    public CommonResult<Boolean> updateTenant(@Valid @RequestBody TenantSaveReqVO updateReqVO) {
        tenantService.updateTenant(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除租户")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:tenant:delete')")
    public CommonResult<Boolean> deleteTenant(@RequestParam("id") Long id) {
        tenantService.deleteTenant(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得租户")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:tenant:query')")
    public CommonResult<TenantRespVO> getTenant(@RequestParam("id") Long id) {
        TenantDO tenant = tenantService.getTenant(id);
        return success(BeanUtils.toBean(tenant, TenantRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得租户分页")
    @PreAuthorize("@ss.hasPermission('system:tenant:query')")
    public CommonResult<PageResult<TenantRespVO>> getTenantPage(@Valid TenantPageReqVO pageVO) {
        PageResult<TenantDO> pageResult = tenantService.getTenantPage(pageVO);
        return success(BeanUtils.toBean(pageResult, TenantRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出租户 Excel")
    @PreAuthorize("@ss.hasPermission('system:tenant:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTenantExcel(@Valid TenantPageReqVO exportReqVO,
                                  HttpServletResponse response) throws IOException {
        exportReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TenantDO> list = tenantService.getTenantPage(exportReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "租户.xls", "数据", TenantRespVO.class,
                BeanUtils.toBean(list, TenantRespVO.class));
    }

    @PostMapping("/initTenant")
    @Operation(summary = "初始化数据库租户字段数据")
    public CommonResult<List<Map<String, Object>>> initTenant(@RequestBody List<String> dataBaseNames) {
        return CommonResult.success(tenantService.initTenant(checkDataBase(dataBaseNames), "ADD"));
    }

    @PostMapping("/delTenantId")
    @Operation(summary = "去除租户字段数据")
    public CommonResult<List<Map<String, Object>>> delTenantId(@RequestBody List<String> dataBaseNames) {
        return CommonResult.success(tenantService.initTenant(checkDataBase(dataBaseNames), "DEL"));
    }

    @GetMapping("/getDataBases")
    @Operation(summary = "获取数据库名称信息")
    public CommonResult<PageResult<Map<String, Object>>> getDataBases(@RequestParam(value = "pageNo", defaultValue = "1") Integer pageNum,
                                                                      @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                                      @RequestParam(value = "name", required = false) String name) {
        return CommonResult.success(tenantService.getDataBases(pageNum, pageSize, name));
    }

    private List<String> checkDataBase(List<String> dataBaseNames) {
        if (ObjUtil.isEmpty(dataBaseNames)) {
            throw new RuntimeException("请选择需要处理数据库");
        }
//        if (dataBaseNames.size() > 1) {
//            throw new ServiceException(501, "暂时只支持（KMYZH_SystemDataBase）Core表");
//        } else if (!dataBaseNames.contains("KMYZH_SystemDataBase") && !dataBaseNames.contains("Core")) {
//            throw new ServiceException(501, "暂时只支持（KMYZH_SystemDataBase）Core表");
//        }
        if (dataBaseNames.contains("KMYZH_SystemDataBase")) {
            dataBaseNames.remove("KMYZH_SystemDataBase");
            if (!dataBaseNames.contains("Core")) {
                dataBaseNames.add("Core");
            }
        }
        return dataBaseNames.stream().distinct().toList();
    }

}
