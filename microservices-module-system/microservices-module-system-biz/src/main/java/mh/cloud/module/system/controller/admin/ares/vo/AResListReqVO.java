package mh.cloud.module.system.controller.admin.ares.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import mh.cloud.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 资源管理列表 Request VO")
@Data
public class AResListReqVO {

    @Schema(description = "ID", example = "21013")
    private String id;

    @Schema(description = "父ID", example = "6956")
    private String parentID;

    @Schema(description = "名称", example = "luohang")
    private String name;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "首页的图标样式")
    private String bootstrapCls;

    @Schema(description = "需求部门")
    private String requireDept;

    @Schema(description = "使用范围")
    private String useScope;

    @Schema(description = "关键词")
    private String keyWord;

    @Schema(description = "特殊用户")
    private String specialUser;

    @Schema(description = "一致显示")
    private Boolean alwaysShow;

    @Schema(description = "权限")
    private String permission;

    @Schema(description = "路径")
    private String path;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "组件")
    private String component;

    @Schema(description = "组件名称", example = "张三")
    private String componentName;

    @Schema(description = "状态", example = "1")
    private Short status;

    @Schema(description = "是否可见")
    private Boolean visible;

    @Schema(description = "存活时间")
    private Boolean keepAlive;

}