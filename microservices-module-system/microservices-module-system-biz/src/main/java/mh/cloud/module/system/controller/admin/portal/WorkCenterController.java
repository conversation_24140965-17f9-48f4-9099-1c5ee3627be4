package mh.cloud.module.system.controller.admin.portal;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletRequest;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.module.system.controller.admin.portal.V0.FuncLogVo;
import mh.cloud.module.system.controller.admin.portal.V0.MyFlowVo;
import mh.cloud.module.system.service.portal.ApplyService;
import mh.cloud.module.system.service.portal.WorkCenterService;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static mh.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "主门户接口")
@RestController
@RequestMapping("/system/Portal/WorkCenter")
public class WorkCenterController {
    @Resource
    private WorkCenterService workCenterService;

    @Resource
    private ApplyService applyService;

    @GetMapping("/LeftTopMenu")
    @Operation(summary = "获得门户网站左边点击排行列表")
    //@PreAuthorize("@ss.hasPermission('system:A-res:query')")
    public CommonResult<List<Map<String, Object>>> getLeftTopMenu() {
        List<Map<String, Object>> list = workCenterService.getLeftTopMenu();
        return success(list);
    }

    @GetMapping("/GetMyWork")
    @Operation(summary = "我的工作")
    @Parameter(name = "type", description = "类型： newtask-我的待办, completetask-我的已办, apply-我的申请, myfile-我的文件, focus-我的关注")
    @Parameter(name = "page", description = "页码")
    @Parameter(name = "pageSize", description = "每页数量")
    //@PreAuthorize("@ss.hasPermission('system:A-res:query')")
    public CommonResult<Object> getMyWork(String type, Integer page, Integer pageSize, Integer key, String activity) {
        Page<Map<String, Object>> data = workCenterService.getMyWork(type, page, pageSize, activity);
        Map<String, Object> map = new HashMap<>();
        map.put("data", data);
        map.put("key", key); //请求唯一标识，确保统一
        return success(map);
    }

    @PostMapping("/WorkSysMenu")
    @Operation(summary = "功能收藏")
    public List<Map<String, Object>> WorkSysMenu() {
        return workCenterService.getMyShortCut();
    }

    @PostMapping("/FocusFlow")
    @Operation(summary = "关注或取关")
    public CommonResult<Object> FocusFlow(@RequestBody MyFlowVo flow) {

        return CommonResult.success(workCenterService.EditMyFocus(flow));
    }

    @PostMapping("/GetMyFocusByID")
    @Operation(summary = "根据关注取待办")
    public CommonResult<Object> GetMyFocusByID(@RequestParam String ID) {

        return CommonResult.success(workCenterService.GetMyFocusByID(ID));
    }

    @PostMapping("/IsOuterForm")
    @Operation(summary = "判断是否是外部表单")
    public CommonResult<Object> IsOuterForm(@RequestParam String flowId) {

        return CommonResult.success(workCenterService.IsOuterForm(flowId));
    }

    @PostMapping("/UpdateTaskNameColor")
    @Operation(summary = "更新任务跳蓝")
    public CommonResult<Object> UpdateTaskNameColor(String taskId, String formInstanceId) {
        return CommonResult.success(workCenterService.UpdateTaskNameColor(taskId, formInstanceId));
    }

    @PostMapping("/GetGWSTaskUrl")
    @Operation(summary = "对老系统任务进行处理")
    public CommonResult<Object> GetGWSTaskUrl(@RequestParam("TaskID") String taskId) {
        return CommonResult.success(workCenterService.GetGWSTaskUrl(taskId));
    }

    @GetMapping("/GetFileToken")
    @Operation(summary = "获取文件Uid")
    public CommonResult<String> GetFileToken(@RequestParam("fileId") String fileName) {
        return CommonResult.success(workCenterService.GetFileToken(fileName));
    }


    @GetMapping("DownLoadLogSql")
    @Operation(summary = "下载日志sql")
    @PermitAll
    public String DownLoadLogSql(Map<String, Object> file, @RequestParam String optnType,
                                 @RequestParam String userIp, @RequestParam String userId, @RequestParam String url) {
        return workCenterService.DownLoadLogSql(file, optnType, userIp, userId, url);
    }

    /**
     * 功能使用日志
     */
    @PostMapping("/FuncLog")
    @Operation(summary = "功能使用日志")
    public CommonResult<Boolean> FuncLog(@RequestBody FuncLogVo funcLogVo, HttpServletRequest request) {
        String clientIp = request.getRemoteAddr();
        funcLogVo.setClientIp(clientIp);
        return CommonResult.success(workCenterService.FuncLog(funcLogVo));
    }

    /**
     * 获取Guid 意见反馈需要
     */
    @GetMapping("/GetGuid")
    @Operation(summary = "获取Guid")
    public CommonResult<String> getGuid() {
        UUID uuid = UUID.randomUUID();
        return CommonResult.success(uuid.toString());
    }

    /**
     * 温馨提示列表
     */
    @GetMapping("/GetWarmPromptList")
    @Operation(summary = "温馨提示列表")
    public CommonResult<Page<Map<String, Object>>> GetWarmPromptList(@RequestParam(defaultValue = "1") Integer page,
                                                                     @RequestParam(defaultValue = "50") Integer pageSize) {
        return CommonResult.success(workCenterService.GetWarmPromptList(page, pageSize));
    }

    /**
     * 温馨提示详情
     */
    @GetMapping("/GetWarmPromptDetail")
    @Operation(summary = "温馨提示详情")
    public CommonResult<Map<String, Object>> GetWarmPromptDetail(@RequestParam(name = "id") String id) {
        return CommonResult.success(workCenterService.GetWarmPromptDetail(id));
    }

}
