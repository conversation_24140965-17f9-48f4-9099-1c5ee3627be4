package mh.cloud.module.system.controller.admin.auser.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import mh.cloud.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static mh.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 个人设置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AUserPageReqVO extends PageParam {

    private String name;

    @Schema(description = "部门id")
    private String deptID;

    @Schema(description = "登录名", example = "李四")
    private String loginName;

    @Schema(description = "工号")
    private String workNo;

    @Schema(description = "部门名称", example = "张三")
    private String deptName;

    @Schema(description = "最后登录IP")
    private String lastLoginIP;

    @Schema(description = "错误时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] errorTime;

    @Schema(description = "修改时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] modifyTime;

    @Schema(description = "手机")
    private String mobilePhone;
    @Schema(description = "座机")
    private String phone;

    @Schema(description = "Emai")
    private String email;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "身份证号")
    private String idCard;

    @Schema(description = "isAuthority")
    private String isAuthority;

    private List<String> deptIds;

    private String sortColumn;

    private String sortOrder;
    //公共参数
    private String param;




}