package mh.cloud.module.system.service.job;

import com.baomidou.mybatisplus.extension.service.IService;
import mh.cloud.module.system.dal.dataobject.job.TaskOptions;

import java.util.Date;
import java.util.List;

public interface TaskOptionsService extends IService<TaskOptions> {
    /**
     * 获取调度任务列表
     *
     * @return list
     */
    Object getTaskList(String taskGroup, Integer pageNo, Integer pageSize, String taskName, String taskState);

    TaskOptions getTask(String id);

    /**
     * 初始化调度任务
     */
    Boolean initTask(List<String> jobIds);

    /**
     * 运行恢复定时任务
     */
    Boolean resumeTask(String jobId);

    /**
     * 立即执行一次
     */
    Boolean trigger(String jobId);

    /**
     * 暂停定时任务
     */
    Boolean pauseTask(String jobId);

    /**
     * 停止
     */
    Boolean stopTask(String jobId);

    /**
     * 删除定时任务
     */
    Boolean deleteTask(String taskId);

    /**
     * 添加定时任务
     */
    Boolean addTask(String taskId);

    /**
     * 修改定时任务
     */
    Boolean updateTask(String taskId);

    /**
     * 开启定时任务
     */
    Boolean startJob(String jobId);

    Date getTaskNextTimes(String jobId, String jobGroup);

    Boolean startTask(String taskId);
}
