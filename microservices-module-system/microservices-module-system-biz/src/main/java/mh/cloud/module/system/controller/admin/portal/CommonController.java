package mh.cloud.module.system.controller.admin.portal;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import lombok.extern.slf4j.Slf4j;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.module.system.controller.admin.portal.V0.ExecSystemScriptReq;
import mh.cloud.module.system.controller.admin.portal.service.CommonService;
import mh.cloud.module.system.service.db.SQLHelper;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Tag(name = "Common")
@RestController
@RequestMapping("/BasicApplication/Common")
@Validated
@Slf4j
public class CommonController {
    @Resource
    private CommonService commonService;

    @PostMapping("/ExecSystemScript")
    @Operation(summary = "执行系统脚本")
    public Object ExecSystemScript(String code, String ExecData) {
        List<Map<String, Object>> map = commonService.execSystemScript(code, ExecData);
        return map;
    }

    @PostMapping("/ExecSystemScript2")
    @Operation(summary = "执行系统脚本2")
    public Object ExecSystemScript2(@RequestBody ExecSystemScriptReq body) {
        List<Map<String, Object>> map = commonService.execSystemScript(body.getCode(), body.getExecData());
        return map;
    }

    @GetMapping("/getLoginImage")
    @Operation(summary = "登录界面背景图")
    @PermitAll
    public CommonResult<List<Map<String, Object>>> GetLoginImage() {
        String sql = "SELECT * FROM oa.dbo.T_Page_LoginBgImg a WHERE (a.IsDeleted <> 1 OR a.IsDeleted IS NULL) and a.show = 1 order by a.sort asc";
        sql = SQLHelper.format(sql);
        List<Map<String, Object>> filterList = new ArrayList<>();
        try (SQLHelper core = SQLHelper.CreateSqlHelper("OA")) {
            List<Map<String, Object>> maps = core.selectRows(sql);
            for (Map<String, Object> map : maps) {
                Map<String, Object> filterMap = new HashMap<>();
                filterMap.put("img", map.get("img"));
                filterMap.put("ID", map.get("ID"));
                filterList.add(filterMap);
            }
            return CommonResult.success(filterList);
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }
}
