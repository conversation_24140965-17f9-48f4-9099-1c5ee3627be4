package mh.cloud.module.system.service.portal.impl;

import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import mh.cloud.framework.security.core.LoginUser;
import mh.cloud.framework.security.core.util.SecurityFrameworkUtils;
import mh.cloud.module.system.controller.admin.portal.V0.FuncLogVo;
import mh.cloud.module.system.controller.admin.portal.V0.PageLogVo;
import mh.cloud.module.system.service.db.SQLHelper;
import mh.cloud.module.system.service.portal.KHIDIService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 门户网站
 */

@Service
@Slf4j
public class KHIDIServiceImpl implements KHIDIService {

    @Override
    public boolean PageLog(PageLogVo pageLogVo) {

        SQLHelper sqlHelper = null;
        String sql = null;
        try {
            if (StringUtil.isNotEmpty(pageLogVo.getId())) {
                sql = "update I_PublicInformation set ReadCount=isnull(readcount,0)+1  where ID= ?";
                sqlHelper = SQLHelper.createSqlHelper("Core");
                sqlHelper.executeNonQuery(sql, pageLogVo.getId());
            }
            UUID uuid = UUID.randomUUID();
            String CreateUser = "";
            String CreateUserID = "";
            String WorkNo = "";
            // 获取当前登录用户
            LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
            if (loginUser != null) {
                CreateUser = loginUser.getInfo().get("nickname");
                CreateUserID = loginUser.getId();
                WorkNo = loginUser.getInfo().get("WorkNo");
            } else {
                WorkNo = pageLogVo.getWorkno();
            }
            //获取当前时间
            LocalDateTime now = LocalDateTime.now();

            sql = "insert into  dbo.T_DevOps_PageVisitLog([ID],[CreateUser],[CreateUserID],[WorkNo],[CreateTime],[PageName],[PageUrl],[SysTag],[ClientIP]) \n" +
                    "                    VALUES (?,?,?,?,?,?,?,?,?)";
            sqlHelper = SQLHelper.createSqlHelper("Log");
            return sqlHelper.executeNonQuery(sql,
                    uuid,
                    CreateUser,
                    CreateUserID,
                    WorkNo,
                    now,
                    pageLogVo.getPagename(),
                    pageLogVo.getPageurl(),
                    pageLogVo.getTag(),
                    pageLogVo.getClientIp()) > 0;

        } catch (Exception e) {
            log.error("getNewTask error", e);
            return false;
        } finally {
            sqlHelper.close();
        }
    }

    @Override
    public boolean FuncLog(FuncLogVo funcLogVo) {
        SQLHelper sqlHelper = null;
        try {
            UUID uuid = UUID.randomUUID();

            LocalDateTime now = LocalDateTime.now();

            String CreateUser = "";
            String CreateUserID = "";
            String WorkNo = "";
            // 获取当前登录用户
            LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
            if (loginUser != null) {
                CreateUser = loginUser.getInfo().get("nickname");
                CreateUserID = loginUser.getId();
                WorkNo = loginUser.getInfo().get("WorkNo");
            } else {
                WorkNo = funcLogVo.getWorkno();
            }
            String sql = "INSERT INTO dbo.T_Page_FunctionLog " +
                    "(ID,CreateUser,CreateUserID, WorkNo, PageName, PageUrl, FuncName, FuncUrl, ClientIP, CreateTime, MenuId) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            sqlHelper = SQLHelper.createSqlHelper("Log");
            return sqlHelper.executeNonQuery(sql,
                    uuid.toString(),
                    CreateUser,
                    CreateUserID,
                    WorkNo,
                    funcLogVo.getPagename(),
                    funcLogVo.getPageurl(),
                    funcLogVo.getFuncname(),
                    funcLogVo.getFuncurl(),
                    funcLogVo.getClientIp(),
                    now,
                    funcLogVo.getMenuId()) > 0;
        } catch (Exception e) {
            log.error("FuncLog error", e);
            return false;
        } finally {
            if (sqlHelper != null) {
                sqlHelper.close();
            }
        }
    }
}
