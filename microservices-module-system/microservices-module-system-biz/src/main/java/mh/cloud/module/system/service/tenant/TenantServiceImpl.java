package mh.cloud.module.system.service.tenant;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import mh.cloud.framework.common.enums.CommonStatusEnum;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.util.collection.CollectionUtils;
import mh.cloud.framework.common.util.date.DateUtils;
import mh.cloud.framework.common.util.object.BeanUtils;
import mh.cloud.framework.tenant.config.TenantProperties;
import mh.cloud.framework.tenant.core.context.TenantContextHolder;
import mh.cloud.framework.tenant.core.util.TenantUtils;
import mh.cloud.module.system.controller.admin.permission.vo.role.RoleSaveReqVO;
import mh.cloud.module.system.controller.admin.tenant.vo.tenant.TenantPageReqVO;
import mh.cloud.module.system.controller.admin.tenant.vo.tenant.TenantSaveReqVO;
import mh.cloud.module.system.convert.tenant.TenantConvert;
import mh.cloud.module.system.dal.dataobject.permission.MenuDO;
import mh.cloud.module.system.dal.dataobject.permission.RoleDO;
import mh.cloud.module.system.dal.dataobject.tenant.TenantDO;
import mh.cloud.module.system.dal.dataobject.tenant.TenantPackageDO;
import mh.cloud.module.system.dal.mysql.tenant.TenantMapper;
import mh.cloud.module.system.enums.permission.RoleCodeEnum;
import mh.cloud.module.system.enums.permission.RoleTypeEnum;
import mh.cloud.module.system.service.db.SQLHelper;
import mh.cloud.module.system.service.permission.MenuService;
import mh.cloud.module.system.service.permission.PermissionService;
import mh.cloud.module.system.service.permission.RoleService;
import mh.cloud.module.system.service.tenant.handler.TenantInfoHandler;
import mh.cloud.module.system.service.tenant.handler.TenantMenuHandler;
import mh.cloud.module.system.service.user.AdminUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import static java.util.Collections.singleton;
import static mh.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static mh.cloud.module.system.enums.ErrorCodeConstants.*;

/**
 * 租户 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class TenantServiceImpl implements TenantService {

    @SuppressWarnings("SpringJavaAutowiredFieldsWarningInspection")
    @Autowired(required = false) // 由于 microservices.tenant.enable 配置项，可以关闭多租户的功能，所以这里只能不强制注入
    private TenantProperties tenantProperties;

    @Resource
    private TenantMapper tenantMapper;

    @Resource
    private TenantPackageService tenantPackageService;
    @Resource
    @Lazy // 延迟，避免循环依赖报错
    private AdminUserService userService;
    @Resource
    private RoleService roleService;
    @Resource
    private MenuService menuService;
    @Resource
    private PermissionService permissionService;

    @Override
    public List<Long> getTenantIdList() {
        List<TenantDO> tenants = tenantMapper.selectList();
        return CollectionUtils.convertList(tenants, TenantDO::getId);
    }

    @Override
    public void validTenant(Long id) {
        TenantDO tenant = getTenant(id);
        if (tenant == null) {
            throw exception(TENANT_NOT_EXISTS);
        }
        if (tenant.getStatus().equals(CommonStatusEnum.DISABLE.getStatus())) {
            throw exception(TENANT_DISABLE, tenant.getName());
        }
        if (DateUtils.isExpired(tenant.getExpireTime())) {
            throw exception(TENANT_EXPIRE, tenant.getName());
        }
    }

    /**
     * 初始化数据库中租户字段
     *
     * @param dataBaseNames 数据库列表
     */
    @Override
    public List<Map<String, Object>> initTenant(List<String> dataBaseNames, String type) {
        String selTablesSql = "SELECT name FROM sys.tables";//查询数据库中所有表数据
        String selIsTenIdSql = "SELECT COUNT(*) AS count FROM SYSCOLUMNS WHERE ID = OBJECT_ID('{0}') AND NAME='tenant_id'";//判断表中是否存在字段
        String setColumns = "ALTER TABLE {0} ADD tenant_id NVARCHAR(50) NOT NULL DEFAULT '1'";//添加字段
        String delColumns = "ALTER TABLE {0} DROP COLUMN tenant_id";//删除字段

        List<Map<String, Object>> dataBaseRes = new ArrayList<>();
        for (String dataBaseName : dataBaseNames) {
            Map<String, Integer> handleTable = new HashMap<>();
            int setNum = 0;
            int notSetNum = 0;
            int errorNum = 0;

            try (SQLHelper sqlHelper = SQLHelper.CreateSqlHelper(dataBaseName)) {

                List<Map<String, Object>> maps = sqlHelper.selectRows(selTablesSql);

                //暂时不处理系统字段，小写表
//                List<Map<String, Object>> mapList = maps.stream().filter(map -> {
//                    char at = map.get("name").toString().charAt(0);
//                    return Character.isUpperCase(at);
//                }).toList();

                for (Map<String, Object> map : maps) {
                    String tableName = (String) map.get("name");
                    try {
                        String format = SQLHelper.format(selIsTenIdSql, tableName);
                        Map<String, Object> objectMap = sqlHelper.selectFirstRow(format);
                        Integer count = (Integer) objectMap.get("count");
                        if (count == 0) {
                            //没有字段，智能添加
                            if (type.equals("ADD")) {
                                sqlHelper.executeNonQuery(SQLHelper.format(setColumns, tableName));
                                handleTable.put(tableName, 1);
                                setNum++;
                            }
                        } else {
                            //有字段，可以删除
                            if (type.equals("DEL")) {
                                sqlHelper.executeNonQuery(SQLHelper.format(delColumns, tableName));
                                handleTable.put(tableName, 1);
                                setNum++;
                            } else {
                                handleTable.put(tableName, 0);
                                notSetNum++;
                            }
                        }
                    } catch (Exception e) {
                        handleTable.put(tableName, -1);
                        errorNum++;
                    }
                }
            }
            Map<String, Object> res = new HashMap<>();
            res.put("dataBase", dataBaseName);
            res.put("setNum", setNum);
            res.put("notSetNum", notSetNum);
            res.put("errorNum", errorNum);
            res.put("data", handleTable);
            dataBaseRes.add(res);
        }
        return dataBaseRes;
    }

    /**
     * 获取数据库信息
     *
     * @return
     */
    @Override
    public PageResult<Map<String, Object>> getDataBases(Integer pageNum, Integer pageSize, String name) {
        PageResult<Map<String, Object>> pageResult = new PageResult<>();
        try (SQLHelper sqlHelper = SQLHelper.CreateSqlHelper("Core")) {
            List<Map<String, Object>> maps = sqlHelper.selectRows("SELECT name FROM sys.databases");
            if (!ObjUtil.isEmpty(name)){
                maps = maps.stream().filter(map->map.get("name").toString().contains(name)).toList();
            }
            List<Map<String, Object>> mapList = maps.stream().skip((long) (pageNum - 1) * pageSize).limit(pageSize).toList();
            pageResult.setTotal((long) maps.size());
            pageResult.setList(mapList);
        }
        return pageResult;

    }

    @Override
    @DSTransactional // 多数据源，使用 @DSTransactional 保证本地事务，以及数据源的切换
    public Long createTenant(TenantSaveReqVO createReqVO) {
        // 校验租户名称是否重复
        validTenantNameDuplicate(createReqVO.getName(), null);
        // 校验租户域名是否重复
        validTenantWebsiteDuplicate(createReqVO.getWebsite(), null);
        // 校验套餐被禁用
        TenantPackageDO tenantPackage = tenantPackageService.validTenantPackage(createReqVO.getPackageId());

        // 创建租户
        TenantDO tenant = BeanUtils.toBean(createReqVO, TenantDO.class);
        tenantMapper.insert(tenant);
        // 创建租户的管理员
        TenantUtils.execute(tenant.getId(), () -> {
            // 创建角色
            Long roleId = createRole(tenantPackage);
            // 创建用户，并分配角色
            String userId = createUser(roleId, createReqVO);
            // 修改租户的管理员
            tenantMapper.updateById(new TenantDO().setId(tenant.getId()).setContactUserId(userId));
        });
        return tenant.getId();
    }

    private String createUser(Long roleId, TenantSaveReqVO createReqVO) {
        // 创建用户
        String userId = userService.createUser(TenantConvert.INSTANCE.convert02(createReqVO));
        // 分配角色
        permissionService.assignUserRole(userId, singleton(roleId));
        return userId;
    }

    private Long createRole(TenantPackageDO tenantPackage) {
        // 创建角色
        RoleSaveReqVO reqVO = new RoleSaveReqVO();
        reqVO.setName(RoleCodeEnum.TENANT_ADMIN.getName()).setCode(RoleCodeEnum.TENANT_ADMIN.getCode())
                .setSort(0).setRemark("系统自动生成");
        Long roleId = roleService.createRole(reqVO, RoleTypeEnum.SYSTEM.getType());
        // 分配权限
        permissionService.assignRoleMenu(roleId, tenantPackage.getMenuIds());
        return roleId;
    }

    @Override
    @DSTransactional // 多数据源，使用 @DSTransactional 保证本地事务，以及数据源的切换
    public void updateTenant(TenantSaveReqVO updateReqVO) {
        // 校验存在
        TenantDO tenant = validateUpdateTenant(updateReqVO.getId());
        // 校验租户名称是否重复
        validTenantNameDuplicate(updateReqVO.getName(), updateReqVO.getId());
        // 校验租户域名是否重复
        validTenantWebsiteDuplicate(updateReqVO.getWebsite(), updateReqVO.getId());
        // 校验套餐被禁用
        TenantPackageDO tenantPackage = tenantPackageService.validTenantPackage(updateReqVO.getPackageId());

        // 更新租户
        TenantDO updateObj = BeanUtils.toBean(updateReqVO, TenantDO.class);
        tenantMapper.updateById(updateObj);
        // 如果套餐发生变化，则修改其角色的权限
        if (ObjectUtil.notEqual(tenant.getPackageId(), updateReqVO.getPackageId())) {
            updateTenantRoleMenu(tenant.getId(), tenantPackage.getMenuIds());
        }
    }

    private void validTenantNameDuplicate(String name, Long id) {
        TenantDO tenant = tenantMapper.selectByName(name);
        if (tenant == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同名字的租户
        if (id == null) {
            throw exception(TENANT_NAME_DUPLICATE, name);
        }
        if (!tenant.getId().equals(id)) {
            throw exception(TENANT_NAME_DUPLICATE, name);
        }
    }

    private void validTenantWebsiteDuplicate(String website, Long id) {
        if (StrUtil.isEmpty(website)) {
            return;
        }
        TenantDO tenant = tenantMapper.selectByWebsite(website);
        if (tenant == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同名字的租户
        if (id == null) {
            throw exception(TENANT_WEBSITE_DUPLICATE, website);
        }
        if (!tenant.getId().equals(id)) {
            throw exception(TENANT_WEBSITE_DUPLICATE, website);
        }
    }

    @Override
    @DSTransactional
    public void updateTenantRoleMenu(Long tenantId, Set<String> menuIds) {
        TenantUtils.execute(tenantId, () -> {
            // 获得所有角色
            List<RoleDO> roles = roleService.getRoleList();
            roles.forEach(role -> Assert.isTrue(tenantId.equals(role.getTenantId()), "角色({}/{}) 租户不匹配",
                    role.getId(), role.getTenantId(), tenantId)); // 兜底校验
            // 重新分配每个角色的权限
            roles.forEach(role -> {
                // 如果是租户管理员，重新分配其权限为租户套餐的权限
                if (Objects.equals(role.getCode(), RoleCodeEnum.TENANT_ADMIN.getCode())) {
                    permissionService.assignRoleMenu(role.getId(), menuIds);
                    log.info("[updateTenantRoleMenu][租户管理员({}/{}) 的权限修改为({})]", role.getId(), role.getTenantId(), menuIds);
                    return;
                }
                // 如果是其他角色，则去掉超过套餐的权限
                Set<String> roleMenuIds = permissionService.getRoleMenuListByRoleId(role.getId());
                roleMenuIds = CollUtil.intersectionDistinct(roleMenuIds, menuIds);
                permissionService.assignRoleMenu(role.getId(), roleMenuIds);
                log.info("[updateTenantRoleMenu][角色({}/{}) 的权限修改为({})]", role.getId(), role.getTenantId(), roleMenuIds);
            });
        });
    }

    @Override
    public void deleteTenant(Long id) {
        // 校验存在
        validateUpdateTenant(id);
        // 删除
        tenantMapper.deleteById(id);
    }

    private TenantDO validateUpdateTenant(Long id) {
        TenantDO tenant = tenantMapper.selectById(id);
        if (tenant == null) {
            throw exception(TENANT_NOT_EXISTS);
        }
        // 内置租户，不允许删除
        if (isSystemTenant(tenant)) {
            throw exception(TENANT_CAN_NOT_UPDATE_SYSTEM);
        }
        return tenant;
    }

    @Override
    public TenantDO getTenant(Long id) {
        return tenantMapper.selectById(id);
    }

    @Override
    public PageResult<TenantDO> getTenantPage(TenantPageReqVO pageReqVO) {
        return tenantMapper.selectPage(pageReqVO);
    }

    @Override
    public TenantDO getTenantByName(String name) {
        return tenantMapper.selectByName(name);
    }

    @Override
    public TenantDO getTenantByWebsite(String website) {
        return tenantMapper.selectByWebsite(website);
    }

    @Override
    public Long getTenantCountByPackageId(Long packageId) {
        return tenantMapper.selectCountByPackageId(packageId);
    }

    @Override
    public List<TenantDO> getTenantListByPackageId(Long packageId) {
        return tenantMapper.selectListByPackageId(packageId);
    }

    @Override
    public void handleTenantInfo(TenantInfoHandler handler) {
        // 如果禁用，则不执行逻辑
        if (isTenantDisable()) {
            return;
        }
        // 获得租户
        TenantDO tenant = getTenant(TenantContextHolder.getRequiredTenantId());
        // 执行处理器
        handler.handle(tenant);
    }

    @Override
    public void handleTenantMenu(TenantMenuHandler handler) {
        // 如果禁用，则不执行逻辑
        if (isTenantDisable()) {
            return;
        }
        // 获得租户，然后获得菜单
        TenantDO tenant = getTenant(TenantContextHolder.getRequiredTenantId());
        Set<String> menuIds;
        if (isSystemTenant(tenant)) { // 系统租户，菜单是全量的
            menuIds = CollectionUtils.convertSet(menuService.getMenuList(), MenuDO::getId);
        } else {
            menuIds = tenantPackageService.getTenantPackage(tenant.getPackageId()).getMenuIds();
        }
        // 执行处理器
        handler.handle(menuIds);
    }

    private static boolean isSystemTenant(TenantDO tenant) {
        return Objects.equals(tenant.getPackageId(), TenantDO.PACKAGE_ID_SYSTEM);
    }

    private boolean isTenantDisable() {
        return tenantProperties == null || Boolean.FALSE.equals(tenantProperties.getEnable());
    }

}
