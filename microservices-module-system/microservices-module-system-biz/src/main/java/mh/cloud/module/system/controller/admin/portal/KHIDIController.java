package mh.cloud.module.system.controller.admin.portal;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.module.system.controller.admin.portal.V0.FuncLogVo;
import mh.cloud.module.system.controller.admin.portal.V0.PageLogVo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "主门户接口")
@RestController
@RequestMapping("/system/KHIDIService")
public class KHIDIController {
    @Resource
    private mh.cloud.module.system.service.portal.KHIDIService KHIDIService;


    @PostMapping("/SysLog/PageLog")
    @Operation(summary = "页面访问日志")
    @Parameter(name = "workno", description = "工号")
    @Parameter(name = "pagename", description = "页面名称")
    @Parameter(name = "pageurl", description = "页面地址")
    @Parameter(name = "tag", description = "系统源：企业门户、数字昆明院、昆明院综合系统、信息院等")
    @Parameter(name = "page", description = "默认约定为更新新闻资讯浏览次数")
    //@PreAuthorize("@ss.hasPermission('system:A-res:query')")
    public CommonResult<Boolean> PageLog(@RequestBody PageLogVo pageLogVo, HttpServletRequest request) {
        String clientIp = request.getRemoteAddr();
        pageLogVo.setClientIp(clientIp);
        return CommonResult.success(KHIDIService.PageLog(pageLogVo));

    }

    @PostMapping("/SysLog/FuncLog")
    @Parameter(name = "workno", description = "工号")
    @Parameter(name = "pagename", description = "页面名称")
    @Parameter(name = "pageurl", description = "页面地址")
    @Parameter(name = "tag", description = "系统源：企业门户、数字昆明院、昆明院综合系统、信息院等")
    @Parameter(name = "page", description = "默认约定为更新新闻资讯浏览次数")
    @Operation(summary = "功能使用日志")
    public CommonResult<Boolean> FuncLog(@RequestBody FuncLogVo funcLogVo, HttpServletRequest request) {
        String clientIp = request.getRemoteAddr();
        funcLogVo.setClientIp(clientIp);
        return CommonResult.success(KHIDIService.FuncLog(funcLogVo));
    }
}
