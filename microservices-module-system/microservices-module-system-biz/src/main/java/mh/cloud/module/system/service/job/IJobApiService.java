package mh.cloud.module.system.service.job;

import com.baomidou.mybatisplus.extension.service.IService;
import mh.cloud.module.system.dal.dataobject.job.JobApi;

import java.util.List;
import java.util.Map;

public interface IJobApiService extends IService<JobApi>{
    /**
     * 获取到JobApi执行列为表，并分组
     */
    Map<String, List<JobApi>> getApiMap(String jobId);


    boolean saveAllApi(List<JobApi> allApi, List<JobApi> apiUrlList, String jobId);
}
