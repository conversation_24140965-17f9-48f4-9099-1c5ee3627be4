package mh.cloud.module.system.service.enumservice;

import mh.cloud.module.system.controller.admin.enumcontroller.vo.EnumDefVO;
import mh.cloud.module.system.controller.admin.enumcontroller.vo.EnumItemVO;
import mh.cloud.module.system.dal.dataobject.enumdo.EnumDefDO;
import mh.cloud.module.system.dal.dataobject.enumdo.EnumItemDO;

import java.util.List;

public interface EnumService {

    List<EnumDefVO> getEnumDefsByCode(String code);

    List<EnumItemVO> getEnumItemsByDefID(String defID);
}
