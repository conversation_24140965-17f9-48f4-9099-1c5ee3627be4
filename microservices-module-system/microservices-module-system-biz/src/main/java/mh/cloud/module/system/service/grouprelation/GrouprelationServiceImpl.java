package mh.cloud.module.system.service.grouprelation;

import jakarta.annotation.Resource;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.util.object.BeanUtils;
import mh.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import mh.cloud.module.system.controller.admin.grouprelation.vo.GrouprelationPageReqVO;
import mh.cloud.module.system.controller.admin.grouprelation.vo.GrouprelationSaveReqVO;
import mh.cloud.module.system.dal.dataobject.grouprelation.GrouprelationDO;
import mh.cloud.module.system.dal.mysql.grouprelation.GrouprelationMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static mh.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static mh.cloud.module.system.enums.ErrorCodeConstants.GROUPRELATION_NOT_EXISTS;

/**
 * 分组内部关联 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class GrouprelationServiceImpl implements GrouprelationService {

    @Resource
    private GrouprelationMapper grouprelationMapper;

    @Override
    public String createGrouprelation(GrouprelationSaveReqVO createReqVO) {
        // 插入
        GrouprelationDO grouprelation = BeanUtils.toBean(createReqVO, GrouprelationDO.class);
        grouprelationMapper.insert(grouprelation);
        // 返回
        return grouprelation.getChildGroupID();
    }

    @Override
    public void updateGrouprelation(GrouprelationSaveReqVO updateReqVO) {
        // 校验存在
        validateGrouprelationExists(updateReqVO.getChildGroupID());
        // 更新
        GrouprelationDO updateObj = BeanUtils.toBean(updateReqVO, GrouprelationDO.class);
        grouprelationMapper.updateById(updateObj);
    }

    @Override
    public void deleteGrouprelation(String id) {
        // 校验存在
        validateGrouprelationExists(id);
        // 删除
        grouprelationMapper.deleteById(id);
    }

    private void validateGrouprelationExists(String id) {
        if (grouprelationMapper.selectById(id) == null) {
            throw exception(GROUPRELATION_NOT_EXISTS);
        }
    }

    @Override
    public GrouprelationDO getGrouprelation(String id) {
        return grouprelationMapper.selectById(id);
    }

    @Override
    public PageResult<GrouprelationDO> getGrouprelationPage(GrouprelationPageReqVO pageReqVO) {
        return grouprelationMapper.selectPage(pageReqVO);
    }

    @Override
    public boolean CheckIsRole(String groupID,String userID){
        var list = grouprelationMapper.selectList(new LambdaQueryWrapperX<GrouprelationDO>().eqIfPresent(GrouprelationDO::getParentGroupID, groupID)
                .eqIfPresent(GrouprelationDO::getChildGroupID, userID));
        return !list.isEmpty();
    };
}
