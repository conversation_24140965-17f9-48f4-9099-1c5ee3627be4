package mh.cloud.module.system.controller.admin.portal.V0;

import java.util.List;
import java.util.Map;

public class CentZxzxMenuDateItem {

    private List<Map<String, Object>> menuData;
    private List<Map<String, Object>> data;
    private List<CentZxzxMenuDateItem> children;

    public List<Map<String, Object>> getData() {
        return data;
    }

    public void setData(List<Map<String, Object>> data) {
        this.data = data;
    }

    public List<CentZxzxMenuDateItem> getChildren() {
        return children;
    }

    public void setChildren(List<CentZxzxMenuDateItem> children) {
        this.children = children;
    }

    public List<Map<String, Object>> getMenuData() {
        return menuData;
    }

    public void setMenuData(List<Map<String, Object>> menuData) {
        this.menuData = menuData;
    }
}