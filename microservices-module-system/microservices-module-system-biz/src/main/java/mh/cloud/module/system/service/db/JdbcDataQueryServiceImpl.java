package mh.cloud.module.system.service.db;

import mh.cloud.module.system.service.db.VO.DbConfig;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

public class JdbcDataQueryServiceImpl {

 

    public static byte[] queryData(String sql) {
        DbConfig dbConfig = new DbConfig();
        List<Map<String, Object>> resultList = new ArrayList<>();
        try (Connection conn = DriverManager.getConnection(dbConfig.getJdbcUrl(), dbConfig.getUsername(), dbConfig.getPassword());
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            while (rs.next()) {
                byte[] pictures = rs.getBytes("HeadPortrait");
                return pictures;
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
        String sql = "select \"HeadPortrait\" from \"A_UserExts\"   where UserID='USER2427'";
        byte[] bytes = queryData(sql);
        //将bytes输出为图片
        String formatName = "jpg"; // 图片格式，例如 "png" 或 "jpg"
        String outputFilePath = "output.jpg"; // 输出文件路径
        try {
            saveByteArrayAsImage(bytes, formatName, outputFilePath);
            System.out.println("图片保存成功：" + outputFilePath);
        } catch (IOException e) {
            System.err.println("保存图片时发生错误: " + e.getMessage());
        }
    }
    public static void saveByteArrayAsImage(byte[] imageBytes, String formatName, String outputFilePath) throws IOException {
        // 将 byte[] 转换为 ByteArrayInputStream
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(imageBytes);

        // ImageIO 读取 ByteArrayInputStream 并转换为 BufferedImage
        BufferedImage bufferedImage = ImageIO.read(byteArrayInputStream);

        if (bufferedImage == null) {
            throw new IOException("无法将字节数组转换为 BufferedImage。请确保字节数组是有效的图片数据。");
        }

        // 将 BufferedImage 保存为文件
        File outputFile = new File(outputFilePath);
        boolean result = ImageIO.write(bufferedImage, formatName, outputFile);

        if (!result) {
            throw new IOException("无法将 BufferedImage 写入文件。请检查格式名称是否正确。");
        }
    }


}
