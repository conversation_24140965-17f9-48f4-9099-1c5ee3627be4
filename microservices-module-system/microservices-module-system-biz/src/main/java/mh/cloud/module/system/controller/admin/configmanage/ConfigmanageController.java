package mh.cloud.module.system.controller.admin.configmanage;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import mh.cloud.framework.common.pojo.PageParam;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.framework.common.util.object.BeanUtils;
import static mh.cloud.framework.common.pojo.CommonResult.success;

import mh.cloud.framework.excel.core.util.ExcelUtils;

import mh.cloud.framework.apilog.core.annotation.ApiAccessLog;
import static mh.cloud.framework.apilog.core.enums.OperateTypeEnum.*;

import mh.cloud.module.system.controller.admin.configmanage.vo.*;
import mh.cloud.module.system.dal.dataobject.configmanage.ConfigmanageDO;
import mh.cloud.module.system.service.configmanage.ConfigmanageService;

@Tag(name = "管理后台 - 系统配置结构树")
@RestController
@RequestMapping("/system/configmanage")
@Validated
public class ConfigmanageController {

    @Resource
    private ConfigmanageService configmanageService;

    @PostMapping("/create")
    @Operation(summary = "创建系统配置结构树")
    @PreAuthorize("@ss.hasPermission('system:configmanage:create')")
    public CommonResult<String> createConfigmanage(@Valid @RequestBody ConfigmanageSaveReqVO createReqVO) {
        return success(configmanageService.createConfigmanage(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新系统配置结构树")
    @PreAuthorize("@ss.hasPermission('system:configmanage:update')")
    public CommonResult<Boolean> updateConfigmanage(@Valid @RequestBody ConfigmanageSaveReqVO updateReqVO) {
        configmanageService.updateConfigmanage(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除系统配置结构树")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:configmanage:delete')")
    public CommonResult<Boolean> deleteConfigmanage(@RequestParam("id") String id) {
        configmanageService.deleteConfigmanage(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得系统配置结构树")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:configmanage:query')")
    public CommonResult<ConfigmanageRespVO> getConfigmanage(@RequestParam("id") String id) {
        ConfigmanageDO configmanage = configmanageService.getConfigmanage(id);
        return success(BeanUtils.toBean(configmanage, ConfigmanageRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得系统配置结构树分页")
    @PreAuthorize("@ss.hasPermission('system:configmanage:query')")
    public CommonResult<PageResult<ConfigmanageRespVO>> getConfigmanagePage(@Valid ConfigmanagePageReqVO pageReqVO) {
        PageResult<ConfigmanageDO> pageResult = configmanageService.getConfigmanagePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ConfigmanageRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出系统配置结构树 Excel")
    @PreAuthorize("@ss.hasPermission('system:configmanage:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportConfigmanageExcel(@Valid ConfigmanagePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ConfigmanageDO> list = configmanageService.getConfigmanagePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "系统配置结构树.xls", "数据", ConfigmanageRespVO.class,
                        BeanUtils.toBean(list, ConfigmanageRespVO.class));
    }

}
