package mh.cloud.module.system.service.auth;

import jakarta.validation.Valid;
import mh.cloud.module.system.controller.admin.auth.vo.*;
import mh.cloud.module.system.controller.admin.portal.V0.AuthLoginVo;
import mh.cloud.module.system.dal.dataobject.auser.AUserDO;
import mh.cloud.module.system.enums.logger.LoginLogTypeEnum;

import java.util.List;
import java.util.Map;

/**
 * 管理后台的认证 Service 接口
 * <p>
 * 提供用户的登录、登出的能力
 */
public interface AdminAuthService {

    /**
     * 验证账号 + 密码。如果通过，则返回用户
     *
     * @param username 账号
     * @param password 密码
     * @return 用户
     */
    AUserDO authenticate(String username, String password);

    /**
     * 账号登录
     *
     * @param reqVO 登录信息
     * @return 登录结果
     */
    AuthLoginRespVO login(@Valid AuthLoginReqVO reqVO);

    AuthLoginVo authLogin(@Valid AuthLoginReqVO reqVO);

    /**
     * 账号登录
     *
     * @param reqVO 登录信息
     * @return 登录结果
     */
    AuthLoginRespVO mockLogin(@Valid AuthLoginReqVO reqVO);

    /**
     * 基于 token 退出登录
     *
     * @param token   token
     * @param logType 登出类型
     */
    void logout(String token, Integer logType);

    /**
     * 短信验证码发送
     *
     * @param reqVO 发送请求
     */
    void sendSmsCode(AuthSmsSendReqVO reqVO);

    /**
     * 短信登录
     *
     * @param reqVO 登录信息
     * @return 登录结果
     */
    AuthLoginRespVO smsLogin(AuthSmsLoginReqVO reqVO);

    /**
     * 社交快捷登录，使用 code 授权码
     *
     * @param reqVO 登录信息
     * @return 登录结果
     */
    AuthLoginRespVO socialLogin(@Valid AuthSocialLoginReqVO reqVO);

    /**
     * 构建登录token
     *
     * @param userId   用户ID
     * @param username 用户名
     * @param logType  登录类型
     * @return token
     */
    AuthLoginRespVO createTokenAfterLoginSuccess(String userId, String username, LoginLogTypeEnum logType);

    /**
     * 获取用户外部系统token
     * @param userId 用户ID
     * @return
     */
    String getOutSystemToken(String userId);

    /**
     * 刷新访问令牌
     *
     * @param refreshToken 刷新令牌
     * @return 登录结果
     */
    AuthLoginRespVO refreshToken(String refreshToken);

    //获取用户信息(手机号码，电建通账号)
    Map<String, Object> getUserInfo(String workNo);

    /**
     * 获取用户登录二维码
     *
     * @return 二维码url数据
     */
    Map<String, Object> getQRCode(String key);


    /**
     * 获取登录状态
     *
     * @param uuid 标识信息
     * @return 登录状态
     */
    Map<String, Object> getQRcCodeState(String uuid);

    /**
     * 处理用户回调，获取token数据
     *
     * @param key
     * @param userId
     */
    boolean handleQRCallBack(String key, String userId);


    AuthLoginRespVO writeToken(String decrypt);

    /**
     * 构建登录token
     *
     * @param userId   用户ID
     * @param username 用户名
     * @param logType  登录类型
     * @return token
     */
    AuthLoginRespVO loginWorkNo(String userId, LoginLogTypeEnum logType);

    /**
     * 获取密码修改日志
     *
     * @param workNo
     * @return
     */

    List<Map<String, Object>> getPasswordLog(String workNo);

    /**
     * 添加密码修改日志
     *
     * @param workNo
     * @return
     */
    boolean addPasswordLog(String workNo);

}
