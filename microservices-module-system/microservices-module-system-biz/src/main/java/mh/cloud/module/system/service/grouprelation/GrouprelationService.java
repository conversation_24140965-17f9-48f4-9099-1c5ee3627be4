package mh.cloud.module.system.service.grouprelation;

import java.util.*;
import jakarta.validation.*;
import mh.cloud.module.system.controller.admin.grouprelation.vo.*;
import mh.cloud.module.system.dal.dataobject.grouprelation.GrouprelationDO;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.pojo.PageParam;

/**
 * 分组内部关联 Service 接口
 *
 * <AUTHOR>
 */
public interface GrouprelationService {

    /**
     * 创建分组内部关联
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createGrouprelation(@Valid GrouprelationSaveReqVO createReqVO);

    /**
     * 更新分组内部关联
     *
     * @param updateReqVO 更新信息
     */
    void updateGrouprelation(@Valid GrouprelationSaveReqVO updateReqVO);

    /**
     * 删除分组内部关联
     *
     * @param id 编号
     */
    void deleteGrouprelation(String id);

    /**
     * 获得分组内部关联
     *
     * @param id 编号
     * @return 分组内部关联
     */
    GrouprelationDO getGrouprelation(String id);

    /**
     * 获得分组内部关联分页
     *
     * @param pageReqVO 分页查询
     * @return 分组内部关联分页
     */
    PageResult<GrouprelationDO> getGrouprelationPage(GrouprelationPageReqVO pageReqVO);

    boolean CheckIsRole(String groupID,String userID);
}