package mh.cloud.module.system.controller.admin.auth.vo;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import mh.cloud.framework.common.validation.InEnum;
import mh.cloud.module.system.enums.social.SocialTypeEnum;
import org.checkerframework.checker.units.qual.C;

@Schema(description = "管理后台 - 账号密码登录 Request VO，如果登录并绑定社交用户，需要传递 social 开头的参数")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AuthUpdatePasswordReqVO {

    private String workNo;  //工号用户名
    private String oldPassword; //旧密码
    private String newPassword; //新密码
    private String confirmPassword; //确认密码
    private String codeId;
    private String appWorkNo; //电建通账号
    private String appVerCode; //电建通验证码
    private String mobilePhone; //手机号
    private String phoneCode; //手机验证码
    private String active; //修改方式 1 旧密码修改  2 电建通APP验证码修改  3 手机验证码修改

}
