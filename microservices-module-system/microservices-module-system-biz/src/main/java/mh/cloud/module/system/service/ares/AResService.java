package mh.cloud.module.system.service.ares;

import java.util.*;
import jakarta.validation.*;
import mh.cloud.module.system.controller.admin.ares.vo.*;
import mh.cloud.module.system.dal.dataobject.ares.AResDO;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.pojo.PageParam;
import mh.cloud.module.system.dal.dataobject.ares.TreeNode;
import mh.cloud.module.system.dal.dataobject.ares.TreeNode2;

/**
 * 资源管理 Service 接口
 *
 * <AUTHOR>
 */
public interface AResService {

    /**
     * 创建资源管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createARes(@Valid AResSaveReqVO createReqVO);

    /**
     * 更新资源管理
     *
     * @param updateReqVO 更新信息
     */
    void updateARes(@Valid AResSaveReqVO updateReqVO);

    /**
     * 删除资源管理
     *
     * @param id 编号
     */
    void deleteARes(String id);

    /**
     * 获得资源管理
     *
     * @param id 编号
     * @return 资源管理
     */
    AResDO getARes(String id);

    /**
     * 获得资源管理列表
     *
     * @param listReqVO 查询条件
     * @return 资源管理列表
     */
    List<AResDO> getAResList(AResListReqVO listReqVO);

    /**
     * 获得资源管理树
     *
     * @param id 根节点ID
     * @return 资源管理列表
     */
    List<TreeNode> getResTree(String id, List<String> roleIds,String name);

    /**
     * 获得资源管理树
     * @param id
     * @return
     */
    List<TreeNode> getResTreeLazy(String id);

    List<TreeNode2> buildTree(List<TreeNode2> collect);

    List<AResDO> getAResList2(AResListReqVO listReqVO);
}