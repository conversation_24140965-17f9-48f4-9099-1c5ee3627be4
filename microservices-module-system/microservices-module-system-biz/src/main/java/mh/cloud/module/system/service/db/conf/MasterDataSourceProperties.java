package mh.cloud.module.system.service.db.conf;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
@Data
@Component
@ConfigurationProperties(prefix = "spring.datasource.dynamic.datasource.master")
public class MasterDataSourceProperties {

    private String url;
    private String username;
    private String password;

}
