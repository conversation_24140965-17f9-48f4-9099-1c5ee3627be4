package mh.cloud.module.system.service.otherjobrelation;

import java.util.*;
import jakarta.validation.*;
import mh.cloud.module.system.controller.admin.otherjobrelation.vo.*;
import mh.cloud.module.system.dal.dataobject.otherjobrelation.OtherjobRelationDO;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.pojo.PageParam;

/**
 * 兼职关系 Service 接口
 *
 * <AUTHOR>
 */
public interface OtherjobRelationService {

    /**
     * 创建兼职关系
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createOtherjobRelation(@Valid OtherjobRelationSaveReqVO createReqVO);

    /**
     * 更新兼职关系
     *
     * @param updateReqVO 更新信息
     */
    void updateOtherjobRelation(@Valid OtherjobRelationSaveReqVO updateReqVO);

    /**
     * 删除兼职关系
     *
     * @param id 编号
     */
    void deleteOtherjobRelation(String id);

    /**
     * 获得兼职关系
     *
     * @param id 编号
     * @return 兼职关系
     */
    OtherjobRelationDO getOtherjobRelation(String id);

    /**
     * 获得兼职关系分页
     *
     * @param pageReqVO 分页查询
     * @return 兼职关系分页
     */
    PageResult<OtherjobRelationDO> getOtherjobRelationPage(OtherjobRelationPageReqVO pageReqVO);

}