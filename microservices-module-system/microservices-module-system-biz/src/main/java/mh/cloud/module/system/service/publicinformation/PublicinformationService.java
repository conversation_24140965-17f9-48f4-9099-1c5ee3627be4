package mh.cloud.module.system.service.publicinformation;

import java.util.*;
import jakarta.validation.*;
import mh.cloud.module.system.controller.admin.publicinformation.vo.*;
import mh.cloud.module.system.dal.dataobject.publicinformation.PublicinformationDO;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.pojo.PageParam;

/**
 * 院内信息发布 Service 接口
 *
 * <AUTHOR>
 */
public interface PublicinformationService {

    /**
     * 创建院内信息发布
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createPublicinformation(@Valid PublicinformationSaveReqVO createReqVO);

    /**
     * 更新院内信息发布
     *
     * @param updateReqVO 更新信息
     */
    void updatePublicinformation(@Valid PublicinformationSaveReqVO updateReqVO);

    /**
     * 删除院内信息发布
     *
     * @param id 编号
     */
    void deletePublicinformation(String id);

    /**
     * 获得院内信息发布
     *
     * @param id 编号
     * @return 院内信息发布
     */
    PublicinformationDO getPublicinformation(String id);

    /**
     * 获得院内信息发布分页
     *
     * @param pageReqVO 分页查询
     * @return 院内信息发布分页
     */
    PageResult<PublicinformationDO> getPublicinformationPage(PublicinformationPageReqVO pageReqVO);

}