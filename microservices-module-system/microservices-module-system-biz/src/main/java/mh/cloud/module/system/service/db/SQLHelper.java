package mh.cloud.module.system.service.db;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public interface SQLHelper extends AutoCloseable {
    static SQLHelper createSqlHelper(String connName) {
        try {
            return new SQLHelperImpl(connName);
        } catch (SQLException | ClassNotFoundException e) {
            throw new RuntimeException(e);
        }
    }

    static String format(String sql, Object... params) {
        for (int i = 0; i < params.length; i++) {
            sql = sql.replace("{" + i + "}", params[i] == null ? "" : params[i].toString());
        }
        return sql;
    }

    static String generateUUID() {
        return UUID.randomUUID().toString().toUpperCase();
    }

    static SQLHelper CreateSqlHelper(String connName) {
        try {
            return new SQLHelperImpl(connName);
        } catch (SQLException | ClassNotFoundException e) {
            throw new RuntimeException(e);
        }
    }

    static String formatString(String srcStr, Map<String, Object> userDic, Map<String, Object> dic) {
        if (srcStr == null || srcStr.isEmpty()) {
            return "";
        }
        if (dic == null) {
            dic = new HashMap<>();
        }
        if (userDic == null) {
            userDic = new HashMap<>();
        }
        String rtnStr = srcStr;

        Pattern pattern = Pattern.compile("\\{[0-9a-zA-Z_\\-\\s:.\\u4e00-\\u9faf]*\\}");
        Matcher matcher = pattern.matcher(rtnStr);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String value = matcher.group().trim();
            String replacement = value;

            if (dic.containsKey(value)) {
                replacement = String.valueOf(dic.get(value));
            }

            if (value.startsWith("Date:")) {
                String[] parts = value.split(":");
                String newValue = "";
                if (dic.containsKey(parts[1])) {
                    newValue = String.valueOf(dic.get(parts[1]));
                }
                if (!newValue.isEmpty()) {
                    try {
                        Date dateTime = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).parse(newValue);
                        if (parts.length > 2) {
                            String formatStr = String.join(":", Arrays.copyOfRange(parts, 2, parts.length));
                            newValue = new SimpleDateFormat(formatStr, Locale.getDefault()).format(dateTime);
                        } else {
                            newValue = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(dateTime);
                        }
                    } catch (Exception e) {
                        // Handle exception
                    }
                }
                replacement = newValue;
            } else if (value.startsWith("ConnectionString:")) {
                try {
                    // Assuming SQLHelper is a class that can be created with a connection string
                    SQLHelper sqlh = SQLHelper.createSqlHelper(value.split(":")[1]);
                    replacement = value.split(":")[1];
                } catch (Exception e) {
                    // Handle exception
                }
            } else if (value.startsWith("CurrentUser.")) {
                try {
                    String key = value.split("\\.")[1];
                    replacement = String.valueOf(userDic.get(key));
                } catch (Exception e) {
                    // Handle exception
                }
            } else {
                switch (value) {
                    case "GUID":
                        replacement = UUID.randomUUID().toString();
                        break;
                    case "UserID":
                        replacement = String.valueOf(userDic.get("ID"));
                        break;
                    case "UserName":
                        replacement = String.valueOf(userDic.get("Name"));
                        break;
                    case "UserLoginName":
                        replacement = String.valueOf(userDic.get("LoginName"));
                        break;
                    case "UserDeptID":
                        replacement = String.valueOf(userDic.get("DeptID"));
                        break;
                    case "UserDeptName":
                        replacement = String.valueOf(userDic.get("DeptName"));
                        break;
                    case "UserUnitID":
                        replacement = String.valueOf(dic.get("UserUnitID"));
                        break;
                    case "UserUnitName":
                        replacement = String.valueOf(dic.get("UserUnitName"));
                        break;
                    case "UserDeptFullID":
                        replacement = String.valueOf(userDic.get("DeptFullID"));
                        break;
                    case "UserOfficeID":
                        replacement = String.valueOf(userDic.get("OfficeId"));
                        break;
                    case "UserOfficeName":
                        replacement = String.valueOf(userDic.get("OfficeName"));
                        break;
                    case "UserOfficeFullID":
                        replacement = String.valueOf(userDic.get("OfficeFullID"));
                        break;
                    case "CurrentTime":
                        replacement = new SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(new Date());
                        break;
                    case "CurrentDate":
                        replacement = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(new Date());
                        break;
                    case "Year":
                        replacement = Integer.toString(new Date().getYear() + 1900); // Year in Java is 1900-based
                        break;
                    case "YearMonth":
                        replacement = Integer.toString(new Date().getYear() + 1900) + new SimpleDateFormat("MM").format(new Date());
                        break;
                    case "Month":
                        replacement = new SimpleDateFormat("MM", Locale.getDefault()).format(new Date());
                        break;
                    case "Day":
                        replacement = new SimpleDateFormat("dd", Locale.getDefault()).format(new Date());
                        break;
                    default:
                        replacement = value;
                        break;
                }
            }

            matcher.appendReplacement(sb, replacement);
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    //核心库按照单例模式创建，禁止关闭
/*    SQLHelper core = SQLHelperImpl.createSqlHelper("Core");
    SQLHelper oa = SQLHelperImpl.createSqlHelper("OA");
    SQLHelper log = SQLHelperImpl.createSqlHelper("Log");*/

    Map<String, Object> selectFirstRow(String sql);

    Map<String, Object> selectFirstRow(String sql, Object... params);

    List<Map<String, Object>> executeReader(String sql);

    List<Map<String, Object>> executeReader(String sql, Object... params);

    List<Map<String, Object>> selectRows(String sql, Object... params);

    List<Map<String, Object>> selectRows(String sql);

    void close();

    int executeNonQuery(String sql);

    int executeNonQuery(String sql, Object... params);

    Page<Map<String, Object>> ExecuteDataTable(String sql, int startRowNum, int maxRowNum, Object... params);

    Page<Map<String, Object>> ExecuteDataTable(String sql, int startRowNum, int maxRowNum);

    Page<Map<String, Object>> page(String sql, int startRowNum, int maxRowNum, Object... params);

    Page<Map<String, Object>> page(String sql, int startRowNum, int maxRowNum);

    int count(String sql, Object... id);

    int count(String sql);



}
