package mh.cloud.module.system.service.portal.VO;
import lombok.Data;


@Data
public class WFMyFlow  {

    private String id; // ID (Primary key)
    private String createUser; // CreateUser
    private String createUserID; // CreateUserID
    private java.util.Date createTime; // 注意这里使用了Java的Date类型替代DateTime

    /**
     * 流程ID
     */
    private String flowID; // FlowID

    private String flowName; // FlowName
    private String remark; // Remark

    /**
     * 任务ID
     */
    private String taskID; // TaskID

    private String formInstanceID; // FormInstanceID

    /**
     * 来源系统
     */
    private String sysType; // SysType

    /**
     * 执行地址
     */
    private String execURL; // ExecURL

    /**
     * 当前审批步骤
     */
    private String currentStep; // CurrentStep

    /**
     * 流程状态
     */
    private String flowState; // FlowState

    /**
     * 是否待办任务
     */
    private boolean isNew; // 将_isNew转换为boolean类型，名称改为isNew
}
