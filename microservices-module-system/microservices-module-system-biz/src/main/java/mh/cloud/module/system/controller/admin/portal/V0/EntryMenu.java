package mh.cloud.module.system.controller.admin.portal.V0;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class EntryMenu {

    @JsonProperty("ID")
    private String ID;

    @JsonProperty("Parent")

    private String Parent;
    @JsonProperty("Icon")

    private String Icon;
    @JsonProperty("Img")

    private String Img;
    @JsonProperty("Name")

    private String Name;
    @JsonProperty("Url")

    private String Url;

    //    public String Overview;
//
    private String ServiceDirectory;
//
//    public String ServiceDirectoryUrl;
//
//    public String SortIndex;
//
//    public String CreateTime;
    @JsonProperty("Level")

    private String Level;

    /// <summary>
    /// 获取服务指南数据ID
    /// </summary>
    public String ServiceDirectoryID;

//    public String ExecType;

    /// <summary>
    /// 是否启用服务指南
    /// </summary>
    @JsonProperty("IsEnable")

    private String IsEnable;

    /// <summary>
    /// 子级菜单
    /// </summary>
    @JsonProperty("SubMenu")

    private List<EntryMenu> SubMenu;

    @Override
    public String toString() {
        return "{" +
                "ID='" + ID + '\'' +
                ", Parent='" + Parent + '\'' +
                ", Icon='" + Icon + '\'' +
                ", Img='" + Img + '\'' +
                ", Name='" + Name + '\'' +
                ", Url='" + Url + '\'' +
                ", Level='" + Level + '\'' +
                ", IsEnable='" + IsEnable + '\'' +
                ", SubMenu=" + SubMenu +
                '}';
    }
}
