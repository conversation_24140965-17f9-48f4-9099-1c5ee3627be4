package mh.cloud.module.system.controller.admin.group.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import mh.cloud.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static mh.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 系统角色管理列表 Request VO")
@Data
public class AGroupListReqVO {

    @Schema(description = "名称", example = "菜单名字")
    private String name;

    @Schema(description = "简称", example = "赵六")
    private String shortName;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @TableField(value = "GroupType")
    private String groupType;

}