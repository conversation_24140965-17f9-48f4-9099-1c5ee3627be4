package mh.cloud.module.system.controller.admin.publicinformation.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 院内信息发布 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PublicinformationRespVO {

    @Schema(description = "", requiredMode = Schema.RequiredMode.REQUIRED, example = "22585")
    @ExcelProperty("")
    private String id;

    @Schema(description = "类别", example = "27068")
    @ExcelProperty("类别")
    private String catalogId;

    @Schema(description = "新闻主题")
    @ExcelProperty("新闻主题")
    private String title;

    @Schema(description = "")
    @ExcelProperty("")
    private String content;

    @Schema(description = "")
    @ExcelProperty("")
    private String contentText;

    @Schema(description = "相关附件")
    @ExcelProperty("相关附件")
    private String attachments;

    @Schema(description = "", example = "2629")
    @ExcelProperty("")
    private String receiveDeptId;

    @Schema(description = "", example = "张三")
    @ExcelProperty("")
    private String receiveDeptName;

    @Schema(description = "", example = "10085")
    @ExcelProperty("")
    private String receiveUserId;

    @Schema(description = "", example = "张三")
    @ExcelProperty("")
    private String receiveUserName;

    @Schema(description = "", example = "2355")
    @ExcelProperty("")
    private String deptDoorId;

    @Schema(description = "", example = "luohang")
    @ExcelProperty("")
    private String deptDoorName;

    @Schema(description = "")
    @ExcelProperty("")
    private LocalDateTime expiresTime;

    @Schema(description = "", example = "13745")
    @ExcelProperty("")
    private Integer readCount;

    @Schema(description = "重要度 1重要，0一般")
    @ExcelProperty("重要度 1重要，0一般")
    private String important;

    @Schema(description = "紧急度 1重要，0一般")
    @ExcelProperty("紧急度 1重要，0一般")
    private String urgency;

    @Schema(description = "置顶排序")
    @ExcelProperty("置顶排序")
    private String isTop;

    @Schema(description = "CreateTime")
    @ExcelProperty("CreateTime")
    private LocalDateTime createTime;

    @Schema(description = "CreateUser")
    @ExcelProperty("CreateUser")
    private String createUser;

    @Schema(description = "CreateUserID", example = "10085")
    @ExcelProperty("CreateUserID")
    private String createUserID;

    @Schema(description = "ModifyUser")
    @ExcelProperty("ModifyUser")
    private String modifyUser;

    @Schema(description = "ModifyUserID", example = "20848")
    @ExcelProperty("ModifyUserID")
    private String modifyUserID;

    @Schema(description = "ModifyTime")
    @ExcelProperty("ModifyTime")
    private LocalDateTime modifyTime;

    @Schema(description = "子系统编号")
    @ExcelProperty("子系统编号")
    private String systemCode;

    @Schema(description = "IsSendMobile")
    @ExcelProperty("IsSendMobile")
    private String isSendMobile;

    @Schema(description = "PicFile")
    @ExcelProperty("PicFile")
    private String picFile;

    @Schema(description = "流程状态")
    @ExcelProperty("流程状态")
    private String flowPhase;

    @Schema(description = "流程环节")
    @ExcelProperty("流程环节")
    private String flowStep;

    @Schema(description = "流程结束日期")
    @ExcelProperty("流程结束日期")
    private LocalDateTime flowCompleteTime;

    @Schema(description = "流程执行人")
    @ExcelProperty("流程执行人")
    private String flowHandler;

    @Schema(description = "流程执行人ID", example = "31991")
    @ExcelProperty("流程执行人ID")
    private String flowHandlerID;

    @Schema(description = "信息类型", example = "张三")
    @ExcelProperty("信息类型")
    private String catalogName;

    @Schema(description = "发布部门ID")
    @ExcelProperty("发布部门ID")
    private String publishDept;

    @Schema(description = "发布部门Name", example = "赵六")
    @ExcelProperty("发布部门Name")
    private String publishDeptName;

    @Schema(description = "发布状态")
    @ExcelProperty("发布状态")
    private String publishState;

    @Schema(description = "副标题")
    @ExcelProperty("副标题")
    private String deputyTitle;

    @Schema(description = "新闻摘要")
    @ExcelProperty("新闻摘要")
    private String abStract;

    @Schema(description = "信息大类", example = "2")
    @ExcelProperty("信息大类")
    private String newsType;

    @Schema(description = "IsExtranet")
    @ExcelProperty("IsExtranet")
    private String isExtranet;

    @Schema(description = "EditorCharge")
    @ExcelProperty("EditorCharge")
    private String editorCharge;

    @Schema(description = "IsImage")
    @ExcelProperty("IsImage")
    private String isImage;

    @Schema(description = "ReleaseScope")
    @ExcelProperty("ReleaseScope")
    private String releaseScope;

    @Schema(description = "WordsAuthor")
    @ExcelProperty("WordsAuthor")
    private String wordsAuthor;

    @Schema(description = "图片作者")
    @ExcelProperty("图片作者")
    private String imageAuthor;

    @Schema(description = "发布时间")
    @ExcelProperty("发布时间")
    private LocalDateTime releaseDate;

    @Schema(description = "Source")
    @ExcelProperty("Source")
    private String source;

    @Schema(description = "关键字")
    @ExcelProperty("关键字")
    private String keyWords;

    @Schema(description = "ReleaseScopeName", example = "王五")
    @ExcelProperty("ReleaseScopeName")
    private String releaseScopeName;

    @Schema(description = "IsPic")
    @ExcelProperty("IsPic")
    private String isPic;

    @Schema(description = "ExtranetType", example = "2")
    @ExcelProperty("ExtranetType")
    private String extranetType;

    @Schema(description = "所在部门")
    @ExcelProperty("所在部门")
    private String releaseCompany;

    @Schema(description = "报送人")
    @ExcelProperty("报送人")
    private String enteredBy;

    @Schema(description = "EnteredBy")
    @ExcelProperty("EnteredBy")
    private String videFile;

    @Schema(description = "所在部门名称", example = "李四")
    @ExcelProperty("所在部门名称")
    private String releaseCompanyName;

    @Schema(description = "报送人名称", example = "张三")
    @ExcelProperty("报送人名称")
    private String enteredByName;

    @Schema(description = "CatalogIDName", example = "luohang")
    @ExcelProperty("CatalogIDName")
    private String catalogIDName;

    @Schema(description = "MGCCX")
    @ExcelProperty("MGCCX")
    private String mgccx;

    @Schema(description = "PicFileMINI")
    @ExcelProperty("PicFileMINI")
    private String picFileMINI;

    @Schema(description = "TopNum")
    @ExcelProperty("TopNum")
    private Integer topNum;

    @Schema(description = "第二作者")
    @ExcelProperty("第二作者")
    private String wordsAuthor2;

    @Schema(description = "第三作者")
    @ExcelProperty("第三作者")
    private String wordsAuthor3;

    @Schema(description = "PublishUser")
    @ExcelProperty("PublishUser")
    private String publishUser;

    @Schema(description = "PublishUserName", example = "张三")
    @ExcelProperty("PublishUserName")
    private String publishUserName;

    @Schema(description = "PublishUnit")
    @ExcelProperty("PublishUnit")
    private String publishUnit;

    @Schema(description = "PublishUnitName", example = "赵六")
    @ExcelProperty("PublishUnitName")
    private String publishUnitName;

    @Schema(description = "Isdelete")
    @ExcelProperty("Isdelete")
    private String isdelete;

    @Schema(description = "")
    @ExcelProperty("")
    private LocalDateTime cjrq;

    @Schema(description = "")
    @ExcelProperty("")
    private String otherWebAddres;

    @Schema(description = "第一作者名称", example = "luohang")
    @ExcelProperty("第一作者名称")
    private String authorName;

    @Schema(description = "发布时间")
    @ExcelProperty("发布时间")
    private LocalDateTime sendTime;

    @Schema(description = "信息类型")
    @ExcelProperty("信息类型")
    private String xxlx;

    @Schema(description = "首页图片", example = "https://www.iocoder.cn")
    @ExcelProperty("首页图片")
    private String firstImageUrl;

    @Schema(description = "第一作者")
    @ExcelProperty("第一作者")
    private String author;

    @Schema(description = "第二作者名称", example = "王五")
    @ExcelProperty("第二作者名称")
    private String wordsAuthor2Name;

    @Schema(description = "第三作者名称", example = "赵六")
    @ExcelProperty("第三作者名称")
    private String wordsAuthor3Name;

    @Schema(description = "图片作者名称", example = "李四")
    @ExcelProperty("图片作者名称")
    private String imageAuthorName;

    @Schema(description = "第一作者名称", example = "赵六")
    @ExcelProperty("第一作者名称")
    private String authorNameName;

    @Schema(description = "")
    @ExcelProperty("")
    private String bigFl;

    @Schema(description = "置顶结束时间")
    @ExcelProperty("置顶结束时间")
    private String isTopEndTime;

    @Schema(description = "外键值")
    @ExcelProperty("外键值")
    private String outKey;

    @Schema(description = "外键类型值", example = "1")
    @ExcelProperty("外键类型值")
    private String outType;

    @Schema(description = "文件底稿")
    @ExcelProperty("文件底稿")
    private String firstFile;

    @Schema(description = "文件终稿")
    @ExcelProperty("文件终稿")
    private String endFile;

    @Schema(description = "落款")
    @ExcelProperty("落款")
    private String lk;

    @Schema(description = "正文内容")
    @ExcelProperty("正文内容")
    private String contentZW;

    @Schema(description = "文件日期")
    @ExcelProperty("文件日期")
    private LocalDateTime wJDate;

    @Schema(description = "是否行政")
    @ExcelProperty("是否行政")
    private String isXZ;

}