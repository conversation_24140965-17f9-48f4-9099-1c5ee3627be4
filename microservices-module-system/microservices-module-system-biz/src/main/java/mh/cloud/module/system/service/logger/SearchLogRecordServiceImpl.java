package mh.cloud.module.system.service.logger;

import lombok.extern.slf4j.Slf4j;
import mh.cloud.framework.security.core.LoginUser;
import mh.cloud.framework.security.core.util.SecurityFrameworkUtils;
import mh.cloud.module.system.controller.admin.logger.vo.searchLog.SearchLogCreateReqVO;
import mh.cloud.module.system.service.db.SQLHelper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

import static com.fasterxml.jackson.databind.type.LogicalType.DateTime;
import static mh.cloud.module.system.service.db.SQLHelper.generateUUID;

/**
 * @Author: ytq
 * @Date: 2025/3/14 14:51
 * @Description: 搜索日志记录
 */
@Service
@Validated
@Slf4j
public class SearchLogRecordServiceImpl implements SearchLogRecordService{

    @Override
    public void createSearchLog(Map<String, Object> searchLog) {
        //写入搜索记录
        String addSearchLog = SQLHelper.format("insert into T_SearchRecord(Id,SearchKey, Memo, UserID,UserName,ModuleFlag,CreateTime)" +
                "values('{0}','{1}','','{2}','{3}','{4}','{5}')");
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        String searchKey = (String) searchLog.get("searchKey");
        String moduleFlag = (String) searchLog.get("moduleFlag");
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedDateTime = now.format(formatter);
        if(!searchKey.isEmpty()){
            String sql = SQLHelper.format(addSearchLog,generateUUID(),searchKey,loginUser.getId(),loginUser.getInfo().get("nickname"),moduleFlag,formattedDateTime);
            SQLHelper sqlHelper = SQLHelper.CreateSqlHelper("Log");
            try {
                //插入数据
                sqlHelper.executeNonQuery(sql);
            }finally {
                sqlHelper.close();
            }
        }

    }
}
