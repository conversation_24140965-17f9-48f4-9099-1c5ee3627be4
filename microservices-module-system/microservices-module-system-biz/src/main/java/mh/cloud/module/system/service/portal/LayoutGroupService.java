package mh.cloud.module.system.service.portal;

import mh.cloud.module.system.controller.admin.portal.V0.CopyLayoutVo;
import mh.cloud.module.system.controller.admin.portal.V0.SysLayoutGroupVo;

import java.util.List;

public interface LayoutGroupService {

    Object getLayoutData(String category, String type, String userId, String group);

    Object saveAndUpdateLayoutGroup(List<SysLayoutGroupVo> sysLayoutGroupList, Boolean copy);

    Object copyLayout(List<CopyLayoutVo> copyLayouts);


}
