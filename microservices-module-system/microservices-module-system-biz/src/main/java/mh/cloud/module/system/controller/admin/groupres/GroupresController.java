package mh.cloud.module.system.controller.admin.groupres;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import mh.cloud.framework.common.pojo.PageParam;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.framework.common.util.object.BeanUtils;
import static mh.cloud.framework.common.pojo.CommonResult.success;

import mh.cloud.framework.excel.core.util.ExcelUtils;

import mh.cloud.framework.apilog.core.annotation.ApiAccessLog;
import static mh.cloud.framework.apilog.core.enums.OperateTypeEnum.*;

import mh.cloud.module.system.controller.admin.groupres.vo.*;
import mh.cloud.module.system.dal.dataobject.groupres.GroupresDO;
import mh.cloud.module.system.service.groupres.GroupresService;

@Tag(name = "管理后台 - 资源-角色关联")
@RestController
@RequestMapping("/system/groupres")
@Validated
public class GroupresController {

    @Resource
    private GroupresService groupresService;

    @PostMapping("/create")
    @Operation(summary = "创建资源-角色关联")
    @PreAuthorize("@ss.hasPermission('system:groupres:create')")
    public CommonResult<String> createGroupres(@Valid @RequestBody GroupresSaveReqVO createReqVO) {
        return success(groupresService.createGroupres(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新资源-角色关联")
    @PreAuthorize("@ss.hasPermission('system:groupres:update')")
    public CommonResult<Boolean> updateGroupres(@Valid @RequestBody GroupresSaveReqVO updateReqVO) {
        groupresService.updateGroupres(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除资源-角色关联")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:groupres:delete')")
    public CommonResult<Boolean> deleteGroupres(@RequestParam("id") String id) {
        groupresService.deleteGroupres(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得资源-角色关联")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:groupres:query')")
    public CommonResult<GroupresRespVO> getGroupres(@RequestParam("id") String id) {
        GroupresDO groupres = groupresService.getGroupres(id);
        return success(BeanUtils.toBean(groupres, GroupresRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得资源-角色关联分页")
    @PreAuthorize("@ss.hasPermission('system:groupres:query')")
    public CommonResult<PageResult<GroupresRespVO>> getGroupresPage(@Valid GroupresPageReqVO pageReqVO) {
        PageResult<GroupresDO> pageResult = groupresService.getGroupresPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, GroupresRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出资源-角色关联 Excel")
    @PreAuthorize("@ss.hasPermission('system:groupres:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportGroupresExcel(@Valid GroupresPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<GroupresDO> list = groupresService.getGroupresPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "资源-角色关联.xls", "数据", GroupresRespVO.class,
                        BeanUtils.toBean(list, GroupresRespVO.class));
    }

}
