package mh.cloud.module.system.service.uiList;

import java.util.*;
import jakarta.validation.*;
import mh.cloud.module.system.controller.admin.uiList.vo.*;
import mh.cloud.module.system.dal.dataobject.uiList.ListDO;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.pojo.PageParam;

/**
 * 列表配置 Service 接口
 *
 * <AUTHOR>
 */
public interface ListService {

    /**
     * 创建列表配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createList(@Valid ListSaveReqVO createReqVO);

    /**
     * 更新列表配置
     *
     * @param updateReqVO 更新信息
     */
    void updateList(@Valid ListSaveReqVO updateReqVO);

    /**
     * 删除列表配置
     *
     * @param id 编号
     */
    void deleteList(String id);

    /**
     * 获得列表配置
     *
     * @param id 编号
     * @return 列表配置
     */
    ListDO getList(String id);

    /**
     * 获得列表配置分页
     *
     * @param pageReqVO 分页查询
     * @return 列表配置分页
     */
    PageResult<ListDO> getListPage(ListPageReqVO pageReqVO);

}