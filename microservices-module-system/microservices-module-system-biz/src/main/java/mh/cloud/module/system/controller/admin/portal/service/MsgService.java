package mh.cloud.module.system.controller.admin.portal.service;

import mh.cloud.module.system.dal.dataobject.auser.AUserDO;
import mh.cloud.module.system.enums.MsgReceiverType;
import mh.cloud.module.system.enums.MsgType;

public interface MsgService {

    /**
     * 发送消息
     * @param title
     * @param content
     * @param receiverIDs
     * @param receiverNames
     * @param link
     * @param attachFileID
     * @param receiverType
     * @param msgType
     * @param isReadReceipt
     * @param isImportant
     * @param formInstanceID
     * @param flowCode
     * @param taskID
     * @param picurl
     * @param messageType
     * @param msgID
     * @param msgName
     * @param businessType
     * @param sfcsz
     * @param csry
     * @param YWGNSLMC
     */
    void SendMsg(String title, String content, String receiverIDs, String receiverNames, String link, String attachFileID, MsgReceiverType receiverType , MsgType msgType, Boolean isReadReceipt, Boolean isImportant, String formInstanceID, String flowCode, String taskID, String picurl, String messageType, String msgID, String msgName, String businessType, String sfcsz, String csry, String YWGNSLMC);

    /**
     * 发送消息
     * @param title
     * @param content
     * @param receiverIDs
     * @param receiverNames
     * @param link
     * @param attachFileID
     * @param sendUser
     * @param receiverType
     * @param msgType
     * @param isReadReceipt
     * @param isImportant
     * @param formInstanceID
     * @param flowCode
     * @param taskID
     * @param picurl
     * @param messageType
     * @param msgID
     * @param msgName
     * @param businessType
     * @param sfcsz
     * @param csry
     * @param YWGNSLMC
     */
    void SendMsg(String title, String content, String receiverIDs, String receiverNames, String link, String attachFileID, AUserDO sendUser, MsgReceiverType receiverType, MsgType msgType, Boolean isReadReceipt, Boolean isImportant, String formInstanceID, String flowCode, String taskID, String picurl, String messageType, String msgID, String msgName, String businessType, String sfcsz, String csry, String YWGNSLMC);

}
