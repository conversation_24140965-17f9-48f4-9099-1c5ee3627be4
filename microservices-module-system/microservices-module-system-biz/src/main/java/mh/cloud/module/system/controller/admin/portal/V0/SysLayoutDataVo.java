package mh.cloud.module.system.controller.admin.portal.V0;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.time.LocalDateTime;

@TableName("system_layout_data")
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysLayoutDataVo {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 布局名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 布局标志
     */
    @TableField(value = "sign")
    private String sign;

    @TableField(value = "default_layout")
    private String defaultLayout;

    /**
     * 数据内容
     */
    @TableField(value = "content")
    private String content;

    /**
     * 所属用户id
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id")
    private String tenantId;


    @TableField(value = "is_delete")
    @TableLogic(value = "0", delval = "1")
    private String isDelete;

    //组件图标
    @TableField("icon")
    private String icon;

    //组件分类
    @TableField("category")
    private String category;

    //拖拽组件标识
    @TableField("itemId")
    private String itemId;


}
