package mh.cloud.module.system.controller.admin.portal.constant;

import cn.hutool.core.util.ObjUtil;
import mh.cloud.module.system.controller.admin.auth.vo.AuthLoginRespVO;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 存储登录二维码状态
 */
public class QRCodeConstant {
    /**
     * 存储二维码状态
     */
    private final static ConcurrentHashMap<String, Map<String, Object>> QRCodeStatus = new ConcurrentHashMap<>();

    /**
     * 回调处理成功
     */
    public static final String QR_SUCCESS = "QR_SUCCESS";

    /**
     * 处理回调中
     */
    public static final String QR_IN_HAND = "QR_IN_HAND";

    /**
     * 超时
     */
    public static final String QR_TIMEOUT = "QR_TIMEOUT";

    /**
     * 有效时间 单位：毫秒
     */
    public static final long QR_VALID_TIME = 120000;

    /**
     * 请求校验标识
     */
    public static final String REQ_KEY = "REQUEST_KEY_QR_CODE";


    /**
     * 保存状态
     */
    public static void setQRCodeStatus(String key, Object status) {
        Map<String, Object> objectMap = new HashMap<>();
        if (QRCodeStatus.containsKey(key)) {
            objectMap = QRCodeStatus.get(key);
            objectMap.put("status", status);
            QRCodeStatus.replace(key, objectMap);
        } else {
            objectMap.put("status", status);
            QRCodeStatus.put(key, objectMap);
        }
    }

    /**
     * 获取状态
     */
    public static Map<String, Object> getQRCodeStatus(String key) {
        Map<String, Object> objectMap = new HashMap<>();
        if (!QRCodeStatus.containsKey(key)) {
            objectMap.put("status", QRCodeConstant.QR_TIMEOUT);
            return objectMap;
        }
        //判断是否超时
        if (isTimeOut(key)) {
            QRCodeConstant.removeQRCodeStatus();
            objectMap.put("status", QRCodeConstant.QR_TIMEOUT);
            return objectMap;
        }

        objectMap = QRCodeStatus.get(key);
        Object status = objectMap.get("status");
        //回调成功删除，清理内存数据
        if (!ObjUtil.isEmpty(status) && status.equals(QR_SUCCESS)) {
            removeQRCodeStatus();
        }
        return objectMap;
    }

    /**
     * 删除状态,因为数据保存到内存，所以处理完成后需要删除数据
     */
    public static void removeQRCodeStatus() {
        //获取到一小时之前的数据进行清理
        List<String> cleanList = QRCodeStatus.keySet().stream().filter(key -> {
            String time = key.split("_")[1];
            long millis = System.currentTimeMillis();
            long m = millis - Long.parseLong(time);
            return m >= 3600 * 1000;
        }).toList();
        for (String key : cleanList) {
            QRCodeStatus.remove(key);
        }
    }

    /**
     * 获取二维码标识Key
     *
     * @return uuid加时间戳
     */
    public static String getKey() {
        long millis = System.currentTimeMillis();
        String uuid = UUID.randomUUID().toString().replace("_", "");
        return uuid + "_" + millis;
    }


    /**
     * 保存用户临时token
     */
    public static void setUserToken(String key, AuthLoginRespVO authLoginRespVO) {
        Map<String, Object> map = QRCodeStatus.get(key);
        map.put("token", authLoginRespVO);
        QRCodeStatus.replace(key, map);
    }

    /**
     * 获取用户token
     *
     * @return uuid加时间戳
     */
    public static Map<String, Object> getUserToken(String key) {
        return QRCodeStatus.get(key);
    }


    /**
     * @param key 时间戳
     */
    private static boolean isTimeOut(String key) {
        String time = key.split("_")[1];
        //获取当前时间戳,有效时间为三分钟
        long millis = System.currentTimeMillis();
        long l = millis - Long.parseLong(time);

        return l >= QR_VALID_TIME;
    }

    /**
     * 校验请求标识
     * 获取二维码接口是开放的，需要一个标识校验
     */
    public static boolean checkReqKey(String reqKey) {
        return true;
    }

}
