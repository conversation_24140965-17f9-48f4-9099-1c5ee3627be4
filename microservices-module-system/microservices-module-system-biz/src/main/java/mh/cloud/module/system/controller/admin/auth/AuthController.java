package mh.cloud.module.system.controller.admin.auth;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import mh.cloud.framework.common.enums.CommonStatusEnum;
import mh.cloud.framework.common.enums.UserTypeEnum;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.framework.security.config.SecurityProperties;
import mh.cloud.framework.security.core.util.SecurityFrameworkUtils;
import mh.cloud.module.system.controller.admin.auth.vo.*;
import mh.cloud.module.system.convert.auth.AuthConvert;
import mh.cloud.module.system.dal.dataobject.auser.AUserDO;
import mh.cloud.module.system.dal.dataobject.auser.AUserDto;
import mh.cloud.module.system.dal.dataobject.auser.AUserPassword;
import mh.cloud.module.system.dal.dataobject.oauth2.UserSession;
import mh.cloud.module.system.dal.dataobject.permission.MenuDO;
import mh.cloud.module.system.dal.dataobject.permission.RoleDO;
import mh.cloud.module.system.dal.dataobject.user.AUserExtsDO;
import mh.cloud.module.system.enums.logger.LoginLogTypeEnum;
import mh.cloud.module.system.service.ares.AResService;
import mh.cloud.module.system.service.auser.AUserService;
import mh.cloud.module.system.service.auth.AdminAuthService;
import mh.cloud.module.system.service.db.SQLHelper;
import mh.cloud.module.system.service.permission.MenuService;
import mh.cloud.module.system.service.permission.PermissionService;
import mh.cloud.module.system.service.permission.RoleService;
import mh.cloud.module.system.service.social.SocialClientService;
import mh.cloud.module.system.service.user.AdminUserService;
import mh.cloud.module.system.service.userexts.AUserExtsService;
import mh.cloud.module.system.util.ConfigurationHelper;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


import java.util.*;
import java.util.stream.Collectors;

import static mh.cloud.framework.common.pojo.CommonResult.success;
import static mh.cloud.framework.common.util.collection.CollectionUtils.convertSet;
import static mh.cloud.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;


//扫码登录相关
import java.util.UUID;

import mh.cloud.module.system.service.oauth2.UserSessionService;


@Tag(name = "管理后台 - 认证")
@RestController
@RequestMapping("/system/auth")
@Validated
@Slf4j
public class AuthController {

    @Resource
    private AdminAuthService authService;
    @Resource
    private AdminUserService userService;
    @Resource
    private RoleService roleService;
    @Resource
    private MenuService menuService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private SocialClientService socialClientService;

    @Resource
    private SecurityProperties securityProperties;

    @Resource
    private AUserService aUserService;
    @Resource
    private AResService aResService;

    @Resource
    private UserSessionService userSessionService;

    @Resource
    private AUserExtsService aUserExtsService;
    @Resource
    private ConfigurationHelper configurationHelper;

    @PostMapping("/login")
    @PermitAll
    @Operation(summary = "使用账号密码登录")
    public CommonResult<AuthLoginRespVO> login(@RequestBody @Valid AuthLoginReqVO reqVO) {
        return success(authService.login(reqVO));
    }

    @PostMapping("/mocklogin")
    @PermitAll
    @Operation(summary = "使用账号密码模拟登录")
    public CommonResult<AuthLoginRespVO> mockLogin(@RequestBody @Valid AuthLoginReqVO reqVO) {
        return success(authService.mockLogin(reqVO));
    }

    @PostMapping("/logout")
    @PermitAll
    @Operation(summary = "登出系统")
    public CommonResult<Boolean> logout(HttpServletRequest request) {
        String token = SecurityFrameworkUtils.obtainAuthorization(request, securityProperties.getTokenHeader(), securityProperties.getTokenParameter());
        if (StrUtil.isNotBlank(token)) {
            authService.logout(token, LoginLogTypeEnum.LOGOUT_SELF.getType());
        }
        return success(true);
    }

    @PostMapping("/refresh-token")
    @PermitAll
    @Operation(summary = "刷新令牌")
    @Parameter(name = "refreshToken", description = "刷新令牌", required = true)
    public CommonResult<AuthLoginRespVO> refreshToken(@RequestParam("refreshToken") String refreshToken) {
        return success(authService.refreshToken(refreshToken));
    }

//    @PostMapping("/refresh-urlToken")
//    @PermitAll
//    @Operation(summary = "根据路径url的token刷新令牌")
//    @Parameter(name = "refreshToken", description = "刷新令牌", required = true)
//    public CommonResult<AuthLoginRespVO> refreshUrlToken(@RequestBody Map<String, String> tokenMap) {
//        String accessToken = tokenMap.get("accessToken");
//        String refreshToken = tokenMap.get("refreshToken");
//        return success(authService.refreshUrlToken(accessToken, refreshToken));
//    }


    @GetMapping("/get-permission-info")
    @Operation(summary = "获取登录用户的权限信息")
    public CommonResult<AuthPermissionInfoRespVO> getPermissionInfo() {
        // 1.1 获得用户信息
        AUserDO aUser = aUserService.getAUser(getLoginUserId());
        if (aUser == null) {
            return success(null);
        }
        LambdaQueryWrapper<AUserExtsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AUserExtsDO::getUserID, aUser.getId());
        AUserExtsDO one = aUserExtsService.getOne(queryWrapper);
        AUserDto user = new AUserDto();
        user.setNickname(aUser.getName());
        user.setId(aUser.getId());
        user.setAvatar(one.getHeadPortraitFile());
        user.setDeptId(aUser.getDeptID());
        user.setWorkNo(aUser.getWorkNo());

        // 1.2 获得角色列表
//        Set<Long> roleIds = permissionService.getUserRoleIdListByUserId(getLoginUserId());
        Set<Long> roleIds = permissionService.getUserRoleIdListByUserId("1");
        Set<String> roleIdsO = roleIds.stream().map(a -> a + "").collect(Collectors.toSet());

        Set<String> roleIds2 = permissionService.getUserRoleIdListByUserIdNew(getLoginUserId());

        roleIdsO.addAll(roleIds2);
        if (CollUtil.isEmpty(roleIdsO)) {
            return success(AuthConvert.INSTANCE.convert(user, Collections.emptyList(), Collections.emptyList()));
        }
        List<RoleDO> roles = roleService.getRoleList(roleIds);
        roles.removeIf(role -> !CommonStatusEnum.ENABLE.getStatus().equals(role.getStatus())); // 移除禁用的角色
        //原系统的角色部门id添加到角色列表中
        roleIds2.stream().forEach(item -> {
            RoleDO roleDO = new RoleDO();
            roleDO.setCode(item);
            roles.add(roleDO);
        });
        //获取原系统的菜单列表
//        List<AResDO> ares = permissionService.getResByUserId(getLoginUserId());

        // 1.3 获得菜单列表
        Set<String> menuIds = permissionService.getRoleMenuListByRoleId(convertSet(roles, RoleDO::getId));
        List<MenuDO> menuList = menuService.getMenuList(menuIds);
        if (IterUtil.isEmpty(menuList)) {
            menuList = new ArrayList<>();
        }
        menuList.removeIf(menu -> !CommonStatusEnum.ENABLE.getStatus().equals(menu.getStatus())); // 移除禁用的菜单

        // 2. 拼接结果返回
        String strsql = SQLHelper.format("SELECT * FROM ( " + "SELECT ROW_NUMBER () OVER (PARTITION BY t.XTLB ORDER BY t.SortIndex ASC) AS RID,t.FZ,t.CDMC AS ResID,t.CDMCName AS ResName,t.XSMC AS Name,t.Url,t.SortIndex,t.Parms,OpenType,t.XTLB, t.ResImage FROM (\n" + "  SELECT s.FZ,sm.CDMC,sm.CDMCName,sm.XSMC,si.Url AS Url,sm.PXH AS SortIndex,sm.cs AS Parms,si.OpenType,si.XTLB,si.ResImage FROM OA.dbo.T_Page_SysMenuAuth a INNER JOIN oa.dbo.T_Page_SysMenu s ON a.sysmenu =s.ID AND isnull(s.IsDeleted,0)=0 INNER JOIN oa.dbo.T_Page_SysMenu_Menu sm ON s.id =sm.T_Page_SysMenuID AND isnull(sm.IsDeleted,0)=0 INNER JOIN oa.dbo.T_Page_SysMenuItem si ON si.id =sm.CDMC AND isnull(si.IsDeleted,0)=0 INNER JOIN KMYZH_SystemDataBase.dbo.A_GroupRelation b ON a.Role LIKE '%'+b.ParentGroupID +'%' INNER JOIN KMYZH_SystemDataBase.dbo.A_Group c ON b.ParentGroupID =c.ID AND c.GroupType ='Role' INNER JOIN KMYZH_SystemDataBase.dbo.A_User d ON d.id =b.ChildGroupID WHERE d.WorkNo ='{0}') t) t1 WHERE t1.RID =1 ORDER BY t1.SortIndex ASC ", aUser.getWorkNo());
        //处理一下全量中不存在的，需要优化为从数据库中查出
        ArrayList<String> menus = new ArrayList();
        HashMap<String, String> mapALL = new HashMap<>();
        StringBuffer stringBuffer = new StringBuffer();
        List<AuthPermissionInfoRespVO.MenuVO> children = new ArrayList<>();
        try (SQLHelper oa = SQLHelper.createSqlHelper("OA")) {
            List<Map<String, Object>> menuMaps = oa.selectRows("select CDMC,ID from \"OA\".dbo.T_Page_SysMenuItem");
//            数据库初始换全量的菜单
            for (Map<String, Object> menuMap : menuMaps) {
                if (menuMap.get("CDMC") != null) {
                    String cdmc = menuMap.get("CDMC").toString();
                    menus.add(cdmc);
                    mapALL.put(cdmc, menuMap.get("CDMC") == null ? "" : menuMap.get("ID").toString());
                }
            }

            List<Map<String, Object>> maps = oa.selectRows(strsql);
            for (Map<String, Object> map : maps) {
                if (map.get("ResName") != null) {
                    //移除有权限的菜单
                    menus.remove(map.get("ResName").toString());
                }
//                AuthPermissionInfoRespVO.MenuVO menuVO = new AuthPermissionInfoRespVO.MenuVO();
//                menuVO.setName(map.get("ResID").toString());
//                menuVO.setId(map.get("ResID").toString());
//                menuVO.setPath(map.get("path").toString());
//                menuVO.setPath(map.get("path").toString());
            }
        }
        for (String menu : menus) {
            //返回没有权限的菜单
            stringBuffer.append("@@" + mapALL.get(menu));
        }
        user.setMenuStrs(stringBuffer.toString());
        AuthPermissionInfoRespVO convert = AuthConvert.INSTANCE.convert(user, roles, menuList);
        /*List<AuthPermissionInfoRespVO.MenuVO> menus1 = convert.getMenus();
        //构建菜单路由
        AuthPermissionInfoRespVO.MenuVO menuVO = new AuthPermissionInfoRespVO.MenuVO();
        menuVO.setName("Portal");
        menuVO.setPath("/Portal");
        menuVO.setComponent("/portal/portal");
        menuVO.setChildren(children);*/
        return success(convert);
    }

    // ========== 短信登录相关 ==========

    @PostMapping("/sms-login")
    @PermitAll
    @Operation(summary = "使用短信验证码登录")
    public CommonResult<AuthLoginRespVO> smsLogin(@RequestBody @Valid AuthSmsLoginReqVO reqVO) {
        return success(authService.smsLogin(reqVO));
    }

    @PostMapping("/send-sms-code")
    @PermitAll
    @Operation(summary = "发送手机验证码")
    public CommonResult<Boolean> sendLoginSmsCode(@RequestBody @Valid AuthSmsSendReqVO reqVO) {
        authService.sendSmsCode(reqVO);
        return success(true);
    }

    // ========== 社交登录相关 ==========

    @GetMapping("/social-auth-redirect")
    @PermitAll
    @Operation(summary = "社交授权的跳转")
    @Parameters({@Parameter(name = "type", description = "社交类型", required = true), @Parameter(name = "redirectUri", description = "回调路径")})
    public CommonResult<String> socialLogin(@RequestParam("type") Integer type, @RequestParam("redirectUri") String redirectUri) {
        return success(socialClientService.getAuthorizeUrl(type, UserTypeEnum.ADMIN.getValue(), redirectUri));
    }

    @PostMapping("/social-login")
    @PermitAll
    @Operation(summary = "社交快捷登录，使用 code 授权码", description = "适合未登录的用户，但是社交账号已绑定用户")
    public CommonResult<AuthLoginRespVO> socialQuickLogin(@RequestBody @Valid AuthSocialLoginReqVO reqVO) {
        return success(authService.socialLogin(reqVO));
    }

    @GetMapping("/getUserInfo")
    @PermitAll
    @Operation(summary = "获取用户信息(手机号码，电建通账号)")
    public CommonResult<Map<String, Object>> socialQuickLogin(@RequestParam("workNo") String workNo) {
        return success(authService.getUserInfo(workNo));
    }

    @PostMapping("/addPasswordLog")
    @PermitAll
    @Operation(summary = "添加修改密码日志")
    public CommonResult<Boolean> loginWorkNo(@RequestBody @Valid AuthUpdatePasswordReqVO reqVO) {
        return success(authService.addPasswordLog(reqVO.getWorkNo()));
    }

    @PostMapping("/update-password")
    @Operation(summary = "修改用户密码")
    @PermitAll
    public CommonResult<Boolean> updateUserPassword(@Valid @RequestBody AuthUpdatePasswordReqVO reqVO) {
        return success(aUserService.updateAUserPassword(reqVO));
    }

    @PostMapping("/reset-password")
    @Operation(summary = "批量设置用户密码")
    @PermitAll
    public CommonResult<Boolean> resetUserPassword(@Valid @RequestBody Map<String, AUserPassword> reqVO) {
        return success(aUserService.resetUserPassword(reqVO));
    }

    @PostMapping("/batch-settings-password")
    @Operation(summary = "批量设置用户密码")
    @PermitAll
    public CommonResult<Boolean> batchSettingsPassword(@Valid @RequestBody Map<String, List<AUserPassword>> reqVO) {
        return success(aUserService.batchSettingsPassword(reqVO));
    }

    @PostMapping("/sendCode")
    @Operation(summary = "发送验证码")
    @PermitAll
    public CommonResult<Boolean> sendCode(@Valid @RequestBody AuthUpdatePasswordReqVO reqVO) {
        aUserService.sendCode(reqVO);
        return success(true);
    }

    //    ======================扫码登录相关========================
    @GetMapping("/getQRcCode")
    @Operation(summary = "获取登录二维码")
    @PermitAll
    public CommonResult<Map<String, Object>> getQRCode(@RequestParam(defaultValue = "key") String key) {
        //最好在回去时，传递一个key来验签
        return CommonResult.success(authService.getQRCode(key));
    }

    @GetMapping("/getQRcCodeState")
    @Operation(summary = "查询二维码登录状态")
    @PermitAll
    public CommonResult<Map<String, Object>> getQRcCodeState(@RequestParam String uuId) {
        return CommonResult.success(authService.getQRcCodeState(uuId));
    }

    @GetMapping("/get-simpl-info")
    @Operation(summary = "获取用户简单参信息")
    public CommonResult<AUserDO> getSimplInfo() {
        return CommonResult.success(aUserService.getUserSimpInfo(getLoginUserId()));
    }

    //创建用户缓存和uuid,为前端创建电建通二维码使用
    @GetMapping("/get_uuid")
    @Operation(summary = "创建二维码uuid")
    @PermitAll
    public CommonResult<String> getUuid() {
        String uuid = userSessionService.createSession();//创建用户信息和uuid
        return CommonResult.success(uuid);
    }

    @GetMapping("/check_login_status")
    @Operation(summary = "检查扫码登录状态")
    @PermitAll
    public CommonResult<AuthLoginRespVO> checkLoginStatus(@RequestParam String uuId) {
        UserSession userSession = userSessionService.checkLoginStatus(uuId);//创建用户信息和uuid
        if (userSession != null) {
            //获取用户电建通账号
            Map<String, Object> userInfo = userSession.getUserInfo();

            //电建通账号获取用户信息和token
            AuthLoginRespVO user_info = authService.loginWorkNo(userInfo.get("KMYWorkNo").toString(), LoginLogTypeEnum.LOGIN_MOBILE);

            //清空临时用户session数据
            userSessionService.clearSession(uuId);

            //返回前端
            return CommonResult.success(user_info);
        }
        return CommonResult.success(null);
    }

    @GetMapping("/getAutoLoginOutTimeout")
    @Operation(summary = "获取自动退出登录时间（单位：秒）")
    public CommonResult<Integer> getAutoLoginOutTimeout() {
        String autoLogoutTimeout = configurationHelper.GetSettingValue("AutoLogoutTimeout");
        return CommonResult.success(Integer.parseInt(autoLogoutTimeout));
    }
}
