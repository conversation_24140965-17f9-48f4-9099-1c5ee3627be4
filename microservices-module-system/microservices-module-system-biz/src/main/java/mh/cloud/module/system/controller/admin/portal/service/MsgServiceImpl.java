package mh.cloud.module.system.controller.admin.portal.service;

import com.fhs.common.utils.StringUtil;
import jakarta.annotation.Resource;
import mh.cloud.module.system.dal.dataobject.auser.AUserDO;
import mh.cloud.module.system.enums.MsgReceiverType;
import mh.cloud.module.system.enums.MsgType;
import mh.cloud.module.system.service.auser.AUserService;
import mh.cloud.module.system.service.db.SQLHelper;
import mh.cloud.module.system.service.db.SqlService;
import org.springframework.stereotype.Service;


import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static mh.cloud.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Service
public class MsgServiceImpl implements  MsgService{
    @Resource
    private AUserService AUserService;
    @Override
    public void SendMsg(String title, String content, String receiverIDs, String receiverNames, String link, String attachFileID, MsgReceiverType receiverType, MsgType msgType, Boolean isReadReceipt, Boolean isImportant, String formInstanceID, String flowCode, String taskID, String picurl, String messageType, String msgID, String msgName, String businessType, String sfcsz, String csry, String YWGNSLMC) {
        AUserDO sendUser = AUserService.getAUser(getLoginUserId());
        SendMsg(title, content, receiverIDs, receiverNames, link, attachFileID, sendUser, receiverType, msgType, isReadReceipt, isImportant, formInstanceID, flowCode, taskID, picurl, messageType, msgID, msgName, businessType, sfcsz, csry, YWGNSLMC);
    }

    @Override
    public void SendMsg(String title, String content, String receiverIDs, String receiverNames, String link, String attachFileID, AUserDO sendUser, MsgReceiverType receiverType, MsgType msgType, Boolean isReadReceipt, Boolean isImportant, String formInstanceID, String flowCode, String taskID, String picurl, String messageType, String msgID, String msgName, String businessType, String sfcsz, String csry, String YWGNSLMC) {
        String userID = "";
        String userName = "系统消息";

        if (sendUser != null)
        {
            userID = sendUser.getId();
            userName = sendUser.getName();
        }
        if (msgType == MsgType.Normal || msgType == MsgType.Warning){
            sendSystemMsg(title, content, receiverIDs, receiverNames, link, attachFileID, userID, userName, receiverType, msgType, isReadReceipt, isImportant);
        }
    }



    public void sendSystemMsg(String title, String content, String receiverIDs, String receiverNames, String link, String attachFileID, String userID,String userName, MsgReceiverType receiverType, MsgType msgType, Boolean isReadReceipt, Boolean isImportant){
        SQLHelper sqlHelper = null;
        try {
            String msgBodyId = SQLHelper.generateUUID();
            sqlHelper = SQLHelper.createSqlHelper("Core");
            String sql = "INSERT  INTO I_MsgBody " +
                    "(ID,Title,Content,AttachFileIDs,LinkUrl,IsSystemMsg,SendTime,SenderID,SenderName,ReceiverIDs,ReceiverNames,IsReadReceipt,Importance,Type) \n" +
                    "VALUES " +
                    "('{0}','{1}','{2}','{3}','{4}','{5}','{6}','{7}','{8}','{9}','{10}','{11}','{12}','{13}')";
            sqlHelper.executeNonQuery(SQLHelper.format(sql, msgBodyId,title,content,attachFileID,link,"1", LocalDateTime.now(),userID,userName,receiverIDs,receiverNames, isReadReceipt? "1" : "0",isImportant ? "1" : "0",msgType.Normal));

           List<String> receiverIDList = Arrays.asList(receiverIDs.split(","));
           List<String> receiverNameList = Arrays.asList(receiverNames.split(","));
            for (int i = 0; i < receiverIDList.size(); i++) {
                String sql2 = "INSERT INTO I_MsgReceiver (ID,MsgBodyID,UserID,UserName,IsDeleted) VALUES ('{0}','{1}','{2}','{3}','{4}')";
                sqlHelper.executeNonQuery(SQLHelper.format(sql2, SQLHelper.generateUUID(),msgBodyId,receiverIDList.get(i),receiverNameList.get(i),"0"));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            if (sqlHelper != null){
                sqlHelper.close();
            }
        }


    }

}
