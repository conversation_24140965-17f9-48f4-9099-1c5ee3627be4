package mh.cloud.module.system.controller.admin.portal;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import mh.cloud.module.system.controller.admin.portal.V0.EntryMenu;
import mh.cloud.module.system.controller.admin.portal.V0.OutSystemV0;
import mh.cloud.module.system.controller.admin.portal.service.ServiceCenterService;
import mh.cloud.module.system.service.db.SQLHelper;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Tag(name = "服务中心")
@RestController
@RequestMapping("/Portal/ServiceCenter")
@Validated

public class ServiceCenter {

    @Resource
    private ServiceCenterService serviceCenterService;

    @PostMapping("/List")
    @Operation(summary = "获取右侧入口菜单数据")
    public List<EntryMenu> getList(@Valid @RequestBody Map<String, String> data) {

        return serviceCenterService.List(data.get("Search"));

    }

    @PostMapping("/LeftListCat")
    @Operation(summary = "获取菜单分类")
    public List<Map<String, Object>> getLeftListCat() {
        return serviceCenterService.getLeftListCat();
    }

    @PostMapping("/TestDb")
    @Operation(summary = "获取菜单分类")
    public List<Map<String, Object>> TestDb() {
        try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("SqlServer")) {
            String sql = "SELECT TOP 10 * FROM A_Token";
            return sqlHelper.executeReader(sql);
        }
    }

    @PostMapping("/getOutSystemUrl")
    @Operation(summary = "获取外部系统打开地址")
    public OutSystemV0 getOutSystemUrl(@RequestBody OutSystemV0 outSystem) {
        return serviceCenterService.getOutSystemUrl(outSystem);
    }

}
