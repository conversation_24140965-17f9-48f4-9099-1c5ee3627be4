package mh.cloud.module.system.service.portal;

import com.baomidou.mybatisplus.extension.service.IService;
import mh.cloud.module.system.controller.admin.portal.V0.SysLayoutDataReq;
import mh.cloud.module.system.controller.admin.portal.V0.SysLayoutDataVo;

import java.util.List;
import java.util.Map;

public interface LayoutDataService extends IService<SysLayoutDataVo> {

    /**
     * 获取布局配置数据
     */
    Object getLayoutData(String category);

    /**
     * 保存布局配置数据
     */
    Boolean saveLayoutData(SysLayoutDataReq sysLayoutReq);


    /**
     * 修改布局配置数据
     */
    Boolean updateLayoutData(SysLayoutDataReq sysLayoutReq);

    /**
     * 删除布局配置数据
     */
    Boolean deleteLayoutData(String id);


    /**
     * 获取对应默认配置数据
     */
    List<Map<String, Object>> getGroupLayoutList(String category, String type, String userId, String group);
}
