package mh.cloud.module.system.service.db.VO;

import lombok.Data;
import org.springframework.context.annotation.Bean;


@Data
public class DbConfig {


    private String jdbcUrlTeample = "***************************************************************,productName=PostgreSQL,SYS_CATALOG,PUBLIC&zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8&createDatabaseIfNotExist=true&SSL=false&nullCatalogMeansCurrent=true";

    private String jdbcUrl;
    private String username;
    private String password;
    private String driverClassName = "com.kingbase8.Driver";

    // 工厂方法，用于创建DbConfig实例
    @Bean
    public DbConfig createConfig() {
        return new DbConfig();
    }

    // 构造函数可以保持默认的公共访问级别
    public DbConfig() {
    }
}

