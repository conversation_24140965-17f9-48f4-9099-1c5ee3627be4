package mh.cloud.module.system.service.auser;

import jakarta.validation.Valid;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.module.system.controller.admin.auser.vo.AUserPageReqVO;
import mh.cloud.module.system.controller.admin.auser.vo.AUserSaveReqVO;
import mh.cloud.module.system.controller.admin.auser.vo.ResetPwdReqVO;
import mh.cloud.module.system.controller.admin.auth.vo.AuthUpdatePasswordReqVO;
import mh.cloud.module.system.controller.admin.portal.V0.HeadAndSign;
import mh.cloud.module.system.dal.dataobject.auser.AUserDO;
import mh.cloud.module.system.dal.dataobject.auser.AUserDoChild;
import mh.cloud.module.system.dal.dataobject.auser.AUserPassword;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 个人设置 Service 接口
 *
 * <AUTHOR>
 */
public interface AUserService {
    /**
     * 更新最后登录时间
     *
     * @param createReqVO 更新最后登录时间
     */
    void updateUserLastTime(@Valid AUserSaveReqVO createReqVO);

    /**
     * 创建个人设置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createAUser(@Valid AUserSaveReqVO createReqVO);

    /**
     * 更新个人设置
     *
     * @param updateReqVO 更新信息
     */
    void updateAUser(@Valid AUserSaveReqVO updateReqVO);

    /**
     * 删除个人设置
     *
     * @param id 编号
     */
    void deleteAUser(String id);

    /**
     * 获得个人设置
     *
     * @param id 编号
     * @return 个人设置
     */
    AUserDO getAUser(String id);


    /**
     * 获得个人设置
     *
     * @param id 编号
     * @return 个人设置
     */
    AUserDO getAUserInfo(String id);

    /**
     * 获得个人设置分页
     *
     * @param pageReqVO 分页查询
     * @return 个人设置分页
     */
    PageResult<AUserDO> getAUserPage(AUserPageReqVO pageReqVO);

    AUserDO getByUserName(String username);

    Boolean resetPwd(ResetPwdReqVO reqVO);

    PageResult<AUserDoChild> getCustomAUserPage(AUserPageReqVO pageReqVO);

    /**
     * 更新用户登录失败次数
     *
     * @param userId 用户编号
     * @return 用户信息
     */
    Integer updateLoginFailCount(String userId, Integer count);

    //修改用户密码
    Boolean updateAUserPassword(AuthUpdatePasswordReqVO reqVO);

    //重置用户密码(管理员功能)
    @Transactional(rollbackFor = Exception.class) // 添加事务，异常则回滚所有修改数据
    Boolean resetUserPassword(Map<String, AUserPassword> reqVO);

    //批量修改用户密码（仅用于用户密码初始化操作）
    Boolean batchSettingsPassword(Map<String, List<AUserPassword>> reqVO);

    //发送验证码
    void sendCode(AuthUpdatePasswordReqVO reqVO);

    /**
     * 获取用户签名
     *
     * @param userId 用户id
     * @return 签名信息
     */
    String getSignImg(String userId);

    List<AUserDO> getUserAll(String name);

    /**
     * 获取用户简单信息
     */
    AUserDO getUserSimpInfo(String loginUserId);

    AUserDO getUserSimpInfoByWorkNo(String workNo);

    /**
     * 是否存在LoginName等于用户名并且IsDeleted='0'的记录
     */
    AUserDO selectUserNameAndIsDeleted(String name ,String password);

}
