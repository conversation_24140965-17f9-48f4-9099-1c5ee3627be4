package mh.cloud.module.system.controller.admin.otherjobrelation.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 兼职关系 Response VO")
@Data
@ExcelIgnoreUnannotated
public class OtherjobRelationRespVO {

    @Schema(description = "groupid", example = "29745")
    @ExcelProperty("groupid")
    private String parentGroupID;

    @Schema(description = "用户id", example = "23989")
    @ExcelProperty("用户id")
    private String childGroupID;

    @Schema(description = "全路径id", example = "15065")
    @ExcelProperty("全路径id")
    private String deptFullID;

    @Schema(description = "关系类型", example = "1")
    @ExcelProperty("关系类型")
    private String relationType;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31559")
    @ExcelProperty("id")
    private String id;

}