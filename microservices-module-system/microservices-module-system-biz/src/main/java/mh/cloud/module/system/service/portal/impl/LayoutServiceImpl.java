package mh.cloud.module.system.service.portal.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import mh.cloud.framework.common.exception.ServiceException;
import mh.cloud.framework.common.exception.enums.GlobalErrorCodeConstants;
import mh.cloud.framework.security.core.util.SecurityFrameworkUtils;
import mh.cloud.framework.tenant.core.context.TenantContextHolder;
import mh.cloud.module.system.controller.admin.portal.V0.*;
import mh.cloud.module.system.dal.mysql.portal.LayoutConfigMapper;
import mh.cloud.module.system.dal.mysql.portal.LayoutDataMapper;
import mh.cloud.module.system.dal.mysql.portal.LayoutMapper;
import mh.cloud.module.system.service.portal.LayoutService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class LayoutServiceImpl extends ServiceImpl<LayoutMapper, SysLayoutVo> implements LayoutService {

    @Resource
    private LayoutMapper layoutMapper;

    @Resource
    private LayoutDataMapper layoutDataMapper;

    @Resource
    private LayoutConfigMapper layoutConfigMapper;


    /**
     * 获取布局数据列表
     *
     * @return 列表
     */
    @Override
    public Map<String, Object> getLayout(String category) {
        //获取用户消息
        String userId = SecurityFrameworkUtils.getLoginUserId();

        //获取配置数据
        SysLayoutConfigVo config = layoutConfigMapper.selectOne(Wrappers.<SysLayoutConfigVo>lambdaQuery().eq(SysLayoutConfigVo::getUserId, userId));
        boolean isDefault = ObjUtil.isEmpty(config) || config.getIsDefault();
        //获取到用布局权限户权限信息，依据权限来获取默认布局
        String groupCode = null;

        //根据用户查询用户布局消息
        List<Map<String, Object>> layout;
        if (!isDefault) {
            layout = layoutMapper.getLayout(category, userId, ObjUtil.isEmpty(groupCode) ? "DEFAULT" : groupCode);
        } else {
            layout = layoutMapper.getDefaultLayout(category, ObjUtil.isEmpty(groupCode) ? "DEFAULT" : groupCode);
        }
        List<SysLayoutReq> sysLayoutReqList = new ArrayList<>();
        for (Map<String, Object> layoutMap : layout) {

            SysLayoutReq layoutReq = BeanUtil.toBean(layoutMap, SysLayoutReq.class);
            JSONObject itemJson = JSONUtil.parseObj(layoutMap.get("content"));
            JSONObject layoutJson = JSONUtil.parseObj(layoutMap.get("layout"));
            JSONObject defaultLayout = JSONUtil.parseObj(layoutReq.getDefaultLayout());
            layoutReq.setItemJson(itemJson);
            layoutReq.setCoordinates(ObjUtil.isEmpty(layoutJson) ? defaultLayout : layoutJson);
            sysLayoutReqList.add(layoutReq);
        }
        Map<Integer, List<SysLayoutReq>> listMap = sysLayoutReqList.stream().collect(Collectors.groupingBy(SysLayoutReq::getStartUse));
        Map<String, Object> map = new HashMap<>();
        map.put("noUse", listMap.get(0));
        map.put("use", listMap.get(1));
        map.put("config", config);
        return map;
    }


    /**
     * 保存配置数据
     */
    @Override
    public Boolean saveLayoutConfig(SysLayoutConfigVo config) {
        //获取用户消息
        String userId = SecurityFrameworkUtils.getLoginUserId();
        if (ObjUtil.isEmpty(config.getId())) {
            config = new SysLayoutConfigVo();
            config.setUserId(userId);
            Long tenantId = TenantContextHolder.getTenantId();
            config.setTenantId(tenantId.toString());
            return layoutConfigMapper.insert(config) > 0;
        } else {
            return layoutConfigMapper.updateById(config) > 0;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object saveOrUpdateLayout(LayoutSaveReq layoutSaveReq) {
        //获取用户消息
        String userId = SecurityFrameworkUtils.getLoginUserId();
        LocalDateTime now = LocalDateTime.now();
        List<SysLayoutVo> saveList = new ArrayList<>();
        List<SysLayoutVo> updateList = new ArrayList<>();
        List<Map<String, String>> layoutList = layoutSaveReq.getLayoutList();
        for (Map<String, String> layout : layoutList) {
            SysLayoutVo layoutVo = BeanUtil.toBean(layout, SysLayoutVo.class);
            Integer startUse = layoutVo.getStartUse();
            if (!ObjUtil.isEmpty(startUse) && startUse == 1) {
                if (ObjUtil.isEmpty(layoutVo.getId())) {
                    layoutVo.setUserId(userId);
                    layoutVo.setCreateTime(now);
                    layoutVo.setUpdateTime(now);
                    saveList.add(layoutVo);
                } else {
                    layoutVo.setUpdateTime(now);
                    updateList.add(layoutVo);
                }
            } else {
                // 未使用状态下，如果存在布局则删除
                if (!ObjUtil.isEmpty(layoutVo.getId())) {
                    this.removeById(layoutVo.getId());
                }
            }
        }
        try {
            if (!ObjUtil.isEmpty(saveList)) {
                this.saveBatch(saveList);
            }
            if (!ObjUtil.isEmpty(updateList)) {
                this.updateBatchById(updateList);
            }
            return saveLayoutConfig(layoutSaveReq.getConfig());
        } catch (Exception e) {
            throw new ServiceException(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR.getCode(), "布局处理异常" + e.getMessage());
        }
    }

    /**
     * 删除布局数据
     *
     * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteLayout(List<String> ids) {
        return this.removeByIds(ids);
    }

    /**
     * 启用删除布局
     *
     * @param id
     */
    @Override
    public Boolean startDelLayout(String id) {
        try {
            layoutMapper.startDelLayout(id);
            return true;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private Map<String, Object> layoutDataInit(Map<String, Object> layoutMap, String userId, String category) {
        SysLayoutDataVo layoutDataVo = layoutDataMapper.selectById(layoutMap.get("dataId").toString());
        SysLayoutVo sysLayoutVo = new SysLayoutVo();
        sysLayoutVo.setDataId(layoutDataVo.getId());
        sysLayoutVo.setStartUse(0);
        if (ObjUtil.isEmpty(layoutDataVo.getDefaultLayout())) {
            Map<String, Integer> map = new HashMap<>();
            map.put("x", 0);
            map.put("y", 0);
            map.put("w", 10);
            map.put("h", 10);
            String jsonStr = JSONUtil.toJsonStr(map);
            sysLayoutVo.setLayout(jsonStr);
        } else {
            sysLayoutVo.setLayout(layoutDataVo.getDefaultLayout());
        }
        sysLayoutVo.setUserId(userId);
        Long tenantId = TenantContextHolder.getTenantId();
        sysLayoutVo.setTenantId(tenantId.toString());
        LocalDateTime now = LocalDateTime.now();
        sysLayoutVo.setCreateTime(now);
        sysLayoutVo.setUpdateTime(now);
        if (this.save(sysLayoutVo)) {
            return layoutMapper.getLayoutById(category, userId, sysLayoutVo.getId());
        }
        throw new ServiceException(500, "数据处理异常，请联系管理员处理");
    }

    /**
     * 获取布局配置
     */
    @Override
    public Object getLayoutConfig() {
        String userId = SecurityFrameworkUtils.getLoginUserId();
        SysLayoutConfigVo layoutConfigVo = layoutConfigMapper.selectOne(Wrappers.<SysLayoutConfigVo>lambdaQuery().eq(SysLayoutConfigVo::getUserId, userId));
        if (!ObjUtil.isEmpty(layoutConfigVo)) {
            return JSONUtil.parseObj(layoutConfigVo.getLayoutConfig());
        }
        return null;
    }
}
