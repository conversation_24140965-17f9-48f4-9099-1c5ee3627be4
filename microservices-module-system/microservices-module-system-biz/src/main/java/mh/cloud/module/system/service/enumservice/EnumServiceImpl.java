package mh.cloud.module.system.service.enumservice;

import jakarta.annotation.Resource;
import mh.cloud.module.system.controller.admin.enumcontroller.vo.EnumDefVO;
import mh.cloud.module.system.controller.admin.enumcontroller.vo.EnumItemVO;
import mh.cloud.module.system.convert.enumconvert.EnumConvert;
import mh.cloud.module.system.dal.mysql.enummapper.EnumDefMapper;
import mh.cloud.module.system.dal.mysql.enummapper.EnumItemMapper;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class EnumServiceImpl implements EnumService {

    @Resource
    private EnumDefMapper enumDefMapper;

    @Resource
    private EnumItemMapper enumItemMapper;


    @Override
    public List<EnumDefVO> getEnumDefsByCode(String code) {

        return EnumConvert.INSTANCE.convertDefList(enumDefMapper.selectByCode(code));
    }

    @Override
    public List<EnumItemVO> getEnumItemsByDefID(String defID) {
        return EnumConvert.INSTANCE.convertItemList(enumItemMapper.selectByEnumDefIdAndCode(defID));
    }
}
