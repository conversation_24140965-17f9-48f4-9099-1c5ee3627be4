package mh.cloud.module.system.controller.admin.otherjobrelation;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import mh.cloud.framework.common.pojo.PageParam;
import mh.cloud.framework.common.pojo.PageResult;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.framework.common.util.object.BeanUtils;
import static mh.cloud.framework.common.pojo.CommonResult.success;

import mh.cloud.framework.excel.core.util.ExcelUtils;

import mh.cloud.framework.apilog.core.annotation.ApiAccessLog;
import static mh.cloud.framework.apilog.core.enums.OperateTypeEnum.*;

import mh.cloud.module.system.controller.admin.otherjobrelation.vo.*;
import mh.cloud.module.system.dal.dataobject.otherjobrelation.OtherjobRelationDO;
import mh.cloud.module.system.service.otherjobrelation.OtherjobRelationService;

@Tag(name = "管理后台 - 兼职关系")
@RestController
@RequestMapping("/system/otherjob-relation")
@Validated
public class OtherjobRelationController {

    @Resource
    private OtherjobRelationService otherjobRelationService;

    @PostMapping("/create")
    @Operation(summary = "创建兼职关系")
    @PreAuthorize("@ss.hasPermission('system:otherjob-relation:create')")
    public CommonResult<String> createOtherjobRelation(@Valid @RequestBody OtherjobRelationSaveReqVO createReqVO) {
        return success(otherjobRelationService.createOtherjobRelation(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新兼职关系")
    @PreAuthorize("@ss.hasPermission('system:otherjob-relation:update')")
    public CommonResult<Boolean> updateOtherjobRelation(@Valid @RequestBody OtherjobRelationSaveReqVO updateReqVO) {
        otherjobRelationService.updateOtherjobRelation(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除兼职关系")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:otherjob-relation:delete')")
    public CommonResult<Boolean> deleteOtherjobRelation(@RequestParam("id") String id) {
        otherjobRelationService.deleteOtherjobRelation(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得兼职关系")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:otherjob-relation:query')")
    public CommonResult<OtherjobRelationRespVO> getOtherjobRelation(@RequestParam("id") String id) {
        OtherjobRelationDO otherjobRelation = otherjobRelationService.getOtherjobRelation(id);
        return success(BeanUtils.toBean(otherjobRelation, OtherjobRelationRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得兼职关系分页")
    @PreAuthorize("@ss.hasPermission('system:otherjob-relation:query')")
    public CommonResult<PageResult<OtherjobRelationRespVO>> getOtherjobRelationPage(@Valid OtherjobRelationPageReqVO pageReqVO) {
        PageResult<OtherjobRelationDO> pageResult = otherjobRelationService.getOtherjobRelationPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, OtherjobRelationRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出兼职关系 Excel")
    @PreAuthorize("@ss.hasPermission('system:otherjob-relation:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportOtherjobRelationExcel(@Valid OtherjobRelationPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<OtherjobRelationDO> list = otherjobRelationService.getOtherjobRelationPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "兼职关系.xls", "数据", OtherjobRelationRespVO.class,
                        BeanUtils.toBean(list, OtherjobRelationRespVO.class));
    }

}
