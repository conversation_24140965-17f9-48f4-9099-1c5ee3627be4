package mh.cloud.module.system.controller.admin.logger;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.module.system.service.db.SQLHelper;
import mh.cloud.module.system.service.logger.OpenUrlLogService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

import static mh.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "用户操作日志管理")
@RestController
@RequestMapping("/system/open-url-log")
@Validated
public class OpenUrlLogController {

    @Resource
    OpenUrlLogService openUrlLogService;

    @RequestMapping("/page")
    public Object list(@RequestParam(value = "page", defaultValue = "1") int page, @RequestParam(value = "size", defaultValue = "10") int size) {

        SQLHelper sqlHelper = SQLHelper.CreateSqlHelper("Log");
        Page<Map<String, Object>> pages = sqlHelper.page("SELECT * FROM T_OpenUrlLog", page, size);
        sqlHelper.close();
        return pages;
    }

    @PostMapping("/add")
    public CommonResult<Boolean> create(@RequestBody Map<String, Object> params) {
        openUrlLogService.create(params);
        return success(true);
    }
}
