package mh.cloud.module.system.service.userexts.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletRequest;
import mh.cloud.module.system.dal.dataobject.user.AUserExtsDO;
import mh.cloud.module.system.dal.mysql.user.AUserExtsMapper;
import mh.cloud.module.system.service.userexts.AUserExtsService;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.*;

@Service
public class AUserExtsServiceImpl extends ServiceImpl<AUserExtsMapper, AUserExtsDO> implements AUserExtsService {

}