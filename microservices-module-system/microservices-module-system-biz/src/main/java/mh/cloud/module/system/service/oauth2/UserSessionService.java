package mh.cloud.module.system.service.oauth2;

import jakarta.annotation.Resource;
import mh.cloud.module.system.dal.dataobject.auser.AUserDO;
import mh.cloud.module.system.dal.dataobject.oauth2.UserSession;
import mh.cloud.module.system.service.auser.AUserService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import mh.cloud.module.system.service.auser.AUserService;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class UserSessionService {
    private final ConcurrentHashMap<String, UserSession> sessionStore = new ConcurrentHashMap<>();

    @Resource
    @Lazy // 懒加载，避免循环依赖
    private AUserService aUserService;

    //创建用户扫码临时信息
    public String createSession() {
        String uuid = UUID.randomUUID().toString();

        UserSession session = new UserSession();
        session.setUuid(uuid);
        session.setUserId(null);
        session.setUserInfo(null);
        session.setLoginStatus(false);

        sessionStore.put(uuid, session);
        return uuid;
    }

    //更新用户登录状态，电建通第三方请求
    public boolean updateLoginStatus(String uuid, String userId, Map<String, Object> userInfo) {
        UserSession session = sessionStore.get(uuid);
        if (session != null) {
            session.setUserId(userId);
            session.setUserInfo(userInfo);
            session.setLoginStatus(true);
            session.setLoginTime(LocalDateTime.now()); // 设置当前时间为登录时间
            return true;
        }
        return false;
    }

    //检查登录状态，扫码登录前端页面获取是否已登录信息
    public UserSession checkLoginStatus(String uuid) {
        UserSession session = sessionStore.get(uuid);

        // 检查会话是否存在
        if (session != null && session.isLoginStatus()) {
            // 获取当前时间
            LocalDateTime now = LocalDateTime.now();

            // 检查登录时间是否在30秒内
            if (session.getLoginTime() != null && Duration.between(session.getLoginTime(), now).toSeconds() < 30) {
                return session; // 返回用户会话
            }
        }

        return null; // 如果会话不存在、未登录或超过1分钟，返回 null
    }

    //用户扫码登录完成，清除用户临时保存信息
    public void clearSession(String uuid) {
        sessionStore.remove(uuid);
    }
}
