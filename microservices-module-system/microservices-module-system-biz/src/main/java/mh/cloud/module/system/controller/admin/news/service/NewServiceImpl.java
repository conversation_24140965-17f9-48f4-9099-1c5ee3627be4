package mh.cloud.module.system.controller.admin.news.service;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import mh.cloud.module.system.controller.admin.news.VO.Menu;
import mh.cloud.module.system.enums.SqlType;
import mh.cloud.module.system.service.db.SQLHelper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class NewServiceImpl implements NewsService {
//    private static  SQLHelper _zxwhelper = SQLHelper.CreateSqlHelper("CenterNet");
//    private static SQLHelper oahelper = SQLHelper.CreateSqlHelper("OA");
//    private static SQLHelper _corehelper = SQLHelper.CreateSqlHelper("Core");
//    private static SQLHelper _loghelper = SQLHelper.CreateSqlHelper("Log");
//    private static SQLHelper _markethelper = SQLHelper.CreateSqlHelper("Market173");


    @Override
    public String newsCenterTypeSql(String code, String detailId, String searchContent, SqlType type) {
        List<Menu> menuList = GetDynamicMenu(code);
        if (IterUtil.isNotEmpty(menuList)) {
            Menu menu = menuList.get(0);
            String sql = null;
            switch (type) {
                case ListSql:
                    String likeCondition = "WHERE Title LIKE '%" + searchContent + "%'";
                    String isLikeCondition = StrUtil.isBlank(searchContent) ? null : likeCondition;
                    /* 根据菜单列表的SQL模板和给定的代码值，格式化SQL语句 */
                    /* 如果isLikeCondition不为空，则将其追加到SQL语句中，以条件形式包含 */
                    sql = SQLHelper.format(menu.getListSql().replace("\"", "'"), code) + (isLikeCondition != null ? " " + isLikeCondition : "");
                    break;
                case DetailSql:
                    sql = SQLHelper.format(menu.getDetailSql().replace("\"", "'"), detailId);
                    break;
                case ViewCountSql:
                    sql = SQLHelper.format(menu.getViewCountSql().replace("\"", "'"), detailId);
                    break;
            }
            return sql;
        }
        return null;
    }

    /**
     * 动态获取菜单列表
     *
     * @param code 编码
     * @return
     */
    @Override
    public List<Menu> GetDynamicMenu(String code) {
        SQLHelper sqlHelper = SQLHelper.createSqlHelper("OA");
        try {
            List<Menu> list = new ArrayList<>();
            //动态获取资讯中心详情脚本
            String scriptSql = "SELECT 'Parent' AS [Type], [ID], 'ParentID' AS ParentID, CatalogName, SortIndex, [IsEnabled], [Code], [ListSql], [DetailSql], [ViewCountSql] FROM [T_Page_NewsCenterMenu] WHERE (IsDeleted <> '1' OR IsDeleted IS NULL) AND IsEnabled = 1 AND Code= '" + code +
                    "' UNION ALL SELECT 'child' AS [Type], [ID], [T_Page_NewsCenterMenuID] AS ParentID, CatalogName, SortIndex, [IsEnabled], [Code], [ListSql], [DetailSql], [ViewCountSql] FROM [T_Page_NewsCenterMenu_NewsCenterMenuSub] WHERE IsEnabled = 1 AND Code= '" + code +
                    "' ORDER BY SortIndex ASC";
            log.info("scriptSql:" + scriptSql);
            List<Map<String, Object>> maps = sqlHelper.executeReader(scriptSql);
            //将maps转换为Menu
            for (Map<String, Object> map : maps) {
                Menu menu = new Menu();
                menu.setType(map.get("Type").toString());
                menu.setId(map.get("ID").toString());
                menu.setParentId(map.get("ParentID").toString());
                menu.setCatalogName(map.get("CatalogName").toString());
                menu.setSortIndex(map.get("SortIndex").toString());
                menu.setIsEnabled(map.get("IsEnabled").toString());
                menu.setCode(map.get("Code").toString());
                menu.setListSql(map.get("ListSql").toString());
                menu.setDetailSql(map.get("DetailSql").toString());
                menu.setViewCountSql(map.get("ViewCountSql").toString());
                list.add(menu);
            }

            return list;
        } finally {
            sqlHelper.close();
        }
    }

    @Override
    public Page<Map<String, Object>> exceNewsSql(String s, int pageSize,int pageNum) {
        SQLHelper sqlHelper = SQLHelper.createSqlHelper("OA");
        try {
            Map<String, Object> map = sqlHelper.selectFirstRow(s);
           //判断是否有数据
            if (map != null) {
                Object listSql = map.get("ListSql");
                if (listSql != null) {
                    Page<Map<String, Object>> page = sqlHelper.page(listSql.toString(), pageNum, pageSize);
                    return page;
                }

            }
        } finally {
            sqlHelper.close();
        }
        return null;
    }
}
