package mh.cloud.module.system.controller.admin.portal.service;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fhs.common.utils.StringUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import mh.cloud.framework.common.exception.BaseException;
import mh.cloud.module.system.controller.admin.portal.V0.CentZxzxMenuDateItem;
import mh.cloud.module.system.controller.admin.portal.V0.ProjectTree;
import mh.cloud.module.system.controller.admin.portal.constant.SQLConstant;
import mh.cloud.module.system.controller.admin.portal.emnu.RoleType;
import mh.cloud.module.system.dal.dataobject.auser.AUserDO;
import mh.cloud.module.system.service.auser.AUserService;
import mh.cloud.module.system.service.db.SQLHelper;
import mh.cloud.module.system.service.portal.WorkCenterService;
import mh.cloud.module.system.util.ConfigurationHelper;
import mh.cloud.module.system.util.GuidUtil;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static mh.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static mh.cloud.module.system.enums.ErrorCodeConstants.SMS_CODE_NOT_CORRECT;
import static mh.cloud.module.system.enums.ErrorCodeConstants.SMS_CODE_NOT_FOUND;

@Service
@Data
@Slf4j
public class HomeServiceImpl implements HomeService {

    @Resource
    private WorkCenterService workCenterService;

    @Resource
    private ConfigurationHelper configurationHelper;

    @Autowired
    private StringRedisTemplate redisTemplate;


    @Resource
    private AUserService aUserService;

    private String GetUserTokenBy4A = "select ID,UserID,Token,CreateTime,ExpireTime from A_Token_4A where UserID='{0}' and UserIP='{1}'";
    private String InsTokenBy4A = "Insert into A_Token_4A(ID,UserID,Token,CreateTime,ExpireTime,UserIP) values('{0}','{1}','{2}',getdate(),{3},'{4}')";


    @Override
    public Page<Map<String, Object>> getYnfw(int pageNumber, int pageSize) {

        String mvcListCode = "List_ad520113ed544ca9a81ccc7b99e2fc09";

        String code = "YNFW";
        String zxzxChildOpCode = null;
        return getMapPage(pageNumber, pageSize, mvcListCode, code, zxzxChildOpCode);
    }

    @Override
    public Page<Map<String, Object>> getGsxw(int pageNumber, int pageSize) {
        String mvcListCode = "List_ad520113ed544ca9a81ccc7b99e2fc09";
        String code = "YNTZ";
        String zxzxChildOpCode = null;
        return getMapPage(pageNumber, pageSize, mvcListCode, code, zxzxChildOpCode);
    }

    @Override
    public Page<Map<String, Object>> getTzgg(int pageNumber, int pageSize) {
        String mvcListCode = "List_ad520113ed544ca9a81ccc7b99e2fc09";
        String code = "YNGG";
        String zxzxChildOpCode = null;
        return getMapPage(pageNumber, pageSize, mvcListCode, code, zxzxChildOpCode);
    }

    @Override
    public Page<Map<String, Object>> getZxxx(int pageNumber, int pageSize, String code) {
        return getMapPage(pageNumber, pageSize, "List_ad520113ed544ca9a81ccc7b99e2fc09", code, null);
    }

    private static @Nullable Page<Map<String, Object>> getMapPage(int pageNumber, int pageSize, String mvcListCode, String code, String zxzxChildOpCode) {
        if (pageNumber == 0) {
            pageNumber = 1;
        }
        String pSqlInfo = "SELECT * FROM UI_List WHERE Code = ? ";
        SQLHelper sqlHelper = null;
        SQLHelper core = SQLHelper.createSqlHelper("Core");
        try {
            List<Map<String, Object>> uiList = core.executeReader(pSqlInfo, mvcListCode);
            if (IterUtil.isNotEmpty(uiList)) {
                Map<String, Object> stringObjectMap = uiList.get(0);
                String tableNames = stringObjectMap.get("TableNames") == null ? "" : stringObjectMap.get("TableNames").toString();
                String connName = uiList.get(0).get("ConnName") == null ? "" : uiList.get(0).get("ConnName").toString();
                sqlHelper = SQLHelper.createSqlHelper(connName);
                String sql = "SELECT * FROM " + tableNames + " WHERE Code = ? ";
                List<Map<String, Object>> maps = sqlHelper.executeReader(sql, code);
                if (IterUtil.isNotEmpty(maps)) {
                    String menuid = maps.get(0).get("ID").toString();

                    String listSql = maps.get(0).get("ListSql") == null ? "" : maps.get(0).get("ListSql").toString();
                    Page<Map<String, Object>> mapPage = sqlHelper.ExecuteDataTable(listSql, pageNumber, pageSize);

                    String subMenuSql = SQLHelper.format("SELECT * FROM T_Page_NewsCenterMenu_NewsCenterMenuSub WHERE T_Page_NewsCenterMenuID = '{0}'", menuid);
                    if (StrUtil.isNotEmpty(zxzxChildOpCode)) {
                        subMenuSql = SQLHelper.format("SELECT * FROM T_Page_NewsCenterMenu_NewsCenterMenuSub WHERE T_Page_NewsCenterMenuID = '{0}' AND Code = '{1}'", menuid, zxzxChildOpCode);
                    }
                    List<Map<String, Object>> subMenuData = sqlHelper.executeReader(subMenuSql);
                    if (subMenuData != null && subMenuData.size() > 0) {
                        List<CentZxzxMenuDateItem> children = new ArrayList<>();
                        for (Map<String, Object> lpMap : mapPage.getRecords()) {
                            //clone一个subMenuData
                            List<Map<String, Object>> lpSubMenuData = subMenuData.stream().collect(Collectors.toList());

                            lpSubMenuData.add(lpMap);

                            CentZxzxMenuDateItem childItem = new CentZxzxMenuDateItem();
                            childItem.setMenuData(lpSubMenuData);
                            String listSql2 = lpMap.get("ListSql") == null ? "" : lpMap.get("ListSql").toString();
                            Page<Map<String, Object>> lpData = sqlHelper.ExecuteDataTable(listSql2, pageNumber, pageSize);
                            childItem.setData(lpData.getRecords());
                            children.add(childItem);
                        }
                    }
                    return mapPage;
                }
            }
            return null;
        } finally {
            sqlHelper.close();
            core.close();
        }
    }

    @Override
    public List<ProjectTree> queryXmzxUnitInfos(String id) {
        String userRelation = getUserRelation(id);
        SQLHelper sqlHelper = null;
        List<Map<String, Object>> maps = null;
        List<Map<String, Object>> maps1 = null;
        try {
            sqlHelper = SQLHelper.createSqlHelper("Core");
            String authSql = "SELECT a.Id,a.Name,CDMC AS ResId,CDMCName AS ResName,a.ParentID,a.FullID,a.SortIndex,b.Url,a.TB AS Icon FROM OA.dbo.T_Tree_ResInfo a LEFT JOIN KMYZH_SystemDataBase.dbo.A_Res b ON a.CDMC = b.ID WHERE (a.IsDeleted IS NULL OR a.IsDeleted = '0' ) AND a.FullID LIKE '70548CE8-4163-4FF0-B835-3A70EDA60496%' AND b.ID IN (SELECT ResID FROM A_GroupRes WHERE GroupID in ({0}) or GroupID = {1}) ORDER BY a.SortIndex";
            maps = sqlHelper.executeReader(SQLHelper.format(authSql, userRelation, "'AllUser'"));
            String dataSql = "SELECT  a.Id,a.Name,CDMC AS ResId,CDMCName AS ResName,a.ParentID,a.FullID,a.SortIndex,b.Url,a.TB AS Icon FROM OA.dbo.T_Tree_ResInfo a LEFT JOIN KMYZH_SystemDataBase.dbo.A_Res b ON a.CDMC = b.ID WHERE(a.IsDeleted IS NULL OR a.IsDeleted = '0' ) AND a.FullID LIKE '70548CE8-4163-4FF0-B835-3A70EDA60496%' ORDER BY a.SortIndex; ";
            maps1 = sqlHelper.executeReader(dataSql);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            sqlHelper.close();
        }
        return buildTree(maps1, maps);
    }

    //构建树
    public List<ProjectTree> buildTree(List<Map<String, Object>> dataMaps, List<Map<String, Object>> authMaps) {
        List<ProjectTree> list = new ArrayList<>();
        dataMaps.stream().forEach(item -> {
            ProjectTree tree = new ProjectTree();
            tree.setFullId(item.get("FullID") != null ? item.get("FullID").toString() : "");
            tree.setIcon(item.get("Icon") != null ? item.get("Icon").toString() : "");
            tree.setId(item.get("Id") != null ? item.get("Id").toString() : "");
            tree.setName(item.get("Name") != null ? item.get("Name").toString() : "");
            tree.setParentId(item.get("ParentID") != null ? item.get("ParentID").toString() : null);
            tree.setResId(item.get("ResId") != null ? item.get("ResId").toString() : "");
            tree.setResName(item.get("ResName") != null ? item.get("ResName").toString() : "");
            tree.setSortIndex(item.get("SortIndex") != null ? item.get("SortIndex").toString() : "");
            tree.setUrl(item.get("Url") != null ? item.get("Url").toString() : "");
            authMaps.stream().forEach(authItem -> {
                if (authItem.get("Id").toString().equals(item.get("Id").toString())) {
                    tree.setAuth(1);
                } else {
                    tree.setAuth(0);
                }
            });
            list.add(tree);
        });
        return ProjectTree.buildTree(list);
    }

    //获取用户角色，组织信息，用，拼接起来
    public String getUserRelation(String userId) {
        String userRelation = quote(userId);
        // 获取动态角色
        String vRoleID = GetVariableRoleByUserID(userId);
        if (vRoleID != null && !vRoleID.isEmpty()) {
            userRelation += "," + quote(vRoleID);
        }
        // 获取系统角色/组织
        SQLHelper sqlHelper = null;
        try {
            sqlHelper = SQLHelper.createSqlHelper("Core");
            List<Map<String, Object>> maps = sqlHelper.executeReader(SQLConstant.GetUserRelation, userId);
            // 构建系统角色/组织
            String deptFullIDs = maps.stream()
                    .filter(map -> map.get("DeptFullID") != null && !map.get("DeptFullID").toString().isEmpty())
                    .map(map -> quote(map.get("DeptFullID")))
                    .collect(Collectors.joining(","));
            // 构建组织角色
            String orgRoles = maps.stream()
                    .filter(map -> map.get("OrgRole") != null && !map.get("OrgRole").toString().isEmpty())
                    .map(map -> quote(map.get("OrgRole")))
                    .collect(Collectors.joining(","));
            // 拼接字符串
            userRelation += "," + deptFullIDs + "," + orgRoles;
            // 移除最后一个逗号
            if (userRelation.endsWith(",")) {
                userRelation = userRelation.substring(0, userRelation.length() - 1);
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (sqlHelper != null) {
                sqlHelper.close();
            }
        }
        return userRelation;
    }

    //取人员拥有的动态变量ID
    public String GetVariableRoleByUserID(String userId) {
        if (userId == null || userId.equals("")) {
            return null;
        }
        SQLHelper sqlHelper = null;
        SQLHelper sqlHelper1 = null;
        String roleIds = "";
        try {
            sqlHelper = SQLHelper.createSqlHelper("Core");
            List<Map<String, Object>> maps = sqlHelper.executeReader(SQLConstant.GetUserRelation, RoleType.VariableRole.getCode());
            for (Map<String, Object> item : maps) {
                if (StringUtil.isEmpty(item.get("ConnName").toString()) || StringUtil.isEmpty(item.get("UserSQL").toString())) {
                    continue;
                }
                String userSQL = SQLHelper.format(item.get("UserSQL").toString());
                userSQL = "select count(*) VarCount from (" + userSQL + ") a where UserID='" + userId + "'";
                if (userSQL.indexOf("{") > 0 && userSQL.indexOf("}") > 0) {
                    continue;
                }
                sqlHelper1 = SQLHelper.createSqlHelper(item.get("ConnName").toString());
                Map<String, Object> userRelation = sqlHelper1.selectFirstRow(userSQL);
                if (userRelation != null && Integer.parseInt(userRelation.get("VarCount").toString()) > 0) {
                    roleIds += item.get("ID").toString() + ",";
                }
            }
        } finally {
            if (sqlHelper1 != null) {
                sqlHelper1.close();
            }
            sqlHelper.close();
        }
        return roleIds;
    }

    //拼接字符串
    private String quote(Object value) {
        //return value.toString();
        return "'" + value.toString() + "'";
    }

    @Override
    public Map<String, Object> getMsgCount(String userId) {
        Map<String, Object> map = new HashMap<>();
        //查询待办工作数
        Page<Map<String, Object>> newtask = workCenterService.getMyWork("newtask", 1, 10, null);
        //查询未读消息数
        map.put("NewTaskCount", newtask.getTotal());
        SQLHelper sqlHelper = null;
        try {
            sqlHelper = SQLHelper.createSqlHelper("Core");
            String msgCountSql = "SELECT COUNT(1) FROM KMYZH_SystemDataBase.dbo.V_MsgReceiveList WHERE UserID='{0}' AND AlreadyRead = 0";
            Map<String, Object> msgCount = sqlHelper.selectFirstRow(SQLHelper.format(msgCountSql, userId));
            map.put("NewMsgCount", msgCount.get("COUNT"));
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            sqlHelper.close();
        }
        return map;
    }

    /**
     * 请求4A系统身份认证接口，设置当前用户在4A系统中的token
     */
    public boolean AuthenticateBy4A(String remoteAddr, AUserDO loginUser) {

        if (!CheckUserTokenValidBy4A(loginUser.getId(), remoteAddr)) {
            String interfaceUrl4A = configurationHelper.GetSettingValue("InterfaceUrl4A");//4A认证接口
            String appID4A = configurationHelper.GetSettingValue("appId4A");//4A提供的门户系统应用的appId
            String url = interfaceUrl4A + "/idp/restful/idpInternalAuthenticateByUsername"; //身份认证接口地址

            JSONObject jsonObject = getInsertParm(appID4A, loginUser.getWorkNo(), remoteAddr, url);
            Object object = jsonObject.get("data");
            JSONObject json = JSONUtil.parseObj(object);
            if (ObjUtil.isEmpty(json.get("tokenId"))) {
                log.error("内部请求获取参数失败：{}", JSONUtil.parseObj(jsonObject));
                return false;
            }
            return InsTokenBy4A(GuidUtil.getID(), loginUser.getId(), json.get("tokenId"), 86400, remoteAddr);
        }
        return true;
    }

    @Override
    public String set4ACookie(String remoteAddr, String userId) {
        String interfaceUrl4A = configurationHelper.GetSettingValue("InterfaceUrl4A");//4A认证接口
        String appID4A = configurationHelper.GetSettingValue("appId4A");
        String tokenId = GetUserTokenBy4A(remoteAddr, userId);
        return interfaceUrl4A + "/idp/restful/setIDPCookie?appId=" + appID4A + "&tokenId=" + URLEncoder.encode(tokenId, StandardCharsets.UTF_8) + "&remoteIp=" + remoteAddr + "&jsonpCallback=set4Acookie";
    }


    /**
     * 处理获取发送电建通验证码
     *
     * @param workNo    登录用户
     * @param sendType  发送类型
     * @param appWorkNo
     * @param request
     * @return 验证码
     */
    @Override
    public String GetVerificationCode(String workNo, Integer sendType, String appWorkNo, HttpServletRequest request) {
        AUserDO user = aUserService.getUserSimpInfoByWorkNo(workNo);

        if (ObjUtil.isEmpty(user)) {
            throw new BaseException("处理获取电建通验证码，用户不存在，用户账号：" + workNo);
        }
        String id = UUID.randomUUID().toString();
        int code = new Random().nextInt(999999 - 100000 + 1) + 100000;
        String sql = "INSERT INTO dbo.A_UserVerificationCode(UserID, WorkNo, Code, Verification, SendType, ID, ExpireTime) VALUES ('{0}','{1}','{2}','{3}','{4}','{5}','{6}')";

        String value = configurationHelper.GetSettingValue("ModifyPwdCode");
        //处理发送信息
        String verification = value.replace("{IpAddress}", request.getRemoteAddr()).replace("{Code}", code + "");
        String formatSQL = SQLHelper.format(sql, user.getId(), user.getWorkNo(), code, verification, sendType, id, 300);

        try (SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core")) {
            int i = sqlHelper.executeNonQuery(formatSQL);
            if (i != 1) {
                throw new BaseException("处理获取电建通验证码，添加验证码数据异常，表：A_UserVerificationCode");
            }
        }

        //发送请求发送code
        String url = configurationHelper.GetSettingValue("VerificationCodeUrl");
        Map<String, String> map = new HashMap<>();
        map.put("id", appWorkNo);
        map.put("title", "【昆明院】验证码：" + code);
        map.put("description", verification);

        String string = remoteRequest(url, map);
        JSONObject jsonObject = JSONUtil.parseObj(string);

        if (!ObjUtil.isEmpty(jsonObject.get("code")) && Integer.parseInt(jsonObject.get("code").toString()) == 200) {
            Object object = jsonObject.get("data");
            JSONObject data = JSONUtil.parseObj(object);
            Object dataCode = data.get("errcode");
            if (!ObjUtil.isEmpty(dataCode) && dataCode.equals(0)) {
                return id;
            }
        }
        throw new BaseException("数据响应异常" + string);
    }




    private String remoteRequest(String url, Map<String, String> params) {
        if (ObjUtil.isEmpty(url)) {
            throw new BaseException("获取发送code验证码url路径异常");
        }
        try {
            HttpClient client = HttpClients.createDefault();
            HttpPost post = new HttpPost(url);
            post.setHeader("content-type", "application/json");
            post.setHeader("token", "qizh_kmy_test");

            if (params != null) {
                post.setEntity(new StringEntity(JSONUtil.toJsonStr(params), "UTF-8"));
            }

            HttpResponse response = client.execute(post);
            if (response.getStatusLine().getStatusCode() == 200) {
                HttpEntity entity = response.getEntity();
                return EntityUtils.toString(entity);
            } else {
                throw new BaseException("响应状态码：" + response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            throw new BaseException("发送请求发送code,url:" + url + "异常信息：" + e.getMessage());
        }

    }


    /**
     * 校验当前用户4A系统tokenId有效性，无效时重新请求获取
     *
     * @return
     */
    public Boolean CheckUserTokenValidBy4A(String userId, String remoteAddr) {
        String interfaceUrl4A = configurationHelper.GetSettingValue("InterfaceUrl4A");
        String appID4A = configurationHelper.GetSettingValue("appId4A");
        String tokenId = GetUserTokenBy4A(remoteAddr, userId);
        String url = interfaceUrl4A + "/idp/restful/isIDPTokenValid";
        return reqPost(appID4A, tokenId, remoteAddr, url);
    }


    /**
     * 获取当前用户4A系统Token
     *
     * @return token
     */
    public String GetUserTokenBy4A(String userIp, String userId) {
        String format = SQLHelper.format(GetUserTokenBy4A, userId, userIp);
        try (SQLHelper core = SQLHelper.CreateSqlHelper("Core")) {
            Map<String, Object> map = core.selectFirstRow(format);
            if (ObjUtil.isEmpty(map)) {
                return "";
            }
            return map.get("Token").toString();
        }
    }

    private boolean InsTokenBy4A(String guId, String userId, Object tokenId, int num, String remoteAddr) {

        String delSql = "Delete A_Token_4A where UserID='{0}' and UserIP='{1}'";

        String sql = SQLHelper.format(InsTokenBy4A, guId, userId, tokenId, num, remoteAddr);
        try (SQLHelper core = SQLHelper.CreateSqlHelper("Core")) {
            //移除数据
            core.executeNonQuery(SQLHelper.format(delSql, userId, remoteAddr));
            //插入数据
            core.executeNonQuery(sql);
        }
        return true;
    }


    private Boolean reqPost(String appID4A, String tokenId, String remoteAddr, String url) {

        HttpClient client = HttpClients.createDefault();
        HttpPost post = new HttpPost(url);
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("appId", appID4A));
        params.add(new BasicNameValuePair("tokenId", tokenId));
        params.add(new BasicNameValuePair("remoteIp", remoteAddr));
        try {
            post.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));
            post.setHeader("content-type", "application/x-www-form-urlencoded");
            HttpResponse response = client.execute(post);
            if (response.getStatusLine().getStatusCode() == 200) {
                HttpEntity entity = response.getEntity();
                String json = EntityUtils.toString(entity);
                JSONObject ret = JSONUtil.parseObj(json);
                Object data = ret.get("data");
                JSONObject dataJson = JSONUtil.parseObj(data);
                return "true".equals(dataJson.get("valid"));
            }
        } catch (IOException e) {
            throw new BaseException("4A认证,内部请求url路径访问异常", e.getMessage());
        }
        return false;
    }

    private JSONObject getInsertParm(String appID4A, Object workNo, String remoteAddr, String url) {
        HttpClient client = HttpClients.createDefault();
        HttpPost post = new HttpPost(url);
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("appId", appID4A));
        params.add(new BasicNameValuePair("userName", workNo.toString()));
        params.add(new BasicNameValuePair("authnMethod", "UsernamePassword"));
        params.add(new BasicNameValuePair("remoteIp", remoteAddr));
        try {
            post.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));
            post.setHeader("content-type", "application/x-www-form-urlencoded");
            HttpResponse response = client.execute(post);
            if (response.getStatusLine().getStatusCode() == 200) {
                HttpEntity entity = response.getEntity();
                String json = EntityUtils.toString(entity);
                JSONObject ret = JSONUtil.parseObj(json);
                return ret;
            }
            throw new BaseException("4A认证,内部请求异常,响应数据", EntityUtils.toString(response.getEntity()));
        } catch (IOException e) {
            throw new BaseException("4A认证,获取插入Token参数，请求url路径异常", e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getZXZXHeaderImg() {
        try (SQLHelper oa = SQLHelper.createSqlHelper("OA")) {
            String strsql = "select * from T_Page_NewsCenterBannerImg where (IsDeleted<>'1' or IsDeleted is null) AND [show] = 1 ORDER BY [sort] ASC";
            List<Map<String, Object>> maps = oa.selectRows(strsql);
            return maps;
        }
    }
}
