package mh.cloud.module.system.service.auth;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.annotations.VisibleForTesting;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.Validator;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import mh.cloud.framework.common.enums.UserTypeEnum;
import mh.cloud.framework.common.exception.ErrorCode;
import mh.cloud.framework.common.util.monitor.TracerUtils;
import mh.cloud.framework.common.util.servlet.ServletUtils;
import mh.cloud.framework.security.core.LoginUser;
import mh.cloud.framework.security.core.util.SecurityFrameworkUtils;
import mh.cloud.module.system.api.logger.dto.LoginLogCreateReqDTO;
import mh.cloud.module.system.api.sms.SmsCodeApi;
import mh.cloud.module.system.api.social.dto.SocialUserBindReqDTO;
import mh.cloud.module.system.api.social.dto.SocialUserRespDTO;
import mh.cloud.module.system.controller.admin.auser.vo.AUserSaveReqVO;
import mh.cloud.module.system.controller.admin.auth.vo.*;
import mh.cloud.module.system.controller.admin.portal.V0.AuthLoginVo;
import mh.cloud.module.system.controller.admin.portal.V0.UserInfo;
import mh.cloud.module.system.controller.admin.portal.constant.QRCodeConstant;
import mh.cloud.module.system.convert.auth.AuthConvert;
import mh.cloud.module.system.dal.dataobject.auser.AUserDO;
import mh.cloud.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import mh.cloud.module.system.dal.dataobject.user.AdminUserDO;
import mh.cloud.module.system.enums.logger.LoginLogTypeEnum;
import mh.cloud.module.system.enums.logger.LoginResultEnum;
import mh.cloud.module.system.enums.oauth2.OAuth2ClientConstants;
import mh.cloud.module.system.enums.sms.SmsSceneEnum;
import mh.cloud.module.system.service.auser.AUserService;
import mh.cloud.module.system.service.db.SQLHelper;
import mh.cloud.module.system.service.grouprelation.GrouprelationService;
import mh.cloud.module.system.service.logger.LoginLogService;
import mh.cloud.module.system.service.member.MemberService;
import mh.cloud.module.system.service.oauth2.OAuth2TokenService;
import mh.cloud.module.system.service.social.SocialUserService;
import mh.cloud.module.system.service.user.AdminUserService;
import mh.cloud.module.system.util.PatternMatchUtil;
import org.dromara.hutool.core.collection.CollUtil;
import org.dromara.hutool.core.text.StrUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.text.MessageFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;

import static mh.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static mh.cloud.framework.common.util.servlet.ServletUtils.getClientIP;
import static mh.cloud.module.system.enums.ErrorCodeConstants.*;

/**
 * Auth Service 实现类
 */
@Service
@Slf4j
public class AdminAuthServiceImpl implements AdminAuthService {

    @Resource
    private AdminUserService userService;
    @Resource
    private LoginLogService loginLogService;
    @Resource
    private OAuth2TokenService oauth2TokenService;
    @Resource
    private SocialUserService socialUserService;
    @Resource
    private MemberService memberService;
    @Resource
    private Validator validator;

    @Resource
    private SmsCodeApi smsCodeApi;
    @Resource
    private GrouprelationService grouprelationService;

    @Resource
    private AUserService aUserService;


    /**
     * 验证码的开关，默认为 true
     */
    @Value("${microservices.captcha.enable:true}")
    private Boolean captchaEnable;

    @Resource
    private PatternMatchUtil patternMatchUtil;

    @Override
    public AUserDO authenticate(String username, String password) {
        final LoginLogTypeEnum logTypeEnum = LoginLogTypeEnum.LOGIN_USERNAME;
        // 校验账号是否存在
        //AdminUserDO user = userService.getUserByUsername(username);

        AUserDO aUser = aUserService.getByUserName(username);
        if (aUser == null) {
            createLoginLog(null, username, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
        }
        if (aUser.getErrorCount() == null) {
            aUser.setErrorCount(0);
        }
        Integer loginErrorCount = Integer.valueOf(patternMatchUtil.GetSettingValue("PasswordErrorCount"));
        Integer loginErrorTime = Integer.valueOf(patternMatchUtil.GetSettingValue("SysErrorTime"));
        Integer hours = loginErrorTime / 3600;
        String minutes;
        if (hours > 1) {
            minutes = hours + "小时";
        } else {
            minutes = loginErrorTime / 60 + "分钟";
        }
        if (aUser.getErrorCount() >= loginErrorCount) {
            Duration between = Duration.between(aUser.getErrorTime(), LocalDateTime.now());
            if (Math.abs(between.getSeconds()) < loginErrorTime) {
                createLoginLog(aUser.getId(), username, logTypeEnum, LoginResultEnum.COUNT_ERROR);
                String countError = COUNT_ERROR.getMsg();
                Integer code = COUNT_ERROR.getCode();
                ErrorCode errorCode = new ErrorCode(code, MessageFormat.format(countError, minutes));
                throw exception(errorCode);
            } else {
                aUserService.updateLoginFailCount(aUser.getId(), 0);
            }
        }

        //校验密码这里从新实现
        if (!userService.isPasswordMatch(password, aUser.getPassword())) {
            aUserService.updateLoginFailCount(aUser.getId(), aUser.getErrorCount() + 1);
            createLoginLog(aUser.getId(), username, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
        }
        // 校验是否禁用
        /*if (CommonStatusEnum.isDisable(Integer.valueOf(aUser.getStatus()))) {
            createLoginLog(aUser.getId(), username, logTypeEnum, LoginResultEnum.USER_DISABLED);
            throw exception(AUTH_LOGIN_USER_DISABLED);
        }*/
        return aUser;
    }

    @Override
    public AuthLoginRespVO login(AuthLoginReqVO reqVO) {
        // 校验验证码
        validateCaptcha(reqVO);

        // 使用账号密码，进行登录
        AUserDO user = authenticate(reqVO.getUsername(), reqVO.getPassword());
        // 如果 socialType 非空，说明需要绑定社交用户
        if (reqVO.getSocialType() != null) {
            socialUserService.bindSocialUser(new SocialUserBindReqDTO(user.getId(), getUserType().getValue(), reqVO.getSocialType(), reqVO.getSocialCode(), reqVO.getSocialState()));
        }
        // 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(user.getId(), reqVO.getUsername(), LoginLogTypeEnum.LOGIN_USERNAME);
    }

    @Override
    public AuthLoginVo authLogin(AuthLoginReqVO reqVO) {
        // 校验验证码
        validateCaptcha(reqVO);
        String username = reqVO.getUsername();
        String password = reqVO.getPassword();

        // 使用账号密码，进行登录
        final LoginLogTypeEnum logTypeEnum = LoginLogTypeEnum.LOGIN_USERNAME;
        AUserDO aUser = aUserService.getByUserName(username);
        Map<String, Object> request = new HashMap<>();
        // 准备失败返回对象
        AuthLoginVo failResponse = new AuthLoginVo();
        failResponse.setCheckPass(false);
        failResponse.setToken("");
        failResponse.setUserInfo(null);
        // 第一步：验证用户是否存在
        if (aUser == null) {
            createLoginLog(null, username, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            failResponse.setCheckInfo(AUTH_LOGIN_USER_PASSWORD_ERROR.getMsg());
            return failResponse;
        }
        // 第二步：验证用户是否已删除
        if (!"0".equals(aUser.getIsDeleted())) {
            createLoginLog(null, username, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            failResponse.setCheckInfo(AUTH_LOGIN_USER_PASSWORD_ERROR.getMsg());
            return failResponse;
        }
        // 第三步：验证用户是否已授权
        if (!"1".equals(aUser.getIsAuth())) {
            createLoginLog(aUser.getId(), username, logTypeEnum, LoginResultEnum.USER_UNAUTHORIZED);
            failResponse.setCheckInfo(AUTH_LOGIN_USER_PASSWORD_ERROR.getMsg());
            return failResponse;
        }
        // 第四步：验证错误次数限制
        Integer loginErrorCount = Integer.valueOf(patternMatchUtil.GetSettingValue("PasswordErrorCount"));
        Integer loginErrorTime = Integer.valueOf(patternMatchUtil.GetSettingValue("SysErrorTime"));
        Integer hours = loginErrorTime / 3600;
        String minutes;
        if (hours > 1) {
            minutes = hours + "小时";
        } else {
            minutes = loginErrorTime / 60 + "分钟";
        }
        if (aUser.getErrorCount() >= loginErrorCount) {
            long secondsSinceLastError = Duration.between(aUser.getErrorTime(), LocalDateTime.now()).getSeconds();
            if (secondsSinceLastError < loginErrorTime) {
                createLoginLog(aUser.getId(), username, logTypeEnum, LoginResultEnum.COUNT_ERROR);
                failResponse.setCheckInfo(MessageFormat.format(AUTH_LOGIN_USER_PASSWORD_NUMBER_ERROR.getMsg(), minutes));
                return failResponse;
            } else {
                // 超过锁定时间，重置错误计数
                aUserService.updateLoginFailCount(aUser.getId(), 0);
            }
        }

        // 第五步：验证密码
        if (!userService.isPasswordMatch(password, aUser.getPassword())) {
            aUserService.updateLoginFailCount(aUser.getId(), aUser.getErrorCount() + 1);
            createLoginLog(aUser.getId(), username, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            failResponse.setCheckInfo(AUTH_LOGIN_USER_PASSWORD_ERROR.getMsg());
            return failResponse;
        }
        // 登录成功处理
        UserInfo userInfo = buildUserInfo(aUser);
        // 如果 socialType 非空，说明需要绑定社交用户
        if (reqVO.getSocialType() != null) {
            socialUserService.bindSocialUser(new SocialUserBindReqDTO(userInfo.getId(), getUserType().getValue(),
                    reqVO.getSocialType(), reqVO.getSocialCode(), reqVO.getSocialState()));
        }
        AuthLoginRespVO tokenResponse = createTokenAfterLoginSuccess(userInfo.getId(), username, logTypeEnum);

        AuthLoginVo successResponse = new AuthLoginVo();
        successResponse.setCheckPass(true);
        successResponse.setToken(tokenResponse.getAccessToken());
        successResponse.setCheckInfo(AUTH_LOGIN_REQUEST_SUCCESS.getMsg());
        successResponse.setUserInfo(userInfo);
        return successResponse;
    }

    private UserInfo buildUserInfo(AUserDO aUser) {
        UserInfo userInfo = new UserInfo();
        userInfo.setId(aUser.getId());
        userInfo.setName(aUser.getName());
        userInfo.setLoginName(aUser.getLoginName());
        userInfo.setWorkNo(aUser.getWorkNo());
        userInfo.setDeptID(aUser.getDeptID());
        userInfo.setDeptName(aUser.getDeptName());
        userInfo.setDeptFullID(aUser.getDeptFullID());
        userInfo.setPhone(aUser.getPhone());
        userInfo.setMobilePhone(aUser.getMobilePhone());
        userInfo.setSex(aUser.getSex());
        userInfo.setEmail(aUser.getEmail());
        userInfo.setAddress(aUser.getAddress());
        userInfo.setDuties(aUser.getDuties());
        userInfo.setSortIndex(aUser.getSortIndex());
        return userInfo;
    }

    public AuthLoginRespVO mockLogin(AuthLoginReqVO reqVO) {
        // 校验验证码
        validateCaptcha(reqVO);

        // 使用账号密码，进行登录
        AUserDO user = authenticate(reqVO.getUsername(), reqVO.getPassword());

        //检验当前用户是不是开发人员或者主控管理员
        if (!(grouprelationService.CheckIsRole("PROP10C8", user.getId()) || grouprelationService.CheckIsRole("529951F3-714C-4AD8-8461-071492EB7C4B", user.getId()))) {
            throw exception(RoleUser_NOT_EXISTS);
        }

        // 如果 socialType 非空，说明需要绑定社交用户
        if (reqVO.getSocialType() != null) {
            socialUserService.bindSocialUser(new SocialUserBindReqDTO(user.getId(), getUserType().getValue(), reqVO.getSocialType(), reqVO.getSocialCode(), reqVO.getSocialState()));
        }

        //获取模拟登陆用户信息
        AUserDO aUser = aUserService.getByUserName(reqVO.getMockusername());
        if (aUser == null) {
            throw exception(USER_NOT_EXISTS);
        }
        // 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(aUser.getId(), aUser.getName(), LoginLogTypeEnum.LOGIN_USERNAME);
    }

    @Override
    public void sendSmsCode(AuthSmsSendReqVO reqVO) {
        // 登录场景，验证是否存在
        if (userService.getUserByMobile(reqVO.getMobile()) == null) {
            throw exception(AUTH_MOBILE_NOT_EXISTS);
        }
        // 发送验证码
        smsCodeApi.sendSmsCode(AuthConvert.INSTANCE.convert(reqVO).setCreateIp(getClientIP()));
    }

    @Override
    public AuthLoginRespVO smsLogin(AuthSmsLoginReqVO reqVO) {
        // 校验验证码
        smsCodeApi.useSmsCode(AuthConvert.INSTANCE.convert(reqVO, SmsSceneEnum.ADMIN_MEMBER_LOGIN.getScene(), getClientIP())).getCheckedData();

        // 获得用户信息
        AdminUserDO user = userService.getUserByMobile(reqVO.getMobile());
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }

        // 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(user.getId(), reqVO.getMobile(), LoginLogTypeEnum.LOGIN_MOBILE);
    }

    private void createLoginLog(String userId, String username, LoginLogTypeEnum logTypeEnum, LoginResultEnum loginResult) {
        // 插入登录日志
        LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
        reqDTO.setLogType(logTypeEnum.getType());
        reqDTO.setTraceId(TracerUtils.getTraceId());
        reqDTO.setUserId(userId);
        reqDTO.setUserType(getUserType().getValue());
        reqDTO.setUsername(username);
        reqDTO.setUserAgent(ServletUtils.getUserAgent());
        reqDTO.setUserIp(getClientIP());
        reqDTO.setResult(loginResult.getResult());
        loginLogService.createLoginLog(reqDTO);
        // 更新最后登录时间
        if (userId != null && Objects.equals(LoginResultEnum.SUCCESS.getResult(), loginResult.getResult())) {
//            userService.updateUserLogin(userId, getClientIP());
            AUserDO aUser = aUserService.getAUser(userId);
            aUser.setLastLoginIP(getClientIP());
            aUser.setModifyTime(LocalDateTime.now());
            @Valid AUserSaveReqVO aUser1 = new AUserSaveReqVO();
            BeanUtils.copyProperties(aUser, aUser1);
            ///aUserService.updateAUser(aUser1);
            aUserService.updateUserLastTime(aUser1);
        }
    }

    @Override
    public AuthLoginRespVO socialLogin(AuthSocialLoginReqVO reqVO) {
        // 使用 code 授权码，进行登录。然后，获得到绑定的用户编号
        SocialUserRespDTO socialUser = socialUserService.getSocialUserByCode(UserTypeEnum.ADMIN.getValue(), reqVO.getType(), reqVO.getCode(), reqVO.getState());
        if (socialUser == null || socialUser.getUserId() == null) {
            throw exception(AUTH_THIRD_LOGIN_NOT_BIND);
        }

        // 获得用户
        AdminUserDO user = userService.getUser(socialUser.getUserId());
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }

        // 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(user.getId(), user.getUsername(), LoginLogTypeEnum.LOGIN_SOCIAL);
    }

    @VisibleForTesting
    void validateCaptcha(AuthLoginReqVO reqVO) {
        // 如果验证码关闭，则不进行校验
        if (!captchaEnable) {
            return;
        }
    }

    public AuthLoginRespVO createTokenAfterLoginSuccess(String userId, String username, LoginLogTypeEnum logType) {
        // 插入登陆日志
        createLoginLog(userId, username, logType, LoginResultEnum.SUCCESS);
        // 创建访问令牌
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.createAccessToken(userId, getUserType().getValue(), OAuth2ClientConstants.CLIENT_ID_DEFAULT, null);
        //创建外部系统访问令牌 不需要登录就创建
        //getOutSystemToken(userId);
        // 构建返回结果
        return AuthConvert.INSTANCE.convert(accessTokenDO);
    }

    public String getOutSystemToken(String userId){
        SQLHelper sqlHelper = SQLHelper.createSqlHelper("Core");
        String sql = "SELECT * FROM A_Token_OutSystem WHERE UserID = '" + userId + "' AND DATEADD(S,ExpireTime,CreateTime) > GETDATE()";
        List<Map<String, Object>> dt_token = sqlHelper.executeReader(sql);
        String token = "";
        if (dt_token.isEmpty()){
            String expireTime = "86400";
            token = IdUtil.fastSimpleUUID();
            String clientIP = getClientIP();
            sql = "DELETE A_Token_OutSystem WHERE [UserID] = '" + userId + "';\n" +
                    "INSERT INTO A_Token_OutSystem(ID,UserID,Token,CreateTime,ExpireTime,UserIP,tenant_id)" +
                    "SELECT NEWID(),'" + userId + "','" + token + "',GETDATE()," + expireTime + ",'" + clientIP + "','1';";
            sqlHelper.executeNonQuery(sql);
        }else {
            token = dt_token.get(0).get("Token").toString();
        }
        return token;
    }

    @Override
    public AuthLoginRespVO refreshToken(String refreshToken) {
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.refreshAccessToken(refreshToken, OAuth2ClientConstants.CLIENT_ID_DEFAULT);
        return AuthConvert.INSTANCE.convert(accessTokenDO);
    }

    @Override
    public AuthLoginRespVO writeToken(String decrypt) {
        AUserDO userInfo = aUserService.getAUserInfo(decrypt);
        //创建token
        return createTokenAfterLoginSuccess(userInfo.getId(), userInfo.getLoginName(), LoginLogTypeEnum.LOGIN_REST);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void logout(String token, Integer logType) {
        // 删除访问令牌
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.removeAccessToken(token);
        if (accessTokenDO == null) {
            return;
        }
        // 删除成功，则记录登出日志
        createLogoutLog(accessTokenDO.getUserId(), accessTokenDO.getUserType(), logType);
    }

    private void createLogoutLog(String userId, Integer userType, Integer logType) {
        LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
        reqDTO.setLogType(logType);
        reqDTO.setTraceId(TracerUtils.getTraceId());
        reqDTO.setUserId(userId);
        reqDTO.setUserType(userType);
        if (ObjectUtil.equal(getUserType().getValue(), userType)) {
            AUserDO aUser = aUserService.getAUser(userId);
            reqDTO.setUsername(aUser.getName());
        } else {
            reqDTO.setUsername(memberService.getMemberUserMobile(userId));
//
        }
        reqDTO.setUserAgent(ServletUtils.getUserAgent());
        reqDTO.setUserIp(getClientIP());
        reqDTO.setResult(LoginResultEnum.SUCCESS.getResult());
        loginLogService.createLoginLog(reqDTO);
    }

    private String getUsername(String userId) {
        if (userId == null) {
            return null;
        }
        AdminUserDO user = userService.getUser(userId);
        return user != null ? user.getUsername() : null;
    }

    private UserTypeEnum getUserType() {
        return UserTypeEnum.ADMIN;
    }

    @Override
    public Map<String, Object> getUserInfo(String workNo) {
        SQLHelper sqlHelper = null;
        Map<String, Object> map = null;
        try {
            sqlHelper = SQLHelper.createSqlHelper("Core");
            String authSql = "SELECT DJTUserName,KMYMobile,KMYWorkNo FROM [KMYZH_SystemDataBase].[dbo].[T_Page_MobileUserSync] WHERE KMYWorkNo='{0}' AND KMYIsDelete = '0'";
            map = sqlHelper.selectFirstRow(SQLHelper.format(authSql, workNo));
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            sqlHelper.close();
        }
        return map;
    }

    /**
     * 获取用户登录二维码
     *
     * @param key 校验秘钥
     * @return 二维码url数据
     */
    @Override
    public Map<String, Object> getQRCode(String key) {

        //2、封装数据 测试数据
        String uuId = QRCodeConstant.getKey();
        //将二维码状态信息存储到内存
        QRCodeConstant.setQRCodeStatus(uuId, QRCodeConstant.QR_IN_HAND);
        Map<String, Object> map = new HashMap<>();
        map.put("uuId", uuId);
        return map;
    }

    /**
     * 获取登录状态
     *
     * @param uuid 标识信息
     * @return 登录状态
     */
    @Override
    public Map<String, Object> getQRcCodeState(String uuid) {
        Map<String, Object> qrCodeStatus = QRCodeConstant.getQRCodeStatus(uuid);
        //如果回调成功，token数据返回
        if (qrCodeStatus.get("status").equals(QRCodeConstant.QR_SUCCESS)) {
            return QRCodeConstant.getUserToken(uuid);
        }
        return qrCodeStatus;
    }

    /**
     * 处理用户回调，获取token数据
     *
     * @param key
     * @param userId
     */
    @Override
    public boolean handleQRCallBack(String key, String userId) {
        //获取用户信息
        AUserDO aUser = aUserService.getAUser(userId);
        if (ObjUtil.isEmpty(aUser)) {
            log.error("二维码登录回调，用户信息异常：user={}", aUser);
            return false;
        }
        AuthLoginRespVO loginRespVO = createTokenAfterLoginSuccess(aUser.getId(), aUser.getLoginName(), LoginLogTypeEnum.LOGIN_QRCODE);
        QRCodeConstant.setUserToken(key, loginRespVO);
        QRCodeConstant.setQRCodeStatus(key, QRCodeConstant.QR_SUCCESS);
        return true;
    }

    //用户工号登录(目前:电建通扫码登录使用)
    public AuthLoginRespVO loginWorkNo(String workNo, LoginLogTypeEnum logType) {
        //根据工号查询用户userId
        Map<String, Object> map = null;
        SQLHelper sqlHelper = null;

        try {
            sqlHelper = SQLHelper.createSqlHelper("Core");
            String authSql = "SELECT ID,Name FROM [KMYZH_SystemDataBase].[dbo].[A_User] WHERE WorkNo='{0}'";
            map = sqlHelper.selectFirstRow(SQLHelper.format(authSql, workNo));
        } catch (Exception e) {
            map = null;
        } finally {
            sqlHelper.close();
        }

        if (map != null) {
            //获取用户id
            String userId = map.get("ID").toString();
            String name = map.get("Name").toString();

            // 创建访问令牌
            OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.createAccessToken(userId, getUserType().getValue(), "djt", null);

            //记录日志
            createLoginLog(userId, name, logType, LoginResultEnum.SUCCESS);

            // 构建返回结果
            return AuthConvert.INSTANCE.convert(accessTokenDO);
        }

        return null;
    }

    @Override
    public List<Map<String, Object>> getPasswordLog(String workNo) {
        SQLHelper sqlHelper = null;
        try {
            // 使用 StringBuilder 构建 SQL，提高性能
            StringBuilder sqlBuilder = new StringBuilder();

            sqlBuilder.append(" SELECT au.[ID] as UserID,au.[WorkNo],au.[Name],au.[DeptID],au.[DeptName],au.[OfficeId],au.[OfficeName],cpl.[CurrentPasswordMD5],cpl.[CurrentChangeTime]")
                    .append(" FROM [A_User] au LEFT JOIN (")
                    .append(" SELECT [UserID], [CurrentPasswordMD5], [CurrentChangeTime]")
                    .append(" FROM (")
                    .append(" SELECT [UserID],[CurrentPasswordMD5],[CurrentChangeTime],")
                    .append(" ROW_NUMBER() OVER (PARTITION BY [UserID] ORDER BY [CurrentChangeTime] DESC) as rn")
                    .append(" FROM C_ChangePasswordLog) t")
                    .append(" WHERE t.rn = 1) cpl ON au.[ID] =cpl.[UserID]")
                    .append(" WHERE au.[IsDeleted] ='0' AND CurrentPasswordMD5 IS NOT NULL");


            // SQL 拼接
            if (StrUtil.isNotEmpty(workNo)) {
                sqlBuilder.append(" AND [WorkNo] ='{0}' ");
            }
            sqlBuilder.append(" ORDER BY [CurrentChangeTime] DESC");

            String sql = sqlBuilder.toString();
            sqlHelper = SQLHelper.createSqlHelper("Core");

            // 使用参数化查询，提高安全性
            List<Map<String, Object>> maps;
            if (StrUtil.isNotEmpty(workNo)) {
                maps = sqlHelper.selectRows(SQLHelper.format(sql, workNo));
            } else {
                maps = sqlHelper.selectRows(sql);
            }
            return maps;
        } catch (Exception e) {
            log.error("getPasswordLog error", e);
            return null;
        } finally {
            sqlHelper.close();
        }
    }

    @Override
    @Transactional
    public boolean addPasswordLog(String workNo) {

        SQLHelper sqlHelper = null;
        try {
            // 获取密码日志数据
            List<Map<String, Object>> passwordLog = getPasswordLog(workNo);

            // 数据验证
            if (CollUtil.isEmpty(passwordLog)) {
                log.info("addPasswordLog: no password log found for workNo: {}", workNo);
                return true; // 没有数据也算成功
            }

            sqlHelper = SQLHelper.createSqlHelper("KD_DataSwap_MSSQL");
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("DELETE FROM OgUser_WLRZ");
            if (StrUtil.isNotEmpty(workNo)) {
                sqlBuilder.append(" WHERE WorkNo='{0}'");
            }
            String delSql = sqlBuilder.toString();
            if (StrUtil.isNotEmpty(workNo)) {
                sqlHelper.executeNonQuery(SQLHelper.format(delSql, workNo));
            } else {
                sqlHelper.executeNonQuery(delSql);
            }
            String errorSql = null;
            String sql = "INSERT INTO OgUser_WLRZ (Id, WorkNo, UserName, DeptId, DeptName, OfficeName, OfficeId, PassWord_MD5) VALUES ('{0}', '{1}', '{2}', '{3}', '{4}', '{5}', '{6}', '{7}')";
            int successCount = 0;
            int totalCount = passwordLog.size();
            // 优化后的循环处理
            for (Map<String, Object> record : passwordLog) {
                try {
                    // 数据完整性检查
                    if (!isValidPasswordLogRecord(record)) {
                        log.warn("addPasswordLog: invalid record data, skipping: {} userID:{}", sql, record.get("UserID"));
                        continue;
                    }
                    errorSql = SQLHelper.format(sql,
                            record.get("UserID"),
                            record.get("WorkNo"),
                            record.get("Name"),
                            record.get("DeptID"),
                            record.get("DeptName"),
                            record.get("OfficeName"),
                            record.get("OfficeId"),
                            record.get("CurrentPasswordMD5"));
                    // 执行插入操作
                    int affectedRows = sqlHelper.executeNonQuery(SQLHelper.format(sql,
                            record.get("UserID"),
                            record.get("WorkNo"),
                            record.get("Name"),
                            record.get("DeptID"),
                            record.get("DeptName"),
                            record.get("OfficeName"),
                            record.get("OfficeId"),
                            record.get("CurrentPasswordMD5")
                    ));

                    if (affectedRows > 0) {
                        successCount++;
                    } else {
                        log.warn("addPasswordLog: insert failed for record: {}", record.get("UserID"));
                    }

                } catch (Exception e) {
                    log.error("addPasswordLog:error SQL{}:: {}", errorSql, e.getMessage());
                    // 继续处理其他记录，不中断整个流程
                }
            }
            log.info("addPasswordLog completed: {}/{} records processed successfully", successCount, totalCount);
            return successCount > 0; // 至少有一条记录成功才返回true
        } catch (Exception e) {
            log.error("addPasswordLog error for workNo: {}", workNo, e);
            return false;
        } finally {
            if (sqlHelper != null) {
                sqlHelper.close();
            }
        }
    }

    /**
     * 验证密码日志记录的数据完整性
     *
     * @param record 密码日志记录
     * @return 是否有效
     */
    private boolean isValidPasswordLogRecord(Map<String, Object> record) {
        if (record == null) {
            return false;
        }

        // 检查必要字段
        String[] requiredFields = {"UserID", "WorkNo", "CurrentPasswordMD5"};
        for (String field : requiredFields) {
            Object value = record.get(field);
            if (value == null || StrUtil.isEmpty(value.toString())) {
                log.warn("addPasswordLog: missing required field: {}字段数据为空", field);
                return false;
            }
        }

        return true;
    }
}
