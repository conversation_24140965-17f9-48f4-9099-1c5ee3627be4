package mh.cloud.module.system.controller.admin.logger.vo.searchLog;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

import java.time.LocalDateTime;

/**
 * @Author: ytq
 * @Date: 2025/3/14 14:36
 * @Description: TOOD 描述
 */

@Schema(description = "管理后台 - 搜索日志 Response VO")
@Data
public class SearchLogCreateReqVO {

    @Value("")
    private String id;
    //    搜索关键字
    private String searchKey;

    //    搜索类型
    private String moduleFlag;
    //    用户id
    private String userId;

    // 用户名
    private String userName;

    //    是否删除
    private String isDeleted;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    //    租户编号
    private String tenantId;
    // 备注
    private String memo;
}
