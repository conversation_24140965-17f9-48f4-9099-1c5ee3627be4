package mh.cloud.module.system.controller.admin.portal.V0;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;


@Data
@Schema(description = "文档实体")
public class SysDocument {
    @Schema(description = "文档Id")
    private String docId;
    @Schema(description = "文档名称")
    private String docName;
    @Schema(description = "文档内容")
    private String docContent;
    @Schema(description = "创建人")
    private String creator;
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    @Schema(description = "更新人")
    private String updater;
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
    @Schema(description = "备注")
    private String remark;
    private String routeName;

}
