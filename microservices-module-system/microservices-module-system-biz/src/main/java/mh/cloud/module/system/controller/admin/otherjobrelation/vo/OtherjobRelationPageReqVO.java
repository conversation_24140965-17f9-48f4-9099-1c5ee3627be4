package mh.cloud.module.system.controller.admin.otherjobrelation.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import mh.cloud.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static mh.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 兼职关系分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OtherjobRelationPageReqVO extends PageParam {

    @Schema(description = "groupid", example = "29745")
    private String parentGroupID;

    @Schema(description = "用户id", example = "23989")
    private String childGroupID;

    @Schema(description = "全路径id", example = "15065")
    private String deptFullID;

    @Schema(description = "关系类型", example = "1")
    private String relationType;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}