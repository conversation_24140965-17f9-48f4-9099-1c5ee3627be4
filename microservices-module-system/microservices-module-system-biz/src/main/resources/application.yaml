
spring:
  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true # 允许 Bean 覆盖，例如说 Feign 等会存在重复定义的服务
    login-error-count: 5 # 登录错误次数
    login-error-time: 300 # 登录错误时间间隔 单位秒
  # Servlet 配置
  servlet:
    # 文件上传相关配置项
    multipart:
      max-file-size: 16MB # 单个文件大小
      max-request-size: 32MB # 设置总上传的文件大小
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER # 解决 SpringFox 与 SpringBoot 2.6.x 不兼容的问题，参见 SpringFoxHandlerProviderBeanPostProcessor 类
  # Jackson 配置项
  jackson:
    serialization:
      write-dates-as-timestamps: true # 设置 LocalDateTime 的格式，使用时间戳
      write-date-timestamps-as-nanoseconds: false # 设置不使用 nanoseconds 的格式。例如说 **********.401，而是直接 **********401
      write-durations-as-timestamps: true # 设置 Duration 的格式，使用时间戳
      fail-on-empty-beans: false # 允许序列化无属性的 Bean
  # Cache 配置项
  cache:
    type: REDIS
    redis:
      time-to-live: 1h # 设置过期时间为 1 小时
  data:
    redis:
      host: 127.0.0.1 # 地址
      port: 6391 # 端口
      database: 0 # 数据库索引
      repositories:
       enabled: false # 项目未使用到 Spring Data Redis 的 Repository，所以直接禁用，保证启动速度
# Spring Data Redis 配置
management:
  health:
    redis:
      enabled: false # 禁用 Redis 的健康检查，保证启动速度
# VO 转换（数据翻译）相关
easy-trans:
  is-enable-global: true # 启用全局翻译（拦截所有 SpringMVC ResponseBody 进行自动翻译 )。如果对于性能要求很高可关闭此配置，或通过 @IgnoreTrans 忽略某个接口
  is-enable-cloud: false # 禁用 TransType.RPC 微服务模式

--- #################### 微服务相关配置 ####################
microservices:
  info:
    version: 1.0.0
    base-package: mh.cloud.module.system
  web:
    admin-ui:
      url: http://dashboard.microservices.iocoder.cn # Admin 管理后台 UI 的地址
  swagger:
    title: 管理后台
    description: 提供管理员管理的所有功能
    version: ${microservices.info.version}
    base-package: ${microservices.info.base-package}
  captcha:
    enable: false # 验证码的开关，默认为 true；
  tenant: # 多租户相关配置项
    enable: true
    ignore-urls:
      - /admin-api/auth/cursessionOA2
      - /admin-api/auth/cursessionOA3
      - /admin-api/auth/checkAccountGetToken
      - /admin-api/auth/checkQRcCode
      - /admin-api/Portal/Home/GetVerificationCode #发送验证码
      - /admin-api/Portal/Home/CheckVerificationCode #校验验证码
      - /admin-api/auth/writeToken
      - /admin-api/auth/login # 基于名字获取租户，不许带租户编号
      - /admin-api/auth/cursessionOA # 基于名字获取租户，不许带租户编号
      - /admin-api/system/token/weblogin
      - /admin-api/system/callback/QRCode #回调接口
      - /admin-api/system/auth/getQRcCodeState #获取二维码登录
      - /admin-api/system/auth/getQRcCode #获取二维码登录
      - /admin-api/BasicApplication/Common/getLoginImage # 基于名字获取租户，不许带租户编号
      - /admin-api/system/tenant/get-id-by-name # 基于名字获取租户，不许带租户编号
      - /admin-api/system/tenant/get-by-website # 基于域名获取租户，不许带租户编号
      - /admin-api/system/captcha/get-image # 获取图片验证码，和租户无关
      - /admin-api/system/captcha/get # 获取图片验证码，和租户无关
      - /admin-api/system/captcha/check # 校验图片验证码，和租户无关
      - /admin-api/system/sms/callback/* # 短信回调接口，无法带上租户编号
      - /admin-api/system/auth/getUserInfo #修改密码时获取用户信息
      - /admin-api/system/auth/update-password #修改密码
      - /rpc-api/system/tenant/valid # 防止递归。避免调用 /rpc-api/system/tenant/valid 接口时，又去触发 /rpc-api/system/tenant/valid 去校验
      - /rpc-api/system/tenant/id-list # 获得租户列表的时候，无需传递租户编号
      - /rpc-api/system/oauth2/token/check # 访问令牌校验时，无需传递租户编号；主要解决上传文件的场景，前端不会传递 tenant-id！
      - /admin-api/auth/updateDjtUserLoginInfo # 电建通扫码登录回调
      - /admin-api/system/auth/get_uuid #扫码登录 - 创建uuid
      - /admin-api/system/auth/check_login_status #扫码登录 - 检查登录状态
      - /admin-api/system/KHIDIService/SysLog/PageLog # 日志接口，无需带租户编号
      - /admin-api/system/KHIDIService/SysLog/FuncLog # 日志接口，无需带租户编号
      - /admin-api/auth/getTokenById
      - /admin-api/infra/file/*/get/** # 获取图片，和租户无关
      - /admin-api/downFile/**
      - /admin-api/business/crawler/**
      - /admin-api/quartz/job-log/**
      - /admin-api/auth2/sysauthbytoken
    ignore-tables:
      - system_tenant
      - system_tenant_package
      - system_dict_data
      - system_dict_type
      - system_error_code
      - system_menu
      - system_sms_channel
      - system_sms_template
      - system_sms_log
      - system_sensitive_word
      - system_oauth2_client
      - system_mail_account
      - system_mail_template
      - system_mail_log
      - system_notify_template
      - system_layout_group
      - system_job
      - system_job_log
      - system_job_api
      - C_NewTimerTask
      - C_NewTimerTask_ApiSQL
      - C_NewTimerTask_ApiUrl
  sms-code: # 短信验证码相关的配置项
    expire-times: 10m
    send-frequency: 1m
    send-maximum-quantity-per-day: 10
    begin-code: 9999 # 这里配置 9999 的原因是，测试方便。
    end-code: 9999 # 这里配置 9999 的原因是，测试方便。
server:
  tongweb:
    license:
      path=classpath: license.dat
com:
  tongtech:
    dino:
      client:
        enabled: true
        url: http://localhost:8083/dino
        username: admin
        password: <EMAIL>
#电建外网新闻列表
crawler:
  djGroupNews: https://www.powerchina.cn/col/col7440/index.html
  djCurPolitNews: https://www.powerchina.cn/col/col7458/index.html
  djStateFundTrends: https://www.powerchina.cn/col/col7457/index.html
