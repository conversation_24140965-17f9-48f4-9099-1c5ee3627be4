<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mh.cloud.module.system.dal.mysql.ares.AResMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <select id="selectResTree" resultType="mh.cloud.module.system.dal.dataobject.ares.AResDO">
        SELECT
            ar.*
        FROM
            "A_Res" ar
                LEFT JOIN
            "A_GroupRes" agr ON ar."ParentID"  = agr."GroupID"
        <where>
            ar."IsDeleted" =0
        <if test="id != null and id != ''">
            and ar."FullID" Like  CONCAT('%', #{id}, '%')
        </if>
        <if test="name != null and name != ''">
        and ar."Name" Like  CONCAT('%', #{name}, '%')
        </if>
        <if test="roleIds != null and roleIds.size() > 0">
         AND agr."GroupID" IN
            <foreach collection="roleIds" item="roleId" open="(" close=")" separator=",">
                #{roleId}
            </foreach>
        </if>
        </where>
        ORDER BY ar."SortIndex" ASC
    </select>

    <select id="getAResList2" resultType="mh.cloud.module.system.dal.dataobject.ares.AResDO">
        select ar."ID",ar."ParentID",ar."Name" from  "A_Res" ar
        <where>
            "IsDeleted" =0
        <if test="name != null and name != ''">
            and ar."Name" Like  CONCAT('%', #{name}, '%')
        </if>
        </where>
        ORDER BY ar."SortIndex" ASC
    </select>
</mapper>