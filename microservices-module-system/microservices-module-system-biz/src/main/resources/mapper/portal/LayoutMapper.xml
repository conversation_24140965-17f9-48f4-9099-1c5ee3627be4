<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mh.cloud.module.system.dal.mysql.portal.LayoutMapper">


<!--    <select id="getLayout" resultType="java.util.Map">-->
<!--        SELECT row_number() over () as i,sl.id, sld.id dataId,sl.data_id, sl.user_id, sl.layout,sl.start_use, sl.create_time, sl.update_time,-->
<!--               sld.name, sld.sign, sld.content,-->
<!--               sld.tenant_id, sld.is_delete,-->
<!--               sld.icon, sld.category, sld.itemId-->
<!--        FROM system_layout_data sld LEFT JOIN system_layout sl ON sl.data_id  = sld .id AND sl.user_id = #{userId}-->
<!--        where sld."category" = #{category} AND sld.is_delete = '0'-->
<!--    </select>-->

    <select id="getLayoutById" resultType="java.util.Map">
        SELECT sl.id, sld.id dataId,sl.data_id, sl.user_id, sl.layout,sl.start_use, sl.create_time, sl.update_time,
               sld.name, sld.sign, sld.content,
               sld.tenant_id, sld.is_delete,
               sld.icon, sld.category, sld.itemId
        FROM system_layout_data sld LEFT JOIN system_layout sl ON sl.data_id  = sld .id AND sl.user_id = #{userId}
        where sld."category" = #{category} and sl.id = #{id} and sld.is_delete = '0'
    </select>

    <select id="getDefaultLayout" resultType="java.util.Map">
        SELECT row_number() over () as i,
        sl."id" as id,
        sl.user_id,
        sld.id as dataId,
        sld.name,
        sld.sign,
        sld.content,
        sld.is_delete,
        sld.icon,
        sld.category,
        sld.itemId,
        slg.group_code,
        slg.default_layout as layout,
        1 AS startUse
        FROM system_layout_data sld JOIN system_layout_group slg ON sld."id" = slg."data_id"
        LEFT JOIN "system_layout" sl ON sl."data_id" = sld."id"
        <where>
            sld."is_delete" = 0 and slg.group_code = #{groupCode}
            <if test="category != null and category != ''">
                and sld."category" = #{category}
            </if>
        </where>
    </select>


    <select id="getGroupLayoutList" resultType="java.util.Map">
        SELECT
        row_number() over () as i,
        sld.id dataId,
        sld.name,
        sld.sign,
        sld.content,
        sld.tenant_id,
        sld.is_delete,
        sld.icon,
        sld.category,
        sld.itemId,
        slg."id" id,
        slg."group_code",
        slg."default_layout" as layout,
        CASE
        WHEN slg."id" IS NULL THEN 0
        ELSE 1
        END AS startUse
        FROM system_layout_data sld
        LEFT JOIN system_layout_group slg
        ON sld."id" = slg."data_id"
        <where>
            sld."is_delete" = 0
            <if test="category != null and category != '' and category != 'allCenter'">
                and sld."category" = #{category}
            </if>
            <if test="groupCode != null and groupCode != ''">
                and slg."group_code" = #{groupCode}
            </if>
        </where>
    </select>
    <select id="getLayout" resultType="java.util.Map">
        SELECT row_number() over () as i,
        sl."id" as id,
        sl."layout" as layout,
        sl.user_id,
        sld.id as dataId,
        sld.name,
        sld.sign,
        sld.content,
        sld.is_delete,
        sld.icon,
        sld.category,
        sld.itemId,
        slg.group_code,
        slg.default_layout,
        CASE
        WHEN sl."id" IS NULL THEN 0
        ELSE 1
        END AS startUse
        FROM system_layout_data sld JOIN system_layout_group slg ON sld."id" = slg."data_id"
        LEFT JOIN "system_layout" sl ON sl."data_id" = sld."id" and sl.user_id = #{userId}
        <where>
            sld."is_delete" = 0 and slg.group_code = #{groupCode}
            <if test="category != null and category != ''">
                and sld."category" = #{category}
            </if>
        </where>
    </select>
</mapper>
