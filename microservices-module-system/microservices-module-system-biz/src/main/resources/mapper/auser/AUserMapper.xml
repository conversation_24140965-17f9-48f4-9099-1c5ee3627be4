<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mh.cloud.module.system.dal.mysql.auser.AUserMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="selectCustomPage" resultType="mh.cloud.module.system.dal.dataobject.auser.AUserDoChild">
        select * from (
                          select
                              distinct u.*,
                                       g.id as gid,
                                       g.DeptUnit,
                                       g.DeptUnitName
                          from
                              dbo.A_Group g
                                  left join dbo.A_GroupRelation a on
                                  a.ParentGroupID = g.ID
                                  left join dbo.A_User u on
                                  u.ID = a.ChildGroupID
                          where
                              u.ID is not null
                                and u.IsDeleted='0'
                                and g.IsDeleted='0'
                          )a
            ${ew.customSqlSegment}
            <if test="groupIds != null and groupIds != ''">
            AND gid IN (${groupIds})
            </if>
            ORDER BY SortIndex ASC
    </select>
    <select id="getAvatarByUserId">
        SELECT HeadPortraitFile FROM  "A_UserExts" WHERE "UserID" = #{userId}
    </select>
</mapper>
